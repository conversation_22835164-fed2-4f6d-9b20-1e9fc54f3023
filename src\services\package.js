const moment = require('moment');
const callApi = require('../utils/callApi');
const logger = require('../utils/logger');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { PAYMENT_URL } = require('../configs');
const {
  PACKAGE_CODE,
  PACKAGE_VERSION,
  PACKAGE_TYPE,
  FREE_PACKAGE_CODES,
} = require('../constants');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const {
  MAPPING_STUDIO_PACKAGE_CODES,
  PACKAGE_LEVEL,
} = require('../constants/package');
const { DEFAULT_MAX_VOICE_CLONING } = require('../constants/voiceCloning');

const MAX_RETRIES = 3;
const RETRY_DELAY = 3000; // ms
const DETAIL_PROPERTIES = [
  'concurrentRequest',
  'features',
  'download',
  'maxLengthInputText',
  'maxLengthDemoInput',
  'code',
  'retentionPeriod',
  'maxPreview',
  'device',
  'maxCloningVoices',
  'maxInstantVoiceSynthesis',
];

// ADVISE: use Caching to store those list
/** a list of FREE packages, fetch from remote service adapter */
const FREE_PACKAGES = {};
/** a list of packages, fetch from remote service adapter */
let PACKAGES = [];

const getFreeStudioPackageCode = () => PACKAGE_CODE.STUDIO_FREE_V3;

const getFreePackage = async (user, ip) => {
  const freeStudioPackageCode = getFreeStudioPackageCode(user, ip);

  // Check if the package is already fetched
  if (FREE_PACKAGES?.[freeStudioPackageCode])
    return FREE_PACKAGES[freeStudioPackageCode];

  try {
    // TODO: reduce `method "GET"`
    const { status, result } = await callApi({
      url: `${PAYMENT_URL}/api/v1/packages`,
      method: 'GET',
      params: { code: freeStudioPackageCode },
    });

    // ADVISE: can we throw error right in callApi()? it seems we already did logging/reject in callApi
    if (status !== 1) throw new Error();
    // Cache the result
    FREE_PACKAGES[freeStudioPackageCode] = result?.packages?.[0];

    return result?.packages?.[0];
  } catch (error) {
    throw new CustomError(errorCodes.INTERNAL_SERVER_ERROR, error.message);
  }
};

const getBonusCredits = async (user, ip) => {
  const freePackage = await getFreePackage(user, ip);

  if (!freePackage) return 0;
  if (!freePackage?.freemiumConfig) return freePackage?.bonusCharacters || 0;

  const {
    introductoryPeriodDays,
    introductoryDailyCredits,
    regularDailyCredits,
  } = freePackage.freemiumConfig || {};
  const usedDays = moment().diff(moment(user.createdAt).startOf('day'), 'days');

  if (usedDays < introductoryPeriodDays) return introductoryDailyCredits;
  return regularDailyCredits;
};

const checkOldPackage = (packageCode) => {
  // Package code may be not provided (e.g., user not yet created in db)
  if (!packageCode) return false;

  const oldVersions = new Set([
    PACKAGE_VERSION.V1,
    PACKAGE_VERSION.V2,
    PACKAGE_VERSION.V2_1,
  ]);
  const parts = packageCode.split('-V');

  // If there is no `-V`, it is V1 → Old package
  if (parts.length === 1) return true;

  return oldVersions.has(`V${parts[1]}`);
};

const checkFreePackage = (packageCode) =>
  packageCode && FREE_PACKAGE_CODES.includes(packageCode);

// ADVISE: implement PackageAdapter (internal microservice)
/** retry to get all package from remote msvc  */
const getPackages = async (retries = 0) => {
  try {
    const fields = DETAIL_PROPERTIES.join(',');
    const { status, result } = await callApi({
      method: 'GET',
      url: `${PAYMENT_URL}/api/v1/packages`,
      params: { fields },
    });

    if (!status) throw new Error();

    return result.packages;
  } catch (error) {
    if (retries < MAX_RETRIES) {
      logger.warn(
        `Retry ${retries + 1}/${MAX_RETRIES} failed. Retry will start in ${
          RETRY_DELAY / 1000
        } seconds...`,
        { ctx: 'GetPackages' },
      );

      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(getPackages(retries + 1));
        }, RETRY_DELAY);
      });
    }

    logger.error(
      'Failed to get packages after maximum retries. Returning empty array.',
      { ctx: 'GetPackages' },
    );
    return [];
  }
};

/** get all packages from remote msvc, cached into PACKAGES */
const fetchAllPackages = async () => {
  if (PACKAGES.length !== 0) return PACKAGES;
  // ADVISE: the problem of current code is it fetch and cached ONCE, no update or refresh. Should use cache manager to store packages
  const packages = await getPackages();
  PACKAGES = packages;

  return packages;
};

/** get package DTO by code */
const getPackage = async (packageCode) => {
  const allPackages = await fetchAllPackages();
  return allPackages.find((item) => item.code === packageCode);
};

const initPackages = async () => {
  const packages = await getPackages();
  PACKAGES = packages;
};

const mappingPackageCodeV3 = (oldPackageCode) => {
  if (!oldPackageCode) return '';
  return MAPPING_STUDIO_PACKAGE_CODES[oldPackageCode] || oldPackageCode;
};

const getUsageOptions = async (packageCode, userId) => {
  const userInVoiceCloningFeatureFlag = getFeatureValue(
    FEATURE_KEYS.VOICE_CLONING,
    { userId },
  );

  const packageDetail = await getPackage(packageCode);
  if (!packageDetail) return null;

  const filteredDetail = DETAIL_PROPERTIES.reduce((acc, key) => {
    if (key in packageDetail) acc[key] = packageDetail[key];
    if (
      userInVoiceCloningFeatureFlag &&
      key === 'maxCloningVoices' &&
      key in packageDetail
    )
      acc[key] =
        packageDetail[key] > 0 ? packageDetail[key] : DEFAULT_MAX_VOICE_CLONING;
    return acc;
  }, {});

  return filteredDetail;
};

const PACKAGE_MAPPING_FIELDS = ['features'];

const getPackageUsageOptions = async ({
  userId,
  packageCode,
  userUsageOptions,
}) => {
  const useUsageOptions = getFeatureValue(
    FEATURE_KEYS.USE_USAGE_OPTIONS_IN_PACKAGE,
    { userId, packageCode },
  );

  packageCode = mappingPackageCodeV3(packageCode);
  const usageOptions = await getUsageOptions(packageCode, userId);
  if (useUsageOptions) return usageOptions;

  const filteredUsageOptions = PACKAGE_MAPPING_FIELDS.reduce((acc, key) => {
    if (key in usageOptions) acc[key] = usageOptions[key];
    return acc;
  }, {});

  return { ...userUsageOptions, ...filteredUsageOptions };
};

const checkCustomPackage = (packageCode) => {
  if (!packageCode) return false;
  return (
    packageCode.includes(PACKAGE_TYPE.STUDIO) &&
    packageCode.includes(PACKAGE_LEVEL.CUSTOM)
  );
};

module.exports = {
  getBonusCredits,
  checkOldPackage,
  checkFreePackage,
  checkCustomPackage,

  getPackageUsageOptions,

  initPackages,
};
