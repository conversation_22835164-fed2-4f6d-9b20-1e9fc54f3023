const blackWordDao = require('../daos/blackWord');
const blackWordService = require('../services/blackWord');

const getBlackWords = async (req, res) => {
  const { search, offset, limit, sort } = req.query;

  const { blackWords, total } = await blackWordDao.findBlackWords({
    search,
    offset,
    limit,
    sort: sort?.split(','),
  });

  return res.send({ blackWords, total });
};

const createBlackWord = async (req, res) => {
  const { word } = req.body;

  const blackWord = await blackWordService.createBlackWord(word);

  return res.send({ blackWord });
};

const editBlackWord = async (req, res) => {
  const { blackWordId } = req.params;
  const { word } = req.body;

  const blackWord = await blackWordService.updateBlackWord(blackWordId, word);

  return res.send({ blackWord });
};

const deleteBlackWord = async (req, res) => {
  const { blackWordId } = req.params;

  const blackWord = await blackWordService.deleteBlackWord(blackWordId);

  return res.send({ blackWord });
};

module.exports = {
  getBlackWords,
  createBlackWord,
  editBlackWord,
  deleteBlackWord,
};
