require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const updateStatusActive = async () => {
  try {
    await Voice.updateMany({}, { active: true });
    const voices = await Voice.find({});
    global.VOICES = voices;
  } catch (error) {
    logger.error('Update status active to voice failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update status active for voice...`, {
    ctx: 'RunScript',
  });
  await updateStatusActive();
  logger.info(`Update status active for voice successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
