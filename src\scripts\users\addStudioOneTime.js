require('dotenv').config();
const fs = require('fs');
require('../../models');
const User = require('../../models/user');
const logger = require('../../utils/logger');
const { PACKAGE_HAS_EOL } = require('../../constants/package');

global.logger = logger;

const IMPORT_FILE_PATH = 'old_studio_pkg_users.csv';
const BATCH_SIZE = 100;

const processUsers = async (users) => {
  const bulkOps = users.map((user) => {
    const { userId, packageCode, packageExpiryDate } = user;

    const eolDate = PACKAGE_HAS_EOL.includes(packageCode)
      ? new Date(
          Math.max(
            new Date(packageExpiryDate).getTime(),
            new Date('2025-05-19T16:59:59.999Z').getTime(),
          ),
        )
      : undefined;
    return {
      updateOne: {
        filter: { _id: userId },
        update: {
          $set: {
            'studio.oneTime.packageCode': packageCode,
            'studio.oneTime.eolDate': eolDate,
          },
        },
      },
    };
  });
  await User.bulkWrite(bulkOps);
};

const main = async () => {
  // Read users
  const users = fs
    .readFileSync(IMPORT_FILE_PATH, 'utf8')
    .split('\n')
    .slice(1)
    .filter((line) => line.trim())
    .map((line) => {
      const [userId, packageCode, packageExpiryDate] = line.split(',');
      return { userId, packageCode, packageExpiryDate };
    });

  // Process users in batches
  for (let i = 0; i < users.length; i += BATCH_SIZE) {
    const batch = users.slice(i, i + BATCH_SIZE);
    await processUsers(batch);

    logger.info(
      `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(
        users.length / BATCH_SIZE,
      )}`,
    );
  }
};

main()
  .then(() => {
    logger.info('Done');
  })
  .catch((err) => {
    logger.error(err);
  });
