const omitIsNil = (obj, { deep = false }) => {
  if (typeof obj !== 'object' || !obj) return {};

  Object.keys(obj).forEach((key) => {
    if (obj[key] === undefined || obj[key] === null) {
      delete obj[key];
    }
  });

  if (deep) {
    Object.keys(obj).forEach((key) => {
      if (typeof obj[key] === 'object') {
        // ADVISE: fix this. Currently this func've never ever worked with the "deep" case
        omitIsNil(obj[key], true);
      }
    });
  }

  return obj;
};

module.exports = {
  omitIsNil,
};
