const { CLOUD_RUN_JOIN_URL } = require('../../configs');
const callApiToCloudRun = require('./invoke');

const joinAudio = async ({ audios, outputType, sampleRate, bitRate }) => {
  try {
    const payload = {
      invoke_type: 'just_join',
      sentences: audios.map((audio) => ({
        type: 'text',
        link: audio,
      })),
      output_type: outputType,
      sample_rate: sampleRate,
      bit_rate: bitRate,
    };

    const response = await callApiToCloudRun(CLOUD_RUN_JOIN_URL, payload);

    const { result } = response || {};
    return result;
  } catch (error) {
    logger.error(error?.message, { ctx: 'JoinAudioWithCloudRun' });
    return null;
  }
};

module.exports = { joinAudio };
