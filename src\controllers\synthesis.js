const synthesisService = require('../services/synthesis');
const { normalizeBitrate } = require('../utils/bitrate');

const synthesize = async (req, res) => {
  const { device, deviceInfo } = req;
  const { publicIP: ip } = req.__clientInfo;
  const {
    title,
    text,
    paragraphs,
    voiceCode,
    sentences,
    audioType,
    backgroundMusic,
    speed,
    bitrate,
    volume,
    version,
    datasenses,
    projectId,
    blockId,
  } = req.body;
  const { userId, email } = req.user;
  const request = await synthesisService.handleSynthesisRequest({
    ip,
    device,
    deviceInfo,
    title,
    text,
    paragraphs,
    voiceCode,
    sentences,
    audioType,
    backgroundMusic,
    speed,
    userId,
    email,
    bitrate: normalizeBitrate(bitrate),
    volume,
    version,
    datasenses,
    projectId,
    blockId,
  });
  return res.send(request);
};

module.exports = { synthesize };
