const moment = require('moment');
const { sendSlackNotification } = require('.');
const { RECAPTCHA_ERROR_SLACK_CHANNEL } = require('../../configs');

const sendRecaptchaErrorToSlackNotification = async ({
  message,
  userId,
  email,
  platform,
  recaptchaVersion,
}) => {
  const messages = [
    '>*:red_circle: Lỗi recaptcha khi chuyển văn bản*',
    `\n>*UserId:* ${userId} ${email ? `(${email})` : ''}`,
    `*Thời gian:* ${moment().format('DD-MM-YYYY HH:mm')}`,
    `*Phiên bản recaptcha:* ${recaptchaVersion}`,
    `*Nền tảng:* ${platform}`,
    `*Chi tiết:* ${message}`,
  ];

  const blockMessages = [
    {
      type: 'section',
      text: { type: 'mrkdwn', text: messages.join(' \n> ') },
    },
  ];

  await sendSlackNotification({
    channel: RECAPTCHA_ERROR_SLACK_CHANNEL,
    blocks: blockMessages,
    text: 'Lỗi recaptcha khi chuyển văn bản',
  });
};

module.exports = { sendRecaptchaErrorToSlackNotification };
