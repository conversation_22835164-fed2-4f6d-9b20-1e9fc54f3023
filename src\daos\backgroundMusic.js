const daoUtils = require('./utils');
const BackgroundMusic = require('../models/backgroundMusic');

const createBackgroundMusic = async (createFields) => {
  const backgroundMusic = await BackgroundMusic.create(createFields);
  return backgroundMusic;
};

const findBackgroundMusics = async ({
  search,
  query,
  offset,
  limit,
  fields,
  sort = ['createdAt_desc'],
}) => {
  let queryFields = query;
  if (query && query.userId) {
    queryFields = {
      $or: [{ userId: { $exists: false } }, { userId: query.userId }],
    };
  }

  const { documents: backgroundMusics, total } = await daoUtils.find(
    BackgroundMusic,
    {
      search,
      searchFields: ['name'],
      query: queryFields,
      offset,
      limit,
      fields,
      sort,
    },
  );

  return { backgroundMusics, total };
};

module.exports = { createBackgroundMusic, findBackgroundMusics };
