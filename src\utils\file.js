const iconv = require('iconv-lite');
const chardet = require('chardet');
const https = require('https');
const CustomError = require('../errors/CustomError');

// ADVISE: The usage of this only read text files. We can use @gurucore/lakdak/RawNetworkHelper.download() with might have more sophisticated logic
const readFileFromLink = async (link) => {
  try {
    const response = await new Promise((res) => https.get(link, res));
    if (response.statusCode !== 200) {
      throw new Error(
        `Error: ${response.statusCode} ${response.statusMessage}`,
      );
    }

    const chunks = [];

    response.on('data', (chunk) => {
      chunks.push(chunk);
    });

    return new Promise((resolve, reject) => {
      response.on('end', () => {
        const buffer = Buffer.concat(chunks);

        // ADVISE: if we assume the encoding is UTF-8 (in existing comment below), we don't need to detect encoding
        // Detect the encoding
        const detectedEncoding = chardet.detect(buffer);
        // ADVISE: should fallback detectedEncoding to UTF-8
        // Convert to UTF-8
        const utf8Content = iconv.decode(buffer, detectedEncoding);

        // The \r character appears when downloading files, need remove
        resolve(utf8Content.replace(/\r/g, ''));
      });

      response.on('error', (error) => {
        reject(error);
      });
    });
  } catch (error) {
    throw new CustomError(error, { ctx: 'ReadFileFromLink', link });
  }
};

module.exports = { readFileFromLink };
