const { TextHelper } = require('@vbee-holding/vbee-node-shared-lib');
const { convertTimeDeltaToMillis, isValidTimeDelta } = require('./time');

// ADVISE: extract to shared-lib dubbing
/**
 * parse SRT content
 * @param {String} subtitleContent srt file content as text
 * @returns {Array} parsed subtitles in format [{ id, start, end, content }]
 */
const parseSRT = (subtitleContent) => {
  if (!subtitleContent)
    return { error: true, message: 'File content is empty' };

  const srtText = subtitleContent.trim();
  const subtitleBlocks = srtText.split(/\n\s*\n/);
  const parsedSubtitles = [];

  try {
    for (let i = 0; i < subtitleBlocks.length; i += 1) {
      const block = subtitleBlocks[i];
      const [id, time, ...content] = block.split('\n');
      if (Number.isNaN(Number(id)) || !time || !content)
        throw new Error('Invalid srt format');

      const [start, end] = time
        .split('-->')
        .map((t) => TextHelper.removeSpecialCharacters(t).trim());
      const isValidTimeDeltas = [start, end].every(isValidTimeDelta);
      if (!isValidTimeDeltas) throw new Error('Invalid time delta format');

      const startTime = convertTimeDeltaToMillis(start);
      const endTime = convertTimeDeltaToMillis(end);
      if (startTime > endTime)
        throw new Error('Start time must be less than end time in srt format');

      const lastSubtitle = parsedSubtitles[parsedSubtitles.length - 1];
      if (lastSubtitle) {
        const lastStartTime = convertTimeDeltaToMillis(lastSubtitle.start);
        if (startTime < lastStartTime)
          throw new Error('Audio timestamp must be in right order');
      }

      parsedSubtitles.push({
        id,
        start,
        end,
        content: TextHelper.removeHtmlTags(content.join(' ')),
      });
    }

    return { error: false, parsedSubtitles };
  } catch (error) {
    return { error: true, message: error.message };
  }
};

const getSRTDuration = (parsedSubtitles) => {
  if (!parsedSubtitles) return 0;

  const duration = parsedSubtitles.reduce((acc, subtitle) => {
    const { start, end } = subtitle;
    const startTime = convertTimeDeltaToMillis(start);
    const endTime = convertTimeDeltaToMillis(end);

    return Math.max(startTime, endTime, acc);
  }, 0);

  return duration;
};

module.exports = { parseSRT, getSRTDuration };
