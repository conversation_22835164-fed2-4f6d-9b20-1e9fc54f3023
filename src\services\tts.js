const { SYNTHESIS_BY_GATEWAY, TTS_GATE_URL } = require('../configs');
const { MAX_TTS_PRESET } = require('../constants/ttsPreset');
const { findTts } = require('../daos/tts');
const ttsPresetDao = require('../daos/ttsPreset');
const voiceCloningDao = require('../daos/voiceCloning');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const callApi = require('../utils/callApi');

const getTts = async ({
  search,
  searchFields,
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
  requestId,
}) => {
  let res;

  if (SYNTHESIS_BY_GATEWAY) {
    // ADVISE: create adapters/ttsGate, and call TTSGateAdapter.getTts()
    const ttsRes = await callApi({
      url: `${TTS_GATE_URL}/api/v1/tts`,
      method: 'GET',
      params: {
        search,
        searchFields,
        dateField,
        query,
        offset,
        limit,
        fields,
        sort,
        requestId,
      },
    });
    res = ttsRes.result || {};
  } else
    res = await findTts({
      search,
      searchFields,
      dateField,
      query,
      offset,
      limit,
      fields,
      sort,
    });

  const { tts, total } = res;
  if (!tts?.length) return { tts: [], total: 0 };

  return { tts, total };
};

const assignVoiceToTtsPreset = async (ttsPreset) => {
  const voiceInGlobal = VOICES.find(
    (voice) => voice.code === ttsPreset.voiceCode,
  );
  if (voiceInGlobal) return { ...ttsPreset, voice: voiceInGlobal };

  const clonedVoice = await voiceCloningDao.findVoiceCloningByCode(
    ttsPreset.voiceCode,
  );
  return { ...ttsPreset, voice: clonedVoice };
};

const validateTtsPresetName = async ({ userId, name, excludeId }) => {
  const existingTtsPreset = await ttsPresetDao.getTtsPresetByName({
    userId,
    name,
    excludeId,
  });
  if (existingTtsPreset)
    throw new CustomError(
      errorCodes.TTS_PRESET_NAME_ALREADY_EXISTS,
      'TTS preset name already exists',
    );
};

const getTtsPresets = async (userId) => {
  const { ttsPresets, total } = await ttsPresetDao.getTtsPresets(userId);
  const ttsPresetsWithVoice = await Promise.all(
    ttsPresets.map(assignVoiceToTtsPreset),
  );
  return { ttsPresets: ttsPresetsWithVoice, total };
};

const createTtsPreset = async (
  userId,
  { name, audioType, backgroundMusic, speed, voiceCode, clientPause },
) => {
  const createFields = {
    userId,
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  };
  const count = await ttsPresetDao.countTtsPresetByUserId(userId);
  if (count >= MAX_TTS_PRESET)
    throw new CustomError(
      errorCodes.TTS_PRESET_LIMIT_REACHED,
      'TTS preset limit reached',
    );

  await validateTtsPresetName({ userId, name });

  const ttsPreset = await ttsPresetDao.createTtsPreset(createFields);
  return ttsPreset;
};

const getTtsPresetById = async (userId, ttsPresetId) => {
  const ttsPreset = await ttsPresetDao.getTtsPresetById(userId, ttsPresetId);
  const ttsPresetWithVoice = await assignVoiceToTtsPreset(ttsPreset);
  return ttsPresetWithVoice;
};

const updateTtsPreset = async (userId, ttsPresetId, updateFields) => {
  const { name } = updateFields;

  await validateTtsPresetName({ userId, name, excludeId: ttsPresetId });

  const ttsPreset = await ttsPresetDao.updateTtsPreset({
    userId,
    ttsPresetId,
    updateFields,
  });
  return ttsPreset;
};

const deleteTtsPreset = async (userId, ttsPresetId) => {
  await ttsPresetDao.deleteTtsPreset(userId, ttsPresetId);
};

module.exports = {
  getTts,
  getTtsPresets,
  getTtsPresetById,
  createTtsPreset,
  updateTtsPreset,
  deleteTtsPreset,
};
