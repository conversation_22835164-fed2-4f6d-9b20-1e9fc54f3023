const MigratedDubbingUser = require('../models/migratedDubbingToStudioUsers');

const findMigratedDubbingUser = async (condition) => {
  const user = await MigratedDubbingUser.findOne(condition);
  return user;
};

const updateMigratedDubbingUser = async (condition, updateFields) => {
  const user = await MigratedDubbingUser.findOneAndUpdate(
    condition,
    updateFields,
    {
      new: true,
      upsert: true,
    },
  );
  return user;
};

module.exports = { findMigratedDubbingUser, updateMigratedDubbingUser };
