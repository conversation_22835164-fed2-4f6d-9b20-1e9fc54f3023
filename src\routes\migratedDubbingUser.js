const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const migratedDubbingToStudioUsers = require('../controllers/migratedDubbingUser');
const { auth } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.put('/migrated-dubbing-user', auth, asyncMiddleware(migratedDubbingToStudioUsers.agreeToMigrate));
/* eslint-disable prettier/prettier */

module.exports = router;
