const BlackWord = require('../models/blackWord');
const daoUtils = require('./utils');

const getAllBlackWords = async () => {
  const blackWords = await BlackWord.find({}).lean();

  return blackWords;
};

const findBlackWord = async (condition) => {
  const blackWord = await daoUtils.findOne(BlackWord, condition);
  return blackWord;
};

const findBlackWords = async ({ search, offset, limit, sort }) => {
  const { documents: blackWords, total } = await daoUtils.find(BlackWord, {
    search,
    searchFields: ['word'],
    limit,
    offset,
    sort,
  });

  return { blackWords, total };
};

const createBlackWord = async (word) => {
  let blackWord = await BlackWord.create({ word });
  blackWord = blackWord.toJSON();
  return blackWord;
};

const updateBlackWord = async (blackWordId, updateFields) => {
  const blackWord = await BlackWord.findByIdAndUpdate(
    blackWordId,
    updateFields,
    {
      new: true,
    },
  ).lean();
  return blackWord;
};

const deleteBlackWord = async (blackWordId) => {
  const blackWord = await BlackWord.findByIdAndDelete(blackWordId);
  return blackWord;
};

module.exports = {
  getAllBlackWords,
  findBlackWord,
  findBlackWords,
  createBlackWord,
  updateBlackWord,
  deleteBlackWord,
};
