/* eslint-disable no-plusplus */
/* eslint-disable no-shadow */
require('dotenv').config();
const moment = require('moment');
const { initMongoDB } = require('../../models');
const {
  REQUEST_TYPE,
  REQUEST_STATUS,
  FREE_PACKAGE_CODES,
} = require('../../constants');
const logger = require('../../utils/logger');
const { VOICE_OWNERSHIP } = require('../../constants/voice');
const voiceCloningDao = require('../../daos/voiceCloning');
const { scanAndHandleByBatch } = require('../../services/batcher');
const requestDao = require('../../daos/request');
const Request = require('../../models/request');

initMongoDB();

global.logger = logger;
global.LANGUAGES = null;

const getAllCommunityVoiceCodes = async () => {
  const { voices } = await voiceCloningDao.findVoiceCloningByOwnerShip({
    voiceOwnership: VOICE_OWNERSHIP.COMMUNITY,
  });
  return voices.map((voice) => voice.code);
};

const updateMonetizable = async (date) => {
  const startDate = moment(date).startOf('day').toDate();
  const endDate = moment(date).endOf('day').toDate();
  const BATCH_SIZE = 1000;

  logger.info(`Start updating monetizable requests from ${date}`, {
    ctx: 'updateMonetizable',
  });

  const communityVoiceCodes = await getAllCommunityVoiceCodes();
  logger.info(`Loaded ${communityVoiceCodes.length} community voice codes`, {
    ctx: 'updateMonetizable',
  });

  const getRequestsQuery = ({ dateRange, dateField, excludeDocIds = [] }) => ({
    _id: { $nin: excludeDocIds },
    [dateField]: { $gte: dateRange.start, $lte: dateRange.end },
    type: REQUEST_TYPE.STUDIO,
    status: REQUEST_STATUS.SUCCESS,
  });

  let totalUpdated = 0;
  let totalScanned = 0;

  const handleUpdateMonetizable = async (docs, batchIndex) => {
    const bulkOps = [];

    logger.info(
      `Processing batch #${batchIndex} with ${docs.length} requests`,
      {
        ctx: 'updateMonetizable',
      },
    );

    for (const doc of docs) {
      let isMonetizable = false;

      const isPaidPackage = !FREE_PACKAGE_CODES.includes(doc.packageCode);
      const isMainVoiceValid = communityVoiceCodes.includes(doc.voiceCode);

      const updatedSentences = (doc.sentences || []).map((s) => {
        const isSentenceValid = communityVoiceCodes.includes(s.voiceCode);
        if (isPaidPackage && isSentenceValid) {
          isMonetizable = true;
          return {
            ...s,
            isMonetizable: true,
            credits: s.characters || 0,
          };
        }
        return s;
      });

      if (isPaidPackage && isMainVoiceValid) isMonetizable = true;

      if (isMonetizable)
        bulkOps.push({
          updateOne: {
            filter: { _id: doc._id },
            update: {
              $set: {
                isMonetizable: true,
                sentences: updatedSentences,
              },
            },
          },
        });
    }

    totalScanned += docs.length;
    totalUpdated += bulkOps.length;

    logger.info(
      `Batch #${batchIndex}: ${bulkOps.length}/${docs.length} requests marked monetizable`,
      { ctx: 'updateMonetizable' },
    );

    if (bulkOps.length) await Request.bulkWrite(bulkOps);
  };

  let batchCounter = 0;

  await scanAndHandleByBatch({
    dateField: 'createdAt',
    dateRange: {
      start: startDate,
      end: endDate,
    },
    batchSize: BATCH_SIZE,
    getQueryFn: getRequestsQuery,
    countDocumentsFn: requestDao.countRequests,
    getDocumentsFn: requestDao.getRequests,
    handleDocumentsFn: async (docs) => {
      await handleUpdateMonetizable(docs, ++batchCounter);
    },
  });

  logger.info(
    `Finished updating monetizable requests. Scanned: ${totalScanned}, Updated: ${totalUpdated}`,
    { ctx: 'updateMonetizable' },
  );
};

const getBetweenDates = (start, end) => {
  const dates = [];
  let currentDate = moment(start);
  while (currentDate.isBefore(end)) {
    dates.push(currentDate.format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day');
  }
  return dates;
};

(async () => {
  try {
    const start = '2025-07-01';
    const end = moment().format('YYYY-MM-DD');

    const dates = getBetweenDates(start, end);

    for (const date of dates) {
      logger.info(`Processing date: ${date}`);
      await updateMonetizable(date);
    }

    process.exit(0);
  } catch (err) {
    logger.error('Script failed with error', { error: err.stack || err });

    process.exit(1);
  }
})();
