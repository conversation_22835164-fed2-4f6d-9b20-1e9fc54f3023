require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const voice = {
  code: 'hn_female_lenka_stor_48k-phg',
  name: '<PERSON><PERSON> <PERSON> <PERSON><PERSON>',
  gender: 'female',
  languageCode: 'vi-VN',
  type: 'Neural TTS',
  provider: 'vbee',
  squareImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/nguyet-duong.png',
  roundImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/nguyet-duong.png',
  demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_lenka_stor_48k-phg.wav',
  rank: 4,
  synthesisFunction: '',
  active: true,
  defaultSampleRate: 22050,
  sampleRates: [8000, 16000, 22050],
  level: 'BASIC',
  styles: ['story'],
  beta: true,
};

(async () => {
  logger.info(`Starting create Nguyet Duong voice...`, { ctx: 'RunScript' });
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Nguyet Duong voice successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
