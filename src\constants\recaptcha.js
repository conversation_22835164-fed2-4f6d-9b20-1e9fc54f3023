const MAPPING_RECAPTCHA_ERROR_CODE = {
  'missing-input-secret': 'MISSING_INPUT_SECRET',
  'invalid-input-secret': 'INVALID_INPUT_SECRET',
  'missing-input-response': 'MISSING_INPUT_RESPONSE',
  'invalid-input-response': 'INVALID_INPUT_RESPONSE',
  'bad-request': 'RECAPTCHA_BAD_REQUEST',
  'timeout-or-duplicate': 'TIMEOUT_OR_DUPLICATE',
  'browser-error': 'BROWSER_ERROR',
  'missing-recaptcha': 'MISSING_RECAPTCHA',
  'bot-detected': 'BOT_DETECTED',
};

const MAPPING_RECAPTCHA_ENTERPRISE_ERROR = {
  INVALID_REASON_UNSPECIFIED: 'INVALID_REASON_UNSPECIFIED', // Default unspecified type.
  UNKNOWN_INVALID_REASON: 'UNKNOWN_INVALID_REASON', // If the failure reason was not accounted for.
  MALFORMED: 'MALFORMED', // The provided user verification token was malformed.
  EXPIRED: 'EXPIRED', // The user verification token had expired.
  DUPE: 'DUPE', // The user verification had already been seen.
  MISSING: 'MISSING', // The user verification token was not present.
  BROWSER_ERROR: 'BROWSER_ERROR', // A retriable error (such as network failure) occurred on the browser.
};

const RECAPTCHA_TYPE = {
  V3: 'V3',
  ENTERPRISE: 'ENTERPRISE',
};

const PLATFORM_TYPE = {
  WEBSITE: 'website',
  ANDROID: 'android',
  IOS: 'ios',
};

module.exports = {
  MAPPING_RECAPTCHA_ERROR_CODE,
  MAPPING_RECAPTCHA_ENTERPRISE_ERROR,
  RECAPTCHA_TYPE,
  PLATFORM_TYPE,
};
