const { REDIS_KEY_PREFIX } = require('../constants');
const App = require('../models/app');
const Request = require('../models/request');
const Caching = require('../caching');
const daoUtils = require('./utils');

const createApp = async (appInfo) => {
  const app = await App.create(appInfo);
  return app.toJSON();
};

const getAllApps = async (condition) => {
  const app = await App.find(condition).populate('members.userId').lean();
  return app;
};

/** return original object (without redacting) */
const findAppWithSecretKey = async (condition) => {
  const app = await App.findOne(condition).populate('members.userId').lean();
  return app;
};

const findAppByIdWithSecretKeyInRedis = async (appId) => {
  const key = `${REDIS_KEY_PREFIX.APP}_${appId}`;
  const app = await Caching.RedisRepo.get(key);
  if (app) return JSON.parse(app);

  const appDB = await findAppWithSecretKey({ _id: appId });
  if (!appDB) return null;
  await Caching.RedisRepo.set(key, JSON.stringify(appDB));
  return appDB;
};

// ADVISE: remove this func. Add a generic redaction mechanism at AppService layer, to remove sensitive fields (secretKey, ...)
const findApp = async (condition) => {
  const app = await App.findOne(condition).populate('members.userId').lean();
  if (app?.secretKey) delete app.secretKey;
  return app;
};

const findApps = async ({
  search,
  searchFields = ['name'],
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
}) => {
  const hasSortByTotalRequests =
    sort && sort.some((s) => s.includes('totalRequests'));

  if (hasSortByTotalRequests)
    sort = sort.filter((s) => !s.includes('totalRequests'));

  // eslint-disable-next-line prefer-const
  let { documents: apps, total } = await daoUtils.find(App, {
    search,
    searchFields,
    dateField,
    query,
    fields,
    sort,
  });

  const appIds = apps.map((app) => app._id);
  const totalRequests = await Request.aggregate([
    { $match: { app: { $in: appIds } } },
    {
      $group: {
        _id: '$app',
        totalRequests: { $sum: 1 },
      },
    },
  ]);

  apps = apps.map((app) => {
    const totalRequest = totalRequests.find((item) => app._id === item._id);
    return {
      ...app,
      totalRequests: totalRequest?.totalRequests || 0,
    };
  });

  if (hasSortByTotalRequests)
    apps = apps.sort((a, b) => b.totalRequests - a.totalRequests);

  if (offset) apps = apps.slice(offset);

  if (limit) apps = apps.slice(0, limit);

  apps = apps.map((app) => {
    const { secretKey, ...infoApp } = app;
    return infoApp;
  });

  return { apps, total };
};

const updateApp = async (appId, updateFields) => {
  const app = await App.findByIdAndUpdate(appId, updateFields, {
    new: true,
    upsert: true,
  }).lean();
  return app;
};

// ADVISE: If we have a appDao, this should be something more abstract saveEntityToCache(). Every DAO that support caching should have the same action
const storeAppToCache = async (app) => {
  const key = `${REDIS_KEY_PREFIX.APP}_${app._id}`;
  await Caching.RedisRepo.set(key, JSON.stringify(app));
};

const updateAppInRedis = async (appId, updateFields) => {
  const appKey = `${REDIS_KEY_PREFIX.APP}_${appId}`;
  let appRedis = await Caching.RedisRepo.get(appKey);
  if (appRedis) {
    appRedis = JSON.parse(appRedis);
    appRedis = { ...appRedis, ...updateFields };
    await Caching.RedisRepo.set(appKey, JSON.stringify(appRedis));
  }
};

const updateAppByUserIdInRedis = async (userId) => {
  const apps = await getAllApps({ 'members.userId': userId });

  await Promise.all(
    apps.map(async (app) => {
      const appKey = `${REDIS_KEY_PREFIX.APP}_${app._id}`;
      await Caching.RedisRepo.set(appKey, JSON.stringify(app));
    }),
  );
};

module.exports = {
  createApp,
  storeAppToCache,
  findAppWithSecretKey,
  findAppByIdWithSecretKeyInRedis,
  findApp,
  findApps,
  getAllApps,
  updateApp,
  updateAppInRedis,
  updateAppByUserIdInRedis,
};
