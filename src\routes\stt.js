const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const sttApiController = require('../controllers/stt');
const { authAPI } = require('../middlewares/auth');
const { checkBlockedUserApi } = require('../middlewares/checkBlockedUser');
const uploadMiddleware = require('../middlewares/upload');
const { apiSttValidate } = require('../validations/stt');

router.post(
  '/stt',
  authAPI,
  checkBlockedUserApi,
  uploadMiddleware.uploadSingle('fileContent'),
  apiSttValidate,
  asyncMiddleware(sttApiController.apiStt),
);
router.post(
  '/stt/callback',
  asyncMiddleware(sttApiController.apiCallbackResponse),
);
router.get('/stt/:requestId', asyncMiddleware(sttApiController.getSttRequest));

module.exports = router;
