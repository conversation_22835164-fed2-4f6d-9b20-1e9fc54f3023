# ============================================================================
# BLACK WORDS MANAGEMENT ENDPOINTS (Admin)
# Inappropriate word filtering (Admin)
# ============================================================================

### Get Black Words
GET {{baseUrl}}/api/v1/black-words?search=test&offset=0&limit=10
Authorization: Bearer {{token}}

### Create Black Word (Admin)
POST {{baseUrl}}/api/v1/black-words
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "word": "inappropriate_word"
}

### Update Black Word (Admin)
PUT {{baseUrl}}/api/v1/black-words/{{blackWordId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "word": "updated_word"
}

### Delete Black Word (Admin)
DELETE {{baseUrl}}/api/v1/black-words/{{blackWordId}}
Authorization: Bearer {{token}}

