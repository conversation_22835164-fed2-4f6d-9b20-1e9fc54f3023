const migratedDubbingUserDao = require('../daos/migratedDubbingUser');

const findMigratedDubbingUser = async (userId) => {
  const migratedUser = await migratedDubbingUserDao.findMigratedDubbingUser({
    userId,
  });
  return migratedUser;
};

const agreeToMigrateDubbing = async (userId) => {
  logger.info(`User ${userId} agrees to migrate dubbing`, {
    ctx: 'AgreeToMigrate',
  });
  await migratedDubbingUserDao.updateMigratedDubbingUser(
    { userId },
    { hasAgreedToMigrate: true, agreedToMigrateAt: new Date() },
  );
};

module.exports = {
  findMigratedDubbingUser,
  agreeToMigrateDubbing,
};
