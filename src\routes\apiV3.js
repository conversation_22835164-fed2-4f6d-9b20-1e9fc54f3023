const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const synthesisApiController = require('../controllers/api');
const requestController = require('../controllers/request');
const { authApiV3 } = require('../middlewares/auth');

// ADVISE: check to remove these old API. Seem to be developed 3y ago and no usage? (last check, <PERSON><PERSON> check with ThanhNX)

/* eslint-disable prettier/prettier */
router.post(
  '/api/old/tts',
  authApiV3,
  asyncMiddleware(synthesisApiController.apiSynthesisV3),
);
router.get(
  '/api/old/requests/:requestId',
  asyncMiddleware(requestController.getV3ApiRequest),
);
/* eslint-disable prettier/prettier */

module.exports = router;
