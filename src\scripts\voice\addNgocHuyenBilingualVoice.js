require('dotenv').config();
const yargs = require('yargs/yargs');
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const getVoice = (env) => {
  return {
    code: 'hn_female_ngochuyen_news_22k-your',
    name: '<PERSON><PERSON> - <PERSON>',
    gender: 'male',
    languageCode: 'vi-VN',
    type: 'Neural TTS',
    provider: 'vbee',
    squareImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ngoc-huyen.png',
    roundImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ngoc-huyen.png',
    demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_ngochuyen_fast_news_48k-thg.mp3',
    rank: 3,
    synthesisFunction: `function:a2s-your-${env}-ngochuyen-news-22k-your`,
    active: true,
    defaultSampleRate: 22050,
    sampleRates: [8000, 16000, 22050],
    level: 'PREMIUM',
    styles: ['bilingual-vi-en'],
    beta: true,
    features: ['premium-vietnam-voice'],
  };
};

(async () => {
  const { argv = {} } = yargs(process.argv.slice(2));
  const { env } = argv;
  logger.info(`Starting create Ngoc Huyen voice...`, { ctx: 'RunScript' });
  const voice = getVoice(env);
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Ngoc Huyen successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
