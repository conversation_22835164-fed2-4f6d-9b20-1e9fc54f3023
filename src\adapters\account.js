const { ACCOUNT_URL } = require('../configs');
const callApi = require('../utils/callApi');

const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { getAccessToken } = require('../services/iam');

const getUsageOptions = async (userId) => {
  try {
    const accessToken = await getAccessToken();

    const { status, result: user } = await callApi({
      method: 'GET',
      url: `${ACCOUNT_URL}/api/v1/users/${userId}`,
      headers: { Authorization: `Bearer ${accessToken}` },
      params: {
        fields: 'studioUsageOptions,apiUsageOptions,dubbingUsageOptions',
      },
    });
    if (!status) throw new Error();
    return user;
  } catch (error) {
    throw new CustomError(errorCodes.ACCOUNT_ERROR, 'Call api get user error');
  }
};

module.exports = { getUsageOptions };
