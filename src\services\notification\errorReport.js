const moment = require('moment');
const { BACK_OFFICE_URL } = require('../../configs');

const getInfoErrorReport = ({
  name,
  phoneNumber,
  email,
  description,
  requestId,
  createdAt,
}) => {
  const userInfo = [
    '>*:red_circle: Thông tin báo lỗi*',
    `\n>*Thời gian tạo:* ${moment(createdAt).format('DD-MM-YYYY HH:mm')}`,
    `*Người báo lỗi:* ${name}`,
  ];

  if (email) userInfo.push(`*Email:* ${email}`);
  if (phoneNumber) userInfo.push(`*Số điện thoại:* ${phoneNumber}`);

  const errorReportInfo = [
    `\n>*Mã yêu cầu:* ${requestId}`,
    `*Chi tiết:* ${BACK_OFFICE_URL}/error-reports/${requestId}`,
    `*<PERSON>ô tả:* ${description}`,
  ];
  const messages = [...userInfo, ...errorReportInfo];

  return [
    {
      type: 'section',
      text: { type: 'mrkdwn', text: messages.join(' \n> ') },
    },
  ];
};

module.exports = { getInfoErrorReport };
