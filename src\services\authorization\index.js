const { CustomError } = require('@vbee-holding/vbee-node-shared-lib');
const userDao = require('../../daos/user');
const errorCodes = require('../../errors/code');

class AuthorizationService {
  /** Lookup user from userId. By default, this will throw error if not found */
  static async getUser(userId, throwOnError = true) {
    // ADVISE: support caching because this is important and very often executed func
    // ADVISE: IMPORTANT: BUSINESS REFACTOR: userDto in DB now still contain credit info, currently we don't have wallet service, then we cannot cache the userDto object
    const user = await userDao.findUser({ _id: userId });
    if (!user) {
      if (throwOnError) {
        throw new CustomError(errorCodes.USER_NOT_FOUND.toString(), null, {
          code: errorCodes.USER_NOT_FOUND,
          userId,
        });
      }
    }

    return user;
  }
}

module.exports = { AuthorizationService };
