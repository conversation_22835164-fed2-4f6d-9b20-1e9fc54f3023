# Express Routing in the Repository

This codebase uses Express.js for routing with a well-structured organization. Here's a breakdown of the routing system:

## Main Routing Structure

Entry Point (src/index.js)

## Route Registration

- src/routes/index.js (main API)
- src/routes/v2/index.js (newer version)
- src/routes/apiV3.js (Legacy V3 API routes /api/old/\*)

RouteFile (like src/routes/system.js) define the

- route path
- middleware (authentication, validation, etc.)
- controller

## Middleware Chain (uses several middleware layers)

When naming information attached to an HttpRequest in middleware for later use by subsequent middleware or controllers, a good approach focuses on clarity, consistency, and avoiding potential conflicts.

- Follow a Consistent Naming Convention
- Namespace to Avoid Collisions
- Prefix or Suffix for Clarity: to indicate the type or source of the data, such as auth, meta, or ctx. E.g.: request.**Auth.User or request.**MetaData signals that the data is related to authentication or metadata.
- Avoid Overwriting Framework or Library Properties. This also helps differentiate your custom properties from framework-reserved properties (e.g., request.body, request.query in Express).

### Global Middleware (applied to all routes)

- cors(): Handles Cross-Origin Resource Sharing
- helmet(): Sets security headers
- compression(): Compresses responses
- express.json(): Parses JSON request bodies
- camelCaseReq: Converts request fields to camelCase
- omitReq: Removes null/undefined values from requests
- snakeCaseRes(): Converts response fields to snake_case
- getDevice: Adds device information to the request
- getClientInfo: Adds IP and location information to the request.
  Expose: `req.__clientInfo`

### Route-Specific Middleware:

- auth: Authentication middleware
- hasRole: Role-based authorization
- asyncMiddleware: Error handling for async functions
- checkBlockedUserApi: check for blocked user to use API
- Various validation middleware (e.g., synthesisApiValidate)

## Request Flow

1. Request Received → Global middleware processes the request
1. Route Matching → Express matches the URL to a specific route
1. Route Middleware → Route-specific middleware executes
1. Controller → The controller function handles the business logic
1. Service Layer → Controllers typically call service functions
1. Response → The response is sent back to the client

## Error Handling

asyncMiddleware: Catches errors in async functions
errorHandler: General error handling middleware for the AIVoice Studio Backend API
errorHandlerApi: error handling for API Selling?
