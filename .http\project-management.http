
# ============================================================================
# PROJECT MANAGEMENT ENDPOINTS
# 📁 Project Management (7 endpoints)
# TTS project CRUD operations
# Project synthesis and audio access
# ============================================================================

### Create Project
POST {{baseUrl}}/api/v1/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "My TTS Project",
    "description": "Project for TTS synthesis",
    "content": [
        {
            "text": "First paragraph",
            "voice_code": "hn_female_ngochuyen_full_48k-fhg"
        }
    ]
}

### Get Projects
GET {{baseUrl}}/api/v1/projects?offset=0&limit=10
Authorization: Bearer {{token}}

### Get Sample Project
GET {{baseUrl}}/api/v1/projects/sample
Authorization: Bearer {{token}}

### Get Specific Project
GET {{baseUrl}}/api/v1/projects/{{projectId}}
Authorization: Bearer {{token}}

### Update Project
PUT {{baseUrl}}/api/v1/projects/{{projectId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Updated Project Name",
    "content": [
        {
            "text": "Updated content",
            "voice_code": "hn_female_ngochuyen_full_48k-fhg"
        }
    ]
}

### Delete Project
DELETE {{baseUrl}}/api/v1/projects/{{projectId}}
Authorization: Bearer {{token}}

### Synthesis Project
POST {{baseUrl}}/api/v1/projects/synthesis
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "project_id": "{{projectId}}",
    "audio_type": "mp3",
    "sample_rate": 22050
}

### Get Project Audio Link
GET {{baseUrl}}/api/v1/projects/{{projectId}}/audio
Authorization: Bearer {{token}}

