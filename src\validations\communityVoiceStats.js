const { Joi, validate } = require('express-validation');

const getUserVoiceStats = {
  query: Joi.object({
    startDate: Joi.string().trim().optional(),
    endDate: Joi.string().trim().optional(),
    search: Joi.string().trim().optional(),
  }),
};

const getUserVoiceStatsByUserId = {
  params: Joi.object({
    userId: Joi.string().trim().required(),
  }),
  query: Joi.object({
    startDate: Joi.string().trim().optional(),
    endDate: Joi.string().trim().optional(),
    search: Joi.string().trim().optional(),
  }),
};

module.exports = {
  getUserVoiceStatsValidate: validate(getUserVoiceStats, {
    keyByField: true,
  }),
  getUserVoiceStatsByUserIdValidate: validate(getUserVoiceStatsByUserId, {
    keyByField: true,
  }),
};
