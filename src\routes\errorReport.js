const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const errorReportController = require('../controllers/errorReport');
const { auth, hasRole } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.get('/admin/error-reports', auth, hasRole("view-error-reports"), asyncMiddleware(errorReportController.getErrorReports));
router.get('/admin/error-reports/:errorReportId', auth, hasRole("view-error-reports"), asyncMiddleware(errorReportController.getErrorReport));
/* eslint-disable prettier/prettier */

module.exports = router;
