require('dotenv').config();
require('../../models');

const User = require('../../models/user');
const logger = require('../../utils/logger');
const callApi = require('../../utils/callApi');
const { VBEE_URL } = require('../../configs');
const { updateUserById } = require('../../daos/user');

global.logger = logger;

const updateInfoUserWrongPackage = async () => {
  logger.info(`Starting add max preview`, { ctx: 'RunScript' });
  const wrongPackageUsers = await User.find({
    packageCode: { $regex: 'STUDIO-ADV' },
    packageExpiryDate: { $gt: new Date() },
  });

  const { result: packageResponse } = await callApi({
    url: `${VBEE_URL}/api/v1/packages`,
    method: 'GET',
  });
  const { packages } = packageResponse;

  for (const wrongUser of wrongPackageUsers) {
    const userId = wrongUser._id;
    const { result: infoUser } = await callApi({
      url: `${VBEE_URL}/api/v1/users/${userId}`,
      method: 'GET',
    });

    if (infoUser?.packageCode !== wrongUser.packageCode) {
      let userPackage;
      try {
        [userPackage] = packages.filter(
          (pkg) => pkg.code === infoUser.packageCode,
        );
      } catch (error) {
        logger.error(error, { ctx: 'RunScript' });
        // eslint-disable-next-line no-continue
        continue;
      }

      const { packageCode, packageExpiryDate } = infoUser;
      const { features, concurrentRequest, maxLengthInputText } = userPackage;

      await updateUserById(userId, {
        features,
        concurrentRequest,
        maxLengthInputText,
        packageCode,
        packageExpiryDate,
      });
      logger.info(`Updated user ${userId}`, { ctx: 'RunScript' });
    }
  }
  logger.info(`Update user info successfully`, { ctx: 'RunScript' });
};

(async () => {
  await updateInfoUserWrongPackage();
  process.exit(1);
})();
