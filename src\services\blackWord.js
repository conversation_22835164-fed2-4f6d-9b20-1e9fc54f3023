const blackWordDao = require('../daos/blackWord');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { initBlackWords } = require('./init/blackWords');
const { VALID_LETTERS } = require('../constants');

const createBlackWord = async (word) => {
  const blackWordExist = await blackWordDao.findBlackWord({ word });
  if (blackWordExist) throw new CustomError(errorCodes.BLACK_WORD_EXIST);

  const newBlackWord = await blackWordDao.createBlackWord(word);

  initBlackWords();
  return newBlackWord;
};

const updateBlackWord = async (blackWordId, word) => {
  const blackWordExist = await blackWordDao.findBlackWord(blackWordId);
  if (!blackWordExist) throw new CustomError(errorCodes.BLACK_WORD_NOT_EXIST);

  const updatedBlackWord = await blackWordDao.updateBlackWord(blackWordId, {
    word,
  });

  initBlackWords();

  return updatedBlackWord;
};

const deleteBlackWord = async (blackWordId) => {
  const blackWordExist = await blackWordDao.findBlackWord(blackWordId);
  if (!blackWordExist) throw new CustomError(errorCodes.BLACK_WORD_NOT_EXIST);

  const deletedBlackWord = await blackWordDao.deleteBlackWord(blackWordId);

  initBlackWords();

  return deletedBlackWord;
};

const containBlackWords = (text) => {
  const lowerText = text.toLowerCase();
  // Match blacklisted words with word boundaries, case-insensitive and Unicode support
  const regex = new RegExp(
    `(?<![${VALID_LETTERS}])(${BLACK_WORDS.join('|')})(?![${VALID_LETTERS}])`,
  );
  return regex.test(lowerText);
};

// ADVISE: extract to a centralized BlackWordService
module.exports = {
  createBlackWord,
  updateBlackWord,
  deleteBlackWord,
  containBlackWords,
};
