import { it, expect, describe } from 'vitest';
import { isValidUrl } from './string';

describe('string', () => {
  describe('isValidUrl', () => {
    it('invalid HTTP Url', () => {
      expect(isValidUrl({})).toBeFalsy();
      expect(isValidUrl('')).toBeFalsy();
      expect(isValidUrl('ftp://google.com')).toBeFalsy();
    });

    it('HTTP Url', () => {
      expect(isValidUrl('http://vbee.vn')).toBeTruthy();
      expect(isValidUrl('https://vbee.vn')).toBeTruthy();

      expect(isValidUrl('https://vbee.vn/a/b/c/d')).toBeTruthy();
      expect(isValidUrl('https://vbee.vn/a/b/c/d?e=f&g=h')).toBeTruthy();
      expect(isValidUrl('https://vbee.vn/a/b/c/d?e=f&g=h#i')).toBeTruthy();
    });
  });
});
