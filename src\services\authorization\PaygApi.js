const moment = require('moment');
const { CustomError } = require('@vbee-holding/vbee-node-shared-lib');
const { AuthorizationService } = require('.');
const errorCodes = require('../../errors/code');

class PaygApiAuthorizationService extends AuthorizationService {
  constructor(user) {
    super();
    this.user = user;
  }

  /** Check current user, have enough permission (or valid paid package) to create app */
  async canCreateApp(throwOnError = true) {
    const { packageCode, packageExpiryDate } = this.user.apiPackage || {};
    const canCreateApp =
      packageCode &&
      (!packageExpiryDate ||
        packageExpiryDate < 0 ||
        moment().isBefore(moment(packageExpiryDate)));

    if (!canCreateApp) {
      if (throwOnError) {
        throw new CustomError(errorCodes.PACKAGE_EXPIRED.toString(), null, {
          code: errorCodes.PACKAGE_EXPIRED,
          userId: this.user._id,
          packageCode,
          packageExpiryDate,
        });
      }
    }

    return canCreateApp;
  }
}

module.exports = {
  PaygApiAuthorizationService,
};
