const { CustomError } = require('@vbee-holding/vbee-node-shared-lib');
const logger = require('../utils/logger');
const {
  REDIS_KEY_PREFIX,
  KAFKA_TOPIC,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
  REQUEST_STATUS,
  PACKAGE_FEATURE,
  TTS_PROCESSING_STEPS,
} = require('../constants');
const { SYNTHESIS_BY_GATEWAY } = require('../configs');

const { sendMessage, sendMessages } = require('./kafka/producer');

const requestDao = require('../daos/request');
const clientPauseDao = require('../daos/clientPause');

const Caching = require('../caching');
const RequestCaching = require('../caching/requestCaching');
const ProcessingTimeCaching = require('../caching/processingTimeCaching');

const { AuthorizationService } = require('./authorization');
const { processText, getMsClientPause } = require('./preprocessing');
const { incrTotalProcessingRequests } = require('./metrics');
const { updateProcessingAt } = require('./inprogressRequest');
const { getPackageUsageOptions } = require('./package');

const getText = async ({
  requestType,
  text,
  voice,
  version,
  paragraphBreak,
  hasClientPause,
}) => {
  if (requestType === REQUEST_TYPE.DUBBING) return text;

  const normalizeText = await processText({
    text,
    voiceProvider: voice.provider,
    voiceLanguage: voice.languageCode,
    ttsCoreVersion: version,
    paragraphBreak,
    hasClientPause,
  });

  return normalizeText;
};

const sendPendingRequestToKafka = async ({ requestId, userId }) => {
  try {
    const request = await requestDao.findRequestById(requestId);
    const processingTime = await ProcessingTimeCaching.getProcessingTime(
      requestId,
    );
    const { pushToQueueAt } = processingTime;
    if (request.type === REQUEST_TYPE.API_CACHING) {
      request.sentenceKeys = [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`];
      await RequestCaching.createRequestInRedis(request);
    }
    if (!request) throw new Error('Request is not exists');

    let paragraphBreak;
    let sentenceBreak;
    let majorBreak;
    let mediumBreak;
    let hasClientPause;

    if (userId) {
      const user = await AuthorizationService.getUser(userId, false);
      // ADVISE: check in runtime, can we throw ASAP when we don't have user? We don't need to get Package Usage Options

      const studioUsageOptions = await getPackageUsageOptions({
        userId,
        packageCode: user.packageCode,
        userUsageOptions: user,
      });
      if (!user)
        throw new CustomError('User does not exists', null, {
          userId,
        });

      hasClientPause = studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      );
      if (hasClientPause) {
        const clientPause = await clientPauseDao.findClientPause(userId);
        ({ paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
          clientPause || {});
      }
    }

    const kafkaTopic = KAFKA_TOPIC.SENTENCE_TOKENIZATION_REQUEST;

    const {
      text,
      numberOfSentences,
      voice = {},
      awsZoneFunctions,
      awsZoneSynthesis,
      type,
      version,
    } = request;
    const {
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      srtFunction,
    } = awsZoneFunctions;

    // multi voice
    if (numberOfSentences > 0) {
      const sentences = await requestDao.getDetailSentencesByRequestId(
        requestId,
      );

      ProcessingTimeCaching.saveStepProcessingTime({
        requestId,
        step: TTS_PROCESSING_STEPS.QUEUE,
        startTime: pushToQueueAt,
        additionalFields: { startSentenceTokenizerAt: Date.now() },
      });

      const messages = await Promise.all(
        sentences.map(async (sentence, index) => {
          const { text: sentenceText, voice: sentenceVoice } = sentence;

          const normalizeText = await processText({
            text: sentenceText,
            voiceProvider: sentenceVoice.provider,
            voiceLanguage: sentenceVoice.languageCode,
            ttsCoreVersion: request.version,
            paragraphBreak,
            hasClientPause,
          });

          return {
            value: {
              requestId,
              index,
              text: normalizeText,
              voice: sentenceVoice,
              ttsCoreVersion: request.version,
              awsZoneSynthesis,
              sentenceTokenizerFunction,
              newSentenceTokenizerFunction,
              clientPause: getMsClientPause({
                paragraphBreak,
                sentenceBreak,
                majorBreak,
                mediumBreak,
              }),
            },
          };
        }),
      );

      await sendMessages(kafkaTopic, messages);
      return;
    }

    const normalizeText = await getText({
      requestType: type,
      text,
      voice,
      version,
      paragraphBreak,
      hasClientPause,
    });

    const data = {
      requestId,
      index: 0,
      text: normalizeText,
      voice,
      ttsCoreVersion: request.version,
      clientPause: getMsClientPause({
        paragraphBreak,
        sentenceBreak,
        majorBreak,
        mediumBreak,
      }),
      srtFunction,
      awsZoneSynthesis,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
    };

    ProcessingTimeCaching.saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.QUEUE,
      startTime: pushToQueueAt,
      additionalFields: { startSentenceTokenizerAt: Date.now() },
    });

    await sendMessage(kafkaTopic, { value: data });
  } catch (error) {
    logger.error(error, { ctx: 'ProcessRequestByCcr', requestId, userId });
    require('./synthesis').handleSentenceTokenizationFailureResponse(
      requestId,
      error.message,
    );
  }
};

/** for specific user, take requestId from Redis Queue */
const runSentenceTokenizerQueue = async (userId, requestType) => {
  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const numOfPendingReq = await Caching.GlobalQueue.size(pendingReqKey);
  if (numOfPendingReq === 0) {
    // ADVISE: no need to log here
    logger.debug(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }

  const requestId = await Caching.GlobalQueue.dequeue(pendingReqKey);
  if (!requestId) {
    // ADVISE: no need to log here
    logger.debug(`No pending request`, { ctx: 'ProcessRequestByCcr', userId });
    return;
  }

  incrTotalProcessingRequests();
  await updateProcessingAt(requestId, new Date());
  // ADVISE: no need to log here
  logger.debug('Pop pending request from queue', {
    ctx: 'ProcessRequestByCcr',
    requestId,
    userId,
  });

  const request = await requestDao.findRequestById(requestId);
  if (request?.status === REQUEST_STATUS.FAILURE) {
    logger.warn(`Request is already processed as failure`, {
      ctx: 'ProcessRequestByCcr',
      requestId,
      userId,
    });
    decrPendAndInprRequests(userId, requestId, request.type);
    return;
  }

  if (SYNTHESIS_BY_GATEWAY) {
    // ADVISE: avoid using inline require(). The inline require here because "ttsProcessing" also call "queue"
    require('./ttsProcessing').callApiSynthesis(requestId);
  } else {
    sendPendingRequestToKafka({ requestId, userId });
  }
};

/** use Redis as Queue to check for CCR of userId.
 *  store user's requestId into Redis Queue.
 * (try to) pop from queue to process (if user.ccr > his allowed CCR) */
const pushRequestToQueue = async ({ userId, requestId, ccr, requestType }) => {
  const numOfPendAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const userQueueLength = await Caching.GlobalQueue.enqueue(
    pendingReqKey,
    requestId,
  );
  logger.info('Push pending request to queue', {
    ctx: 'ProcessRequestByCcr',
    requestId,
    userId,
    userQueueLength,
  });

  if (userQueueLength) {
    const numOfPendAndInprReq = await Caching.GlobalCounter.increase(
      numOfPendAndInprReqKey,
    );
    if (ccr >= 0 && numOfPendAndInprReq > ccr) {
      logger.warn(
        `Queue ${numOfPendAndInprReqKey} is max ccr: ${numOfPendAndInprReq}`,
        { ctx: 'ProcessRequestByCcr', userId, requestId },
      );
      return userQueueLength;
    }

    runSentenceTokenizerQueue(userId, requestType);
  }

  return userQueueLength;
};

/** decrease Pending and InProgress requests.
 * @description also for specific user, take requestId from Redis Queue? */
const decrPendAndInprRequests = async (userId, requestId, requestType) => {
  const numPendAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  const numOfPendAndInprReq = await Caching.GlobalCounter.decrease(
    numPendAndInprReqKey,
  );
  logger.info(`Decrease ${numPendAndInprReqKey}: ${numOfPendAndInprReq}`, {
    ctx: 'ProcessRequestByCcr',
    userId,
    requestId,
  });

  runSentenceTokenizerQueue(userId, requestType);
};

const processQueueWhenRequestFailure = async ({
  userId,
  requestId,
  requestType,
}) => {
  const failureKey = `${REDIS_KEY_PREFIX.FAILURE_REQUEST}_${requestId}`;

  const numOfFailure = await Caching.GlobalCounter.increase(failureKey);
  Caching.RedisRepo.expire(failureKey, REDIS_KEY_TTL.FAILURE_REQUEST);

  if (numOfFailure > 1) return;

  decrPendAndInprRequests(userId, requestId, requestType);
};

const processQueueWhenRequestSuccess = async ({
  userId,
  requestId,
  requestType,
}) => {
  const successKey = `${REDIS_KEY_PREFIX.SUCCESS_REQUEST}_${requestId}`;

  const numOfSuccess = await Caching.GlobalCounter.increase(successKey);
  Caching.RedisRepo.expire(successKey, REDIS_KEY_TTL.SUCCESS_REQUEST);

  if (numOfSuccess > 1) return;

  if (requestType !== REQUEST_TYPE.API_CACHING)
    decrPendAndInprRequests(userId, requestId, requestType);
};

const processRemovePendingReqByType = async ({ requestType, userId }) => {
  if (!Array.isArray(requestType) || requestType.length === 0) return;

  await Promise.all(
    requestType.map((type) =>
      removePendingRequests({ requestType: type, userId }),
    ),
  );
};

const removePendingRequests = async ({ requestType, userId }) => {
  const pendingReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_REQUESTS}_${userId}`;

  const pendingAndInprReqKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
      : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

  await Caching.Manager.mdel([pendingReqKey, pendingAndInprReqKey]);
};

module.exports = {
  pushRequestToQueue,

  decrPendAndInprRequests,

  processQueueWhenRequestFailure,
  processQueueWhenRequestSuccess,

  sendPendingRequestToKafka,
  processRemovePendingReqByType,
};
