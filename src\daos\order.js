const Order = require('../models/order');
const daoUtils = require('./utils');

const updateOrderById = async (orderId, updateFields) => {
  const order = await Order.findByIdAndUpdate(orderId, updateFields, {
    new: true,
    upsert: true,
    omitUndefined: true,
  });
  return order;
};

const findOrder = async (condition) => {
  const order = await daoUtils.findOne(Order, condition);
  return order;
};

module.exports = { updateOrderById, findOrder };
