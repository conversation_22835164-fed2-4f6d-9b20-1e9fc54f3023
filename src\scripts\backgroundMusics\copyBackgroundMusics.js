require('dotenv').config();
require('../../models');

const BackgroundMusic = require('../../models/backgroundMusic');
const logger = require('../../utils/logger');
const callApi = require('../../utils/callApi');
const { UPLOAD_URL } = require('../../configs');

global.logger = logger;

const copyBackgroundMusics = async () => {
  try {
    const numOfBgMusics = await BackgroundMusic.countDocuments({});
    logger.info(`Number of background musics: ${numOfBgMusics}`, {
      ctx: 'RunScript',
    });

    for (let i = 0; i < numOfBgMusics; i += 1000) {
      logger.info(`Copying background musics from ${i} to ${i + 1000}`, {
        ctx: 'RunScript',
      });

      const bgMusics = await BackgroundMusic.find({}).skip(i).limit(1000);

      bgMusics.forEach(async (bgMusic) => {
        const { _id: bgMusicId, name, link, userId } = bgMusic;

        const extension = link.split('.').pop();
        let fileName = name.split('.').shift();
        if (userId) fileName = `users/${fileName}`;
        else fileName = `system/${fileName}`;

        const { result } = await callApi({
          url: `${UPLOAD_URL}/api/v1/background-musics/copy`,
          method: 'POST',
          data: { sourceUrl: link, fileName, extension },
        });

        if (!result?.url) throw new Error();

        await BackgroundMusic.findByIdAndUpdate(bgMusicId, {
          link: result.url,
        });
      });
    }
  } catch (error) {
    logger.error('Copy background music error', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting copy background musics...`, { ctx: 'RunScript' });
  await copyBackgroundMusics();
  logger.info(`Copy background musics successfully`, { ctx: 'RunScript' });
})();
