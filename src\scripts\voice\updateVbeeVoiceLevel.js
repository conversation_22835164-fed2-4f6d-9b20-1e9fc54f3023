require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update voice level...`, { ctx: 'RunScript' });

  const voices = require('./seedVoiceLevel.json');
  for (const voice of voices) {
    await Voice.findOneAndUpdate({ code: voice.code }, voice, { upsert: true });
  }

  const updatedVoices = await Voice.find({});
  global.VOICES = updatedVoices;

  logger.info(`Update voice level successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
