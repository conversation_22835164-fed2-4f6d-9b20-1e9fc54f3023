const voiceCloningService = require('../services/voiceCloning');
const voiceCloningDao = require('../daos/voiceCloning');

const createVoiceCloningVoice = async (req, res) => {
  const {
    userId,
    code,
    name,
    gender,
    avatar,
    locale,
    province,
    status,
    type,
    ttsGateCode,
    sampleLink,
  } = req.body;

  const voice = await voiceCloningService.createVoiceCloningVoice({
    userId,
    code,
    name,
    gender,
    avatar,
    locale,
    province,
    status,
    type,
    ttsGateCode,
    sampleLink,
  });
  return res.send({ voice });
};

const getVoiceCloningVoices = async (req, res) => {
  const { search, fields, offset, limit, sort, searchFields } = req.query;

  // ADVISE: duplicated code with controllers/voice Duplicate code: lines 13-30
  const query = {};
  query.query = { active: true };
  if (search) query.search = search;
  if (fields) query.fields = fields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  if (searchFields) query.searchFields = searchFields.split(',');
  Object.keys(req.query)
    .filter(
      (q) =>
        ['search', 'fields', 'offset', 'limit', 'sort', 'searchFields'].indexOf(
          q,
        ) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { voices, total } = await voiceCloningDao.findVoiceCloningVoices(query);
  return res.send({ voices, metadata: { total } });
};

const updateClonedVoice = async (req, res) => {
  const {
    userId,
    code,
    name,
    avatar,
    status,
    demo,
    active,
    retentionDays,
    discardAt,
    category,
  } = req.body;
  await voiceCloningService.updateClonedVoice({
    userId,
    code,
    name,
    avatar,
    status,
    demo,
    active,
    retentionDays,
    discardAt,
    category,
  });
  return res.send({});
};

module.exports = {
  createVoiceCloningVoice,
  getVoiceCloningVoices,
  updateClonedVoice,
};
