const { Joi, validate } = require('express-validation');

const createApp = {
  body: Joi.object({
    name: Joi.string().trim().required(),
    expiresAt: Joi.date()
      .optional()
      .min(new Date() + 24 * 60 * 60 * 1000),
  }),
};

const updateApp = {
  body: Joi.object({
    name: Joi.string().trim().optional(),
    active: Joi.boolean().when('name', {
      not: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
  }),
};

module.exports = {
  createAppValidate: validate(createApp, { keyByField: true }),
  updateAppValidate: validate(updateApp, { keyByField: true }),
};
