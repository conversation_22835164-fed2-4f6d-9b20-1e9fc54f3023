const logger = require('../utils/logger');
const healthCheckService = require('../services/healthCheck');

const checkStartup = async (req, res) => {
  const isReady = healthCheckService.isReady();
  logger.debug(
    { pid: process.pid, probe: 'STARTUP', status: isReady },
    { ctx: 'HealthCheck' },
  );
  if (isReady) return res.status(200).send();
  return res.status(500).send();
};

const checkReadiness = async (req, res) => {
  const isReady = healthCheckService.isReady();
  logger.debug(
    { pid: process.pid, probe: 'READINESS', status: isReady },
    { ctx: 'HealthCheck' },
  );
  if (isReady) return res.status(200).send();
  return res.status(500).send();
};

const checkLiveness = async (req, res) => {
  const isHealthy = healthCheckService.isHealthy();
  logger.debug(
    { pid: process.pid, probe: 'LIVENESS', status: isHealthy },
    { ctx: 'HealthCheck' },
  );
  if (isHealthy) return res.status(200).send();
  return res.status(500).send();
};

module.exports = {
  checkStartup,
  checkReadiness,
  checkLiveness,
};
