const { Joi, validate } = require('express-validation');
const { REQUEST_TYPE } = require('../constants');

const updateParagraphs = {
  body: Joi.object({
    paragraphs: Joi.array().items(
      Joi.object({
        key: Joi.string().required(),
        text: Joi.string().required().allow(''),
        elements: Joi.array()
          .items(
            Joi.object({
              key: Joi.string().required(),
              text: Joi.string().required().allow(''),
              name: Joi.string().required().allow(null),
              value: Joi.string().required().allow(null),
              startOffset: Joi.number().required(),
              endOffset: Joi.number().required(),
            }),
          )
          .required(),
      }),
    ),
  }),
};

const updateManyRequestByIds = {
  body: Joi.object({
    requestIds: Joi.array().items(Joi.string().required()).min(1),
    isDeleteAll: Joi.boolean(),
    type: Joi.string()
      .valid(...Object.values(REQUEST_TYPE))
      .optional(),
  }).custom((value, helpers) => {
    const { isDeleteAll, requestIds } = value;
    if (!isDeleteAll && !requestIds) {
      return helpers.message('Must provide "requestIds" or "isDeleteAll"');
    }
    return value;
  }),
};

const updateRequestById = {
  body: Joi.object({
    title: Joi.string().min(1).max(50).required(),
  }),
};

const removePendingRequests = {
  body: Joi.object({
    requestType: Joi.array()
      .items(Joi.string().valid(...Object.values(REQUEST_TYPE)))
      .required(),
    userId: Joi.string().required(),
  }),
};

module.exports = {
  updateParagraphsValidate: validate(updateParagraphs, { keyByField: true }),
  updateManyRequestByIdsValidate: validate(updateManyRequestByIds, {
    keyByField: true,
  }),
  updateRequestByIdValidate: validate(updateRequestById, { keyByField: true }),
  removePendingRequests: validate(removePendingRequests, { keyByField: true }),
};
