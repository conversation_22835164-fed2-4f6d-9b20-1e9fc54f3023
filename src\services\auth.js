const jwt = require('jsonwebtoken');
const camelCaseKeys = require('camelcase-keys');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { IAM_VALID_CLIENT_IDS } = require('../configs');
const appDao = require('../daos/app');
const appService = require('./app');

const verifyAccessToken = async (accessToken) => {
  try {
    let data = jwt.verify(accessToken, IAM_PUBLIC_KEY);
    data = camelCaseKeys(data);
    const { sub: userId, azp: clientId } = data;

    if (!userId) throw new CustomError(errorCodes.UNAUTHORIZED);
    if (!IAM_VALID_CLIENT_IDS.includes(clientId))
      throw new CustomError(errorCodes.UNAUTHORIZED);

    return data;
  } catch (error) {
    throw new CustomError(errorCodes.UNAUTHORIZED);
  }
};

const verifyAccessTokenAPI = async (appId, accessToken) => {
  try {
    const app = await appDao.findAppByIdWithSecretKeyInRedis(appId);
    if (!app) throw new CustomError(errorCodes.APP_NOT_FOUND);
    if (!app.active) throw new CustomError(errorCodes.APP_NOT_ACTIVATED);

    const { secretKey, token: appToken } = app;
    if (!secretKey) throw new CustomError(errorCodes.UNAUTHORIZED);

    if (accessToken !== appToken)
      throw new CustomError(errorCodes.UNAUTHORIZED);
    jwt.verify(accessToken, secretKey);
    return app;
  } catch (error) {
    throw new CustomError(error.errorCode || errorCodes.UNAUTHORIZED);
  }
};

const verifyKeyV3 = async (appId) => {
  try {
    const app = await appService.getApp(appId, false);
    if (!app) throw new CustomError(errorCodes.UNAUTHORIZED);

    const { active } = app;
    if (!active) throw new CustomError(errorCodes.APP_NOT_ACTIVATED);

    return app;
  } catch (error) {
    throw new CustomError(errorCodes.UNAUTHORIZED);
  }
};

module.exports = {
  verifyAccessToken,
  verifyAccessTokenAPI,
  verifyKeyV3,
};
