require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const AMAZON_SAMPLE_RATES = [8000, 16000];
const AMAZON_DEFAULT_SAMPLE_RATE = 16000;

const updateSampleRate = async () => {
  try {
    await Voice.updateMany(
      { provider: VOICE_PROVIDER.AMAZON },
      {
        sampleRates: AMAZON_SAMPLE_RATES,
        defaultSampleRate: AMAZON_DEFAULT_SAMPLE_RATE,
      },
    );
  } catch (error) {
    logger.error(`Update sample rate of amazon provider failure`, {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting sample rate of amazon provider ...`, {
    ctx: 'RunScript',
  });
  await updateSampleRate();
  const voices = await Voice.find({});
  global.VOICES = voices;
  logger.info(`Update sample rate of amazon provider successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
