name: Release

on:
  release:
    types: [published]

jobs:
  test-ci:
    uses: vbee-holding/shared-github-actions/.github/workflows/build-push-image.yml@v1
    with:
      registry_type: 'GAR'
      gar_location: asia-southeast1
      repository: ${{ github.event.repository.name }}
      tag: ${{ github.event.release.tag_name }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GAR_CREDENTIALS: ${{ secrets.VBEE_TEST_GAR_PUSH_KEY }}

  uat-ci:
    uses: vbee-holding/shared-github-actions/.github/workflows/build-push-image.yml@v1
    with:
      registry_type: 'GAR'
      gar_location: asia-southeast1
      repository: ${{ github.event.repository.name }}
      tag: ${{ github.event.release.tag_name }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GAR_CREDENTIALS: ${{ secrets.VBEE_UAT_GAR_PUSH_KEY }}

  prod-ci:
    uses: vbee-holding/shared-github-actions/.github/workflows/build-push-image.yml@v1
    with:
      registry_type: 'GAR'
      gar_location: asia-southeast1
      repository: ${{ github.event.repository.name }}
      tag: ${{ github.event.release.tag_name }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GAR_CREDENTIALS: ${{ secrets.AIVOICE_GAR_PUSH_KEY }}

  prod-ci-ecr:
    uses: vbee-holding/shared-github-actions/.github/workflows/build-push-image.yml@v1
    with:
      registry_type: 'ECR'
      aws_region: ap-southeast-1
      repository: ${{ github.event.repository.name }}
      tag: ${{ github.event.release.tag_name }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      AWS_ACCESS_KEY_ID: ${{ secrets.VBEE_PROD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.VBEE_PROD_AWS_SECRET_ACCESS_KEY }}
