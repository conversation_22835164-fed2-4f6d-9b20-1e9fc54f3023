require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const GOOGLE_SAMPLE_RATES = [8000, 16000, 22050, 32000, 44100, 48000];
const GOOGLE_DEFAULT_SAMPLE_RATE = 48000;

const updateSampleRate = async () => {
  try {
    await Voice.updateMany(
      { provider: VOICE_PROVIDER.GOOGLE },
      {
        sampleRates: GOOGLE_SAMPLE_RATES,
        defaultSampleRate: GOOGLE_DEFAULT_SAMPLE_RATE,
      },
    );
  } catch (error) {
    logger.error(`Update sample rate of google provider failure`, {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting sample rate of google provider ...`, {
    ctx: 'RunScript',
  });
  await updateSampleRate();
  const voices = await Voice.find({});
  global.VOICES = voices;
  logger.info(`Update sample rate of google provider successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
