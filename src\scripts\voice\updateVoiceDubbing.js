/* eslint-disable import/no-extraneous-dependencies */
require('dotenv').config();
const yargs = require('yargs/yargs');
require('../../models');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');
const { getDataVoiceDubbing } = require('./seedUpdateVoiceDubbing');

global.logger = logger;

(async () => {
  logger.info(`Starting update voice has dubbing...`, {
    ctx: 'RunScriptUpdateVoiceDubbing',
  });
  const { argv = {} } = yargs(process.argv.slice(2));
  const { env } = argv;

  const data = getDataVoiceDubbing(env);
  for (const voice of data) {
    const { code, hasDubbing, synthesisFunction } = voice;
    await Voice.findOneAndUpdate({ code }, { hasDubbing, synthesisFunction });
  }

  logger.info(`Update voice dubbing successfully`, {
    ctx: 'RunScriptUpdateVoiceDubbing',
  });
  process.exit(1);
})();

// Usage: node src/scripts/voice/updateVoiceDubbing.js --env=<environment> // dev-uat-prod
// Example: node src/scripts/voice/updateVoiceDubbing.js --env="dev"
