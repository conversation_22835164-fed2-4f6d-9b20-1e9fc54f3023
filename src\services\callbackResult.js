const requestDao = require('../daos/request');
const callbackResultDao = require('../daos/callbackResult');

const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const { verifyAccessTokenAPI } = require('./auth');

const getCallbackResult = async (requestId, appToken) => {
  const request = await requestDao.findRequest({ _id: requestId }, ['app']);
  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const { app: appId } = request;

  await verifyAccessTokenAPI(appId, appToken);

  const selectFields = [
    'requestId',
    'statusCode',
    'callbackUrl',
    'payload',
    'result',
    'createdAt',
  ];
  const callbackResult = await callbackResultDao.findCallbackResult(
    requestId,
    selectFields,
  );
  delete callbackResult._id;

  return callbackResult;
};

module.exports = { getCallbackResult };
