const { Joi, validate } = require('express-validation');
const { ObjectId } = require('mongoose').Types;

const createBlackWord = {
  body: Joi.object({
    word: Joi.string().trim().required(),
  }),
};

const editBlackWord = {
  params: Joi.object({
    blackWordId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('blackWordId is invalid');
      }),
  }),
  body: Joi.object({
    word: Joi.string().trim().required(),
  }),
};

const deleteBlackWord = {
  params: Joi.object({
    blackWordId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('blackWordId is invalid');
      }),
  }),
};

module.exports = {
  createBlackWordValidate: validate(createBlackWord, { keyByField: true }),
  editBlackWordValidate: validate(editBlackWord, { keyByField: true }),
  deleteBlackWordValidate: validate(deleteBlackWord, { keyByField: true }),
};
