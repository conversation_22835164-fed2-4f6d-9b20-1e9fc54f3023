const { VBEE_URL } = require('../configs');
const {
  RESPONSE_TYPE,
  V3_API_TYPE,
  V3_RESPONSE_TYPE,
  OUTPUT_TYPE,
  APP_ROLE,
  API_V3_URL,
} = require('../constants');
const mapVoices = require('../constants/mapVoices.json');

const { normalizeBitrate } = require('../utils/bitrate');

const apiSynthesisService = require('../services/apiSynthesis');
const { getVoiceByCode } = require('../services/voice');

const apiSynthesis = async (req, res) => {
  const { apiApp: app } = req;
  const { publicIP: ip } = req.__clientInfo;

  const {
    responseType,
    callbackUrl,
    inputText,
    voiceCode,
    audioDomainType,
    outputType,
    sentences,
    audioType,
    bitrate,
    sampleRate,
    speedRate,
    backgroundMusic,
    fromVn,
    returnTimestampWords,
  } = req.body;

  let normalizedSentences = [];
  if (sentences?.length > 0) {
    normalizedSentences = sentences.map((sentence) => ({
      text: sentence.inputText,
      voiceCode: sentence.voiceCode,
      speed: sentence.speedRate,
    }));
  }

  const request = await apiSynthesisService.handleApiSynthesisRequest({
    ip,
    app,
    responseType,
    callbackUrl,
    text: inputText,
    voiceCode,
    outputType,
    audioDomainType,
    sentences: normalizedSentences,
    audioType,
    bitrate: normalizeBitrate(bitrate),
    sampleRate,
    speed: speedRate,
    backgroundMusic,
    fromVn,
    returnTimestampWords,
  });

  if (responseType === RESPONSE_TYPE.DIRECT) {
    global.REQUEST_DIRECT[request.requestId] = res;
    return null;
  }

  return res.send(request);
};

// ADVISE: check to delete
const apiSynthesisV3 = async (req, res) => {
  const { apiApp: app } = req;
  const { publicIP: ip } = req.__clientInfo;

  const {
    voice,
    inputText,
    username,
    key,
    voices,
    content,
    typeResponse,
    typeOutput,
    audioType,
    rate,
    bitRate,
    urlCallbackApi,
    httpCallback,
    dictionaryId,
    typeCampaign,
    inputType,
  } = req.body;

  let v3ApiType;
  if (key) v3ApiType = V3_API_TYPE.ENTERPRISE;
  if (username || dictionaryId) v3ApiType = V3_API_TYPE.PERSONAL;
  if (httpCallback) v3ApiType = V3_API_TYPE.ARTICLE;

  const responseType =
    typeResponse === V3_RESPONSE_TYPE.DIRECT
      ? RESPONSE_TYPE.DIRECT
      : RESPONSE_TYPE.INDIRECT;

  const v3VoiceCode = voice || voices[voices.length - 1]?.id;
  const voiceCode = mapVoices[v3VoiceCode] || v3VoiceCode;

  const request = await apiSynthesisService.handleApiSynthesisRequest({
    ip,
    app,
    text: inputText || content,
    callbackUrl: urlCallbackApi || httpCallback,
    responseType,
    voiceCode,
    audioType,
    outputType: typeOutput,
    bitrate: bitRate,
    speed:
      v3ApiType === V3_API_TYPE.ARTICLE
        ? voices[voices.length - 1]?.rate
        : rate,
    v3ApiType,
  });

  if (responseType === RESPONSE_TYPE.DIRECT) {
    global.REQUEST_DIRECT[request.requestId] = res;
    return null;
  }

  let responseData;
  // Response data for API ENTERPRISE and ARTICLE
  if (v3ApiType !== V3_API_TYPE.PERSONAL) {
    responseData = {
      errorCode: 1,
      errorMessage: 'Thành công',
      link: `${VBEE_URL}${API_V3_URL.GET_REQUEST}/${request.requestId}`,
    };
    return res.send(responseData);
  }
  const voiceInfo = await getVoiceByCode(voiceCode);
  const { userId: user } = app.members?.filter(
    (member) => member.role === APP_ROLE.ADMIN,
  )[0];
  // Response data for API PERSONAL
  responseData = {
    errorCode: 0,
    errorMsg: 'Success',
    error: [],
    info: [],
    data: {
      inputText,
      urlCallbackApi,
      typeOutput: typeOutput || OUTPUT_TYPE.LINK,
      typeCampaign: typeCampaign || 1,
      inputType,
      dictionaryId,
      applicationId: app._id,
      voice: voiceCode,
      bitRate,
      rate,
      audioType,
      money: request.price || 0,
      userId: user._id,
      // orderId: user.orderId,
      providerId: voiceInfo.provider,
      countText: request.characters,
      link: `${VBEE_URL}${API_V3_URL.GET_REQUEST}/${request.requestId}`,
      name: 'voice',
    },
  };
  return res.send(responseData);
};

/** tts-gate call to this handler */
const apiCallbackResponse = async (req, res) => {
  const {
    requestId: ttsRequestId,
    sessionId: requestId,
    status,
    audioLink,
    errorCode,
    errorMessage,
    timestampWords,
  } = req.body;

  const response = await apiSynthesisService.apiCallbackResponse({
    requestId,
    ttsRequestId,
    status,
    audioLink,
    errorCode,
    errorMessage,
    timestampWords,
  });

  return res.send(response);
};

/** tts-gate call to this handler */
const updateRequestProgress = async (req, res) => {
  const {
    requestId,
    userId,
    progress,
    status,
    index,
    subIndex,
    audioLink,
    phrases,
    error,
    tts,
  } = req.body;
  await apiSynthesisService.updateProgressRequest({
    requestId,
    userId,
    progress,
    status,
    index,
    subIndex,
    audioLink,
    phrases,
    error,
    tts,
  });
  res.send();
};

module.exports = {
  apiSynthesis,
  apiSynthesisV3,
  apiCallbackResponse,
  updateRequestProgress,
};
