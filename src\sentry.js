const Sentry = require('@sentry/node');
const { nodeProfilingIntegration } = require('@sentry/profiling-node');
const logger = require('./utils/logger');
const {
  SENTRY_DSN,
  SENTRY_TRACES_SAMPLE_RATE,
  SENTRY_PROFILES_SAMPLE_RATE,
  ENV,
  SENTRY_ENABLED,
} = require('./configs');

const initSentry = () => {
  if (SENTRY_ENABLED)
    Sentry.init({
      dsn: SENTRY_DSN,
      integrations: [nodeProfilingIntegration()],
      environment: ENV,

      tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,
      profilesSampleRate: SENTRY_PROFILES_SAMPLE_RATE,
    });

  logger.info('Sentry: Init successfully,', {
    ctx: 'Adapter.Sentry',
  });

  return Sentry;
};

module.exports = { initSentry };
