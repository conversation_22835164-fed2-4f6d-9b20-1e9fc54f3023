const axios = require('axios');
const camelCaseKeys = require('camelcase-keys');
const logger = require('./logger');
const { RandomFactory } = require('./random');

/** axios instance with predefined config and predefined business (logging, error processing, transforrm result props to camelCase) */
// @ts-ignore
const axiosInstance = axios.create({
  responseType: 'json',
  timeout: 10 * 1000,
});

// Add a request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Do something before request is sent
    /** correlationId */
    config.id = RandomFactory.getGuid();
    const originalUrl = config.baseURL
      ? `${config.baseURL}${config.url}`
      : config.url;

    logger.info(
      `[${config.id}] ${config.method.toUpperCase()} - ${originalUrl}`,
      { ctx: 'callApi.req' },
    );
    return config;
  },
  (error) => {
    // Do something with request error
    return Promise.reject(error);
  },
);

// Add a response interceptor
axiosInstance.interceptors.response.use(
  // Any status code that lie within the range of 2xx cause this function to trigger
  (response) => {
    const { config } = response;

    logger.info(`[${config.id}] ${JSON.stringify(response.data)}`, {
      ctx: 'callApi.res.success',
    });

    // Do something with response data
    return camelCaseKeys(response.data, { deep: true });
  },
  // Any status codes that falls outside the range of 2xx cause this function to trigger
  (error) => {
    const { config, response, message, stack } = error;

    if (response) {
      const { data } = response;
      const isShowData = !(
        data &&
        typeof data === 'string' &&
        data.match('<!DOCTYPE html>')
      );
      logger.error(
        `[${config.id}] ${
          data && isShowData ? JSON.stringify(response.data) : ''
        }`,
        { ctx: 'callApi.res.error', stack },
      );
    } else {
      logger.error(`[${config.id}] ${message}`, {
        ctx: 'callApi.res.error',
        stack,
      });
    }

    return Promise.reject(error);
  },
);

module.exports = axiosInstance;
