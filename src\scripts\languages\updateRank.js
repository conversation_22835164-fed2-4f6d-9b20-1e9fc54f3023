require('dotenv').config();
require('../../models');

const Language = require('../../models/language');
const logger = require('../../utils/logger');

global.logger = logger;

const updateRank = async () => {
  try {
    await Language.updateMany({ code: { $nin: [/^en/, /^vi/] } }, { rank: 9 });
    await Language.updateMany({ code: { $in: [/^vi/] } }, { rank: 1 });
    await Language.updateMany({ code: { $in: [/^en/] } }, { rank: 2 });
  } catch (error) {
    logger.error('Update rank to language failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update rank for language...`, {
    ctx: 'RunScript',
  });
  await updateRank();
  logger.info(`Update rank for language successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
