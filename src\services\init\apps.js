require('dotenv').config();
const logger = require('../../utils/logger');
const appDao = require('../../daos/app');

const initApps = async () => {
  // ADVISE: use Caching.wrap() instead of calling directly to DAO from initialization process is not good. Should move getAllApps() into AppService. Caching also should be a side effect of getApps.
  const apps = (await appDao.getAllApps()) || [];

  // ADVISE: PERFORMANCE: use mset
  await Promise.all(apps.map(appDao.storeAppToCache));

  logger.info('Apps: Init successfully,', { ctx: 'Service.Apps' });
};

module.exports = { initApps };
