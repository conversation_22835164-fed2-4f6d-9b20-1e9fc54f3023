const mongoose = require('mongoose');
const { VALID_CLIENT_PAUSE } = require('../constants/clientPause');

const ttsPresetSchema = new mongoose.Schema(
  {
    userId: String,
    name: String,
    audioType: String,
    backgroundMusic: {
      name: String,
      link: String,
      volume: Number,
    },
    speed: Number,
    voiceCode: String,
    clientPause: {
      paragraphBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
      sentenceBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
      majorBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
      mediumBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model('TtsPreset', ttsPresetSchema, 'tts_presets');
