// ADVISE: what is the difference between services/apiSynthesis and services/synthesis?

const {
  RESPONSE_TYPE,
  SYNTHESIS_TYPE,
  TTS_CORE_VERSION,
  REGEX,
  REQUEST_TYPE,
  REQUEST_STATUS,
  REDIS_KEY_PREFIX,
  AUDIO_DOMAIN_TYPE,
  SERVICE_TYPE,
  SME_PACKAGE_CODES,
  TTS_PROCESSING_STEPS,
} = require('../constants');
const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const { getVoiceByCode, getVersionVoice } = require('./voice');
const {
  validateText,
  getValidSampleRates,
  preCheckSynthesisApiRequest,
  countCredits,
} = require('./preprocessing');
const { createRequest, getTitle } = require('./request');
const { pushRequestToQueue } = require('./queue');
const { spendCharacters } = require('./characterProcessing');
const { checkVoicePermission } = require('../utils/tts');
const logger = require('../utils/logger');
const { RandomFactory } = require('../utils/random');
const { getAwsZone } = require('../daos/awsZone');
const {
  DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  MULTI_ZONE,
  DEFAULT_AUDIO_TYPE,
} = require('../configs');
const RequestCaching = require('../caching/requestCaching');
const ProcessingTimeCaching = require('../caching/processingTimeCaching');

const {
  handleUpdateProgressTTS,
  handleStreamAudio,
  handleTtsFailure,
  findUsedWordsInPronunciationDict,
  handleJoinAudiosFailureResponse,
  handleJoinAudiosSuccessResponse,
} = require('./ttsProcessing');

const { incrTotalInProgressRequests, incrTotalRequests } = require('./metrics');
const { updateUserById } = require('../daos/user');
const MoEngageAdapter = require('../adapters/moengage');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { findAdminApp } = require('./user');
const { findDictionary } = require('../daos/dictionary');
const { findClientPause } = require('../daos/clientPause');
const { DEFAULT_CLIENT_PAUSE } = require('../constants/clientPause');
const { getSynthesisComputePlatform } = require('./synthesisComputePlatform');
const { SynthesisService } = require('./SynthesisService');
const { getAwsZoneSynthesis } = require('./init/awsZone');

// ADVISE: IMPORTANT: this func is way TOO LONG. Must be splitted for easier maintaining
/** create TTSRequest, save to DB, ask tts-gate to process */
const handleApiSynthesisRequest = async ({
  ip,
  app,
  audioDomainType = AUDIO_DOMAIN_TYPE.S3,
  responseType = RESPONSE_TYPE.INDIRECT,
  outputType,
  v3ApiType,
  callbackUrl,
  text,
  voiceCode,
  sentences = [],
  audioType = DEFAULT_AUDIO_TYPE,
  bitrate = 128,
  sampleRate,
  speed = 1,
  backgroundMusic,
  fromVn,
  serviceType,
  clientUserId,
  returnTimestampWords,
}) => {
  incrTotalInProgressRequests();
  incrTotalRequests();
  const requestId = RandomFactory.getGuid();
  const requestCreatedAt = new Date();
  const startTime = Date.now();

  // ADVISE: all these logic and switching based on constants should be inside one func (getAwsZoneSynthesis() for example)
  const awsZoneSynthesis = MULTI_ZONE
    ? getAwsZoneSynthesis() || DEFAULT_AWS_REGION
    : DEFAULT_AWS_REGION;

  const synthesisType = SynthesisService.getSynthesisType({ text, sentences });
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) {
    // this text have ssml
    text = sentences.reduce((prev, curr) => prev + curr.text.trim(), '');
  }

  // Get user info for check get voice cloning
  const adminUser = await findAdminApp(app);

  const { voice, ttsCoreVersion } = await getVersionVoice({
    requestType: REQUEST_TYPE.API,
    synthesisType,
    voiceCode,
    text: undefined,
    userId: adminUser._id,
    // TODO: add apiUsageOptions
    userFeatures: undefined,
  });

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    voiceCode,
  });

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW && !useEmphasisV2)
    text = text.replace(
      REGEX.OLD_BREAK_TIME,
      (p1, p2) => `${`<break time="${p2}s"/>`}`,
    );

  if (useEmphasisV2)
    text = text.replace(
      REGEX.NEW_BREAK_TIME,
      (p1, p2) => `${`<break time=${p2}s/>`}`,
    );

  const {
    appId,
    userId,
    features,
    price,
    packageCode,
    retentionPeriod,
    concurrentRequest,
    textLength,
    user,
    limitCredits,
  } = await preCheckSynthesisApiRequest({ app, text, ttsCoreVersion });

  // TODO: move count credit to preCheckSynthesisApiRequest
  const credits = await countCredits({
    sentences: sentences.length ? sentences : [{ text, voiceCode }],
    ssmlRegex:
      ttsCoreVersion === TTS_CORE_VERSION.NEW ? REGEX.ADVANCE_TAG : undefined,
    user: { userId, packageCode },
  });

  if (credits > limitCredits && !SME_PACKAGE_CODES.includes(packageCode))
    throw new CustomError(code.TEXT_TOO_LONG);

  const useReturnTimestamp = getFeatureValue(FEATURE_KEYS.TIMESTAMP_WORDS, {
    userId,
    appId,
  });

  const configStorage = getFeatureValue(FEATURE_KEYS.CONFIG_STORAGE_TTS, {
    userId,
    ip,
  });

  const { bucket: gcsBucket = {} } = configStorage?.gcs || {};

  if (!user?.firstConvertAt) {
    updateUserById(userId, { firstConvertAt: new Date() }).catch((error) => {
      logger.error(error, { ctx: 'UpdateFirstConvertAt' });
    });
  }

  const awsZone = await getAwsZone({ region: awsZoneSynthesis });
  const {
    normalizerFunction = DEFAULT_NORMALIZER_FUNCTION,
    sentenceTokenizerFunction = DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
    newSentenceTokenizerFunction = DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
    textToAllophoneFunction = DEFAULT_T2A_FUNCTION,
    synthesisFunction = DEFAULT_SYNTHESIS_FUNCTION,
    joinSentencesFunction = DEFAULT_JOIN_AUDIO_FUNCTION,
    defaultS3Bucket = DEFAULT_BUCKET_S3,
    s3Buckets = {},
  } = awsZone;

  // find in-used pronunciationDict for this text
  const pronunciationDict = await findDictionary(userId);
  const usedWordsInPronunciationDict = findUsedWordsInPronunciationDict(
    text,
    pronunciationDict?.words || [],
  );

  const clientPause = await findClientPause(userId);
  const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
    clientPause || {};

  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) {
    const voiceCodes = sentences.reduce((acc, curr) => {
      if (curr.voiceCode && !acc.includes(curr.voiceCode))
        return [...acc, curr.voiceCode];
      return acc;
    }, []);

    const { sampleRates, maxSampleRate } = await getValidSampleRates(
      voiceCodes,
    );
    if (!maxSampleRate || (sampleRate && !sampleRates.includes(sampleRate)))
      throw new CustomError(
        code.INVALID_SAMPLE_RATE,
        `This voice only support sample rate of ${JSON.stringify(sampleRates)}`,
      );

    sentences = await Promise.all(
      sentences.map(async (sentence) => {
        // ADVISE: PERFORMANCE: this VoiceInfo already fetched somewhere else above. We should attach sentence-voice-samplesrate in the beginning
        const sentenceVoice = await getVoiceByCode(sentence.voiceCode);
        if (!sentenceVoice) throw new CustomError(code.INVALID_VOICE_CODE);

        if (sentenceVoice.version === TTS_CORE_VERSION.NEW)
          throw new CustomError(code.SENTENCES_NOT_SUPPORT_EMPHASIS);
        if (!checkVoicePermission(features, sentenceVoice.features))
          throw new CustomError(code.UNAVAILABLE_VOICE);

        // ADVISE: search all `active === false`, create VoiceService.isActive()
        if (sentenceVoice?.active === false)
          throw new CustomError(code.INACTIVE_VOICE);

        const isValidText = validateText({
          text: sentence.text,
          voiceProvider: sentenceVoice.provider,
          ttsCoreVersion,
        });
        if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

        return {
          ...sentence,
          characters: SynthesisService.countSentenceTextLength(
            sentence.text,
            ttsCoreVersion,
          ),
        };
      }),
    );

    const firstVoice = await getVoiceByCode(sentences[0].voiceCode);
    const request = {
      ip,
      requestId,
      title: getTitle(sentences[0].text, firstVoice.languageCode), // TODO: remove title
      sentences,
      characters: textLength,
      credits,
      audioType,
      createdAt: requestCreatedAt,
      status: REQUEST_STATUS.IN_PROGRESS,
      voiceCode: sentences[0].voiceCode,
      bitrate,
      sampleRate: sampleRate ? sampleRate.toString() : maxSampleRate.toString(),
      retentionPeriod,
      version: ttsCoreVersion,
      app: appId,
      userId,
      callbackUrl,
      pronunciationDict: usedWordsInPronunciationDict,
      clientPause: {
        paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
        sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
        majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
        mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
      },
      responseType,
      audioDomainType,
      outputType,
      v3ApiType,
      type: REQUEST_TYPE.API,
      packageCode,
      price: SME_PACKAGE_CODES.includes(packageCode) ? price : 0,
      fromVn,
      serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
      clientUserId,
      awsZoneSynthesis,
      awsZoneFunctions: {
        normalizerFunction,
        sentenceTokenizerFunction,
        newSentenceTokenizerFunction,
        textToAllophoneFunction,
        synthesisFunction,
        joinSentencesFunction,
        s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
      },
      googleCloudStorage: {
        bucket: gcsBucket[retentionPeriod],
      },
      returnTimestampWords: useReturnTimestamp ? returnTimestampWords : false,
    };

    const isRealTime = getFeatureValue(FEATURE_KEYS.API_REAL_TIME, {
      voiceCode: sentences[0]?.voiceCode,
      characters: textLength,
    });
    if (isRealTime) request.isRealTime = true;

    if (backgroundMusic) request.backgroundMusic = backgroundMusic;
    await createRequest(request);

    request.voice = firstVoice; // ADVISE: seem to be unused, only for displaying info, not business logic

    // ADVISE: BUSINESS: explain caching mechanism of request and sentences
    const cacheSentences = sentences.map((sentence) => {
      // ADVISE: textSentence is unused?
      const { text: textSentence, ...cacheSentence } = sentence;
      return cacheSentence;
    });
    const cacheRequest = { ...request, sentences: cacheSentences, progress: 0 };
    const sentenceKeys = Array.from(Array(sentences.length).keys()).map(
      (index) => `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`,
    );
    cacheRequest.sentenceKeys = sentenceKeys;
    cacheRequest.numberOfIndexSentences = sentences.length;
    cacheRequest.numberOfSentences = sentences.length;
    // ADVISE: duplicated code in this cache request into Redis (synthesis)
    // ADVISE: duplicated code in this cache request into Redis (apiSynthesis) current file, line 479
    await RequestCaching.createRequestInRedis(cacheRequest);
    ProcessingTimeCaching.saveStepProcessingTime({
      requestId,
      step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
      startTime,
      additionalFields: {
        userId,
        startTime,
        characters: textLength,
        pushToQueueAt: Date.now(),
      },
    });

    const processRequestStatus = await pushRequestToQueue({
      userId,
      requestId,
      ccr: concurrentRequest,
      requestType: REQUEST_TYPE.API,
    });
    if (!processRequestStatus)
      throw new CustomError(
        code.INTERNAL_SERVER_ERROR,
        'Cannot push request to queue',
      );

    if (!SME_PACKAGE_CODES.includes(packageCode))
      spendCharacters({
        userId,
        requestId,
        characters: credits || textLength,
      });

    MoEngageAdapter.sendEventCreateRequest({
      customerId: userId,
      request,
    });

    return {
      appId,
      requestId,
      characters: textLength,
      voiceCode,
      audioType,
      speedRate: speed,
      bitrate,
      sampleRate: request.sampleRate,
      backgroundMusic,
      status: REQUEST_STATUS.IN_PROGRESS,
    };
  }

  if (!checkVoicePermission(features, voice.features))
    throw new CustomError(code.UNAVAILABLE_VOICE);

  const isValidText = validateText({
    text,
    voiceProvider: voice.provider,
    ttsCoreVersion,
  });
  if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

  if (sampleRate && !voice.sampleRates.includes(sampleRate))
    throw new CustomError(
      code.INVALID_SAMPLE_RATE,
      `This voice only support sample rate of ${JSON.stringify(
        voice.sampleRates,
      )}`,
    );

  const request = {
    ip,
    requestId,
    title: getTitle(text, voice.languageCode), // TODO: remove title
    text,
    characters: textLength,
    credits,
    audioType,
    speed,
    pronunciationDict: usedWordsInPronunciationDict,
    clientPause: {
      paragraphBreak: paragraphBreak || DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
      sentenceBreak: sentenceBreak || DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
      majorBreak: majorBreak || DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
      mediumBreak: mediumBreak || DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
    },
    createdAt: requestCreatedAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: sampleRate
      ? sampleRate.toString()
      : voice.defaultSampleRate.toString(),
    retentionPeriod,
    version: ttsCoreVersion,
    app: appId,
    userId,
    callbackUrl,
    audioDomainType,
    responseType,
    outputType,
    v3ApiType,
    type: REQUEST_TYPE.API,
    packageCode,
    price: SME_PACKAGE_CODES.includes(packageCode) ? price : 0,
    fromVn,
    serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
    clientUserId,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
    googleCloudStorage: {
      bucket: gcsBucket[retentionPeriod],
    },
    returnTimestampWords: useReturnTimestamp ? returnTimestampWords : false,
  };

  const isRealTime = getFeatureValue(FEATURE_KEYS.API_REAL_TIME, {
    voiceCode,
    characters: textLength,
  });
  if (isRealTime) request.isRealTime = true;

  if (backgroundMusic) request.backgroundMusic = backgroundMusic;

  request.synthesisComputePlatform = getSynthesisComputePlatform(request);

  await createRequest(request);

  const { text: textRequest, ...cacheRequest } = request;
  cacheRequest.numberOfSentences = 0;
  cacheRequest.voice = voice;
  cacheRequest.progress = 0;
  cacheRequest.sentenceKeys = [
    `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`,
  ];
  cacheRequest.numberOfIndexSentences = 1;
  // ADVISE: duplicated code in this cache request into Redis (same in services/Synthesis 662)
  await RequestCaching.createRequestInRedis(cacheRequest);
  ProcessingTimeCaching.saveStepProcessingTime({
    requestId,
    step: TTS_PROCESSING_STEPS.PRE_PROCESSING,
    startTime,
    additionalFields: {
      userId,
      startTime,
      characters: textLength,
      pushToQueueAt: Date.now(),
    },
  });

  const processRequestStatus = await pushRequestToQueue({
    userId,
    requestId,
    ccr: concurrentRequest,
    requestType: REQUEST_TYPE.API,
  });
  if (!processRequestStatus)
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Cannot push request to queue',
    );

  if (!SME_PACKAGE_CODES.includes(packageCode))
    spendCharacters({
      userId,
      requestId,
      characters: credits || textLength,
    });

  MoEngageAdapter.sendEventCreateRequest({
    customerId: userId,
    request,
  });

  return {
    appId,
    requestId,
    characters: textLength,
    voiceCode,
    audioType,
    speedRate: speed,
    bitrate,
    sampleRate: request.sampleRate,
    backgroundMusic,
    status: REQUEST_STATUS.IN_PROGRESS,
  };
};

const apiCallbackResponse = async ({
  requestId,
  ttsRequestId,
  status,
  audioLink,
  errorCode,
  errorMessage: error,
  timestampWords,
}) => {
  if (status) {
    handleJoinAudiosSuccessResponse({
      requestId,
      ttsRequestId,
      audioLink,
      timestampWords,
    }).catch((err) => {
      logger.error(err, { ctx: 'HandleJoinAudiosSuccessResponse', requestId });
    });
  } else {
    handleJoinAudiosFailureResponse({
      requestId,
      ttsRequestId,
      error,
      errorCode,
    }).catch((err) => {
      logger.error(err, { ctx: 'HandleJoinAudiosFailureResponse', requestId });
    });
  }
};

const updateProgressRequest = async ({
  requestId,
  userId,
  progress,
  status,
  index,
  subIndex,
  audioLink,
  phrases,
  error,
  tts,
}) => {
  const request = await RequestCaching.findRequestByIdInRedis(requestId);
  const { progress: currentProgress } = request;
  if (currentProgress >= progress) return;
  if (status === REQUEST_STATUS.FAILURE) {
    handleTtsFailure({
      request,
      errorCode: code.TTS_FAILURE,
      errorMessage: error,
    });
    return;
  }

  handleUpdateProgressTTS({ requestId, userId, progress });
  if (request.demo)
    handleStreamAudio({ requestId, index, subIndex, audioLink, phrases, tts });
};

module.exports = {
  handleApiSynthesisRequest,
  apiCallbackResponse,
  updateProgressRequest,
};
