require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');
const { VOICE_PROVIDER } = require('../../constants');
const { generateMSVoiceSampleRates } = require('../../utils/sampleRate');
const msChineseLanguage = require('../languages/chineseLanguages.json');
const {
  getMicrosoftVoices,
  getVoiceFeatures,
  getRoundImage,
  getSquareImage,
} = require('../shared');

global.logger = logger;

const getLanguageCodes = () => {
  const languageCodes = msChineseLanguage.map((language) => language.code);
  return languageCodes;
};

const createMicrosoftVoices = async () => {
  const voices = await getMicrosoftVoices();

  const languageCodes = getLanguageCodes();

  const usedVoices = [];

  for (const voice of voices) {
    if (languageCodes.includes(voice.Locale)) {
      usedVoices.push(voice);
    }
  }

  const microsoftVoices = usedVoices.map((voice) => ({
    code: voice.ShortName,
    name: voice.DisplayName,
    gender: voice.Gender?.toLowerCase(),
    languageCode: voice.Locale,
    type: voice.VoiceType,
    provider: VOICE_PROVIDER.MICROSOFT,
    rank: 999,
    active: true,
    defaultSampleRate: voice.SampleRateHertz,
    sampleRates: generateMSVoiceSampleRates(voice.SampleRateHertz),
    level: voice.VoiceType === 'Standard' ? 'STANDARD' : 'PREMIUM',
    squareImage: getSquareImage(voice.Gender),
    roundImage: getRoundImage(voice.Gender),
    features: getVoiceFeatures(voice.VoiceType),
    demo: `https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/microsoft/${voice.ShortName}.mp3`,
  }));

  const microsoftVoicesCreated = await Voice.insertMany(microsoftVoices);
  logger.info(`Created ${microsoftVoicesCreated.length} Microsoft voices`);
};

(async () => {
  await createMicrosoftVoices();
  process.exit(1);
})();
