const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const backgroundMusicController = require('../controllers/backgroundMusic');
const { auth } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.post('/background-musics', auth, asyncMiddleware(backgroundMusicController.createBackgroundMusic));
router.get('/background-musics', auth, asyncMiddleware(backgroundMusicController.getBackgroundMusics));
/* eslint-disable prettier/prettier */

module.exports = router;
