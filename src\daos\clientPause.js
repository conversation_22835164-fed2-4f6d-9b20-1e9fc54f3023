const daoUtils = require('./utils');
const ClientPause = require('../models/clientPause');

/** Find in DB. ClientPause is a mapping, comma wait 0.2s, dot wait 0.45s, semicolon wait 0.3s, newline wait 0.6s */
const findClientPause = async (userId) => {
  const clientPause = await daoUtils.findOne(ClientPause, { userId });
  return clientPause;
};

const createClientPause = async (createFields) => {
  const clientPause = await ClientPause.create(createFields);
  return clientPause;
};

const updateClientPause = async (id, updateFields) => {
  const clientPause = await ClientPause.findByIdAndUpdate(id, updateFields, {
    new: true,
  });
  return clientPause;
};

module.exports = { findClientPause, createClientPause, updateClientPause };
