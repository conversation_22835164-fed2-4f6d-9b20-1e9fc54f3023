const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const dictionaryController = require('../controllers/dictionary');
const { auth } = require('../middlewares/auth');
const { dictionaryValidate } = require('../validations/dictionary');

/* eslint-disable prettier/prettier */
router.get('/dictionary', auth, asyncMiddleware(dictionaryController.getDictionary));
router.post('/dictionary', auth, dictionaryValidate, asyncMiddleware(dictionaryController.createDictionary));
/* eslint-disable prettier/prettier */

module.exports = router;
