const mongoose = require('mongoose');
const { PACKAGE_TYPE } = require('../constants');

const orderSchema = new mongoose.Schema(
  {
    _id: String,
    userId: String,
    type: { type: String, enum: Object.values(PACKAGE_TYPE) },
    activePackage: { type: Boolean, default: false },
    payOrder: { type: Boolean, default: false },
    cancelOrder: { type: Boolean, default: false },
    lastResetBonusAt: { type: Date, default: Date.now },
    lastResetBonusDubbingAt: { type: Date, default: Date.now },
    lastResetMonthly: Date,
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model('Order', orderSchema);
