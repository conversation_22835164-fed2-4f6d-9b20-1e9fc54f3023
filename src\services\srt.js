const callApi = require('../utils/callApi');
const { getAccessToken } = require('./iam');
const { VBEE_DUBBING_URL } = require('../configs');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const API_TIMEOUT = 15 * 60 * 1000; // 5 minutes

const extractSrtFromYoutube = async (
  link,
  originalLanguage,
  isTranslate,
  userId,
) => {
  const accessToken = await getAccessToken();

  try {
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/srt/youtube`,
      method: 'POST',
      data: { link, originalLanguage, isTranslate },
      headers: { Authorization: `Bearer ${accessToken}` },
      timeout: API_TIMEOUT,
    });

    if (status !== 1) throw new Error();
    return result.content;
  } catch (err) {
    logger.error(err, { ctx: 'ExtractSrtFromYoutube', userId });
    throw new CustomError(
      code.VBEE_DUBBING_ERROR,
      'Failed to extract srt content from youtube link',
    );
  }
};

const extractSrtFromLocal = async (link) => {
  const accessToken = await getAccessToken();

  try {
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/srt/video`,
      method: 'POST',
      data: { link },
      headers: { Authorization: `Bearer ${accessToken}` },
      timeout: API_TIMEOUT,
    });

    if (status !== 1) throw new Error();
    return result.content;
  } catch (err) {
    logger.error(err, { ctx: 'ExtractSrtFromLocal' });
    throw new CustomError(
      code.VBEE_DUBBING_ERROR,
      'Failed to extract srt content from video link',
    );
  }
};

module.exports = {
  extractSrtFromYoutube,
  extractSrtFromLocal,
};
