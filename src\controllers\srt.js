const { DUBBING_SOURCE } = require('../constants/dubbing');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const srtService = require('../services/srt');

const extractSrtContentFromLink = async (req, res) => {
  const { link, source, originalLanguage, isTranslate } = req.body;
  const { userId } = req.user;
  let content = {};

  switch (source) {
    case DUBBING_SOURCE.YOUTUBE:
      content = await srtService.extractSrtFromYoutube(
        link,
        originalLanguage,
        isTranslate,
        userId,
      );
      break;
    case DUBBING_SOURCE.LOCAL:
      content = await srtService.extractSrtFromLocal(link);
      break;
    default:
      throw new CustomError(errorCodes.BAD_REQUEST, 'Invalid source');
  }

  return res.send({ content });
};

module.exports = { extractSrtContentFromLink };
