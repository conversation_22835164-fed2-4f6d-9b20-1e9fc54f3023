const { Joi, validate } = require('express-validation');
const { RESPONSE_TYPE } = require('../constants');

const apiStt = {
  body: Joi.object({
    responseType: Joi.string()
      .valid(...Object.values(RESPONSE_TYPE))
      .required(),
    callbackUrl: Joi.string()
      .uri()
      .trim()
      .when('responseType', {
        is: Joi.string().valid(RESPONSE_TYPE.INDIRECT),
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    fileUrl: Joi.string().trim().optional(),
  }).required(),
};

module.exports = {
  apiSttValidate: validate(apiStt, { keyByField: true }),
};
