const mongoose = require('mongoose');

const processingTimeSchema = new mongoose.Schema(
  {
    _id: String,
    userId: String,
    characters: Number,
    preProcessingTime: Number,
    queueTime: Number,
    sentenceTokenizerTime: Number,
    synthesisTime: Number,
    audioJoinerTime: Number,
    avgTotalTime: Number,
    totalTime: Number,
    status: String,
  },
  { _id: false, versionKey: false, timestamps: true },
);

module.exports = mongoose.model('ProcessingTime', processingTimeSchema);
