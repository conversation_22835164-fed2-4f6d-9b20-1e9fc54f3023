const { Constants } = require('@vbee-holding/vbee-tts-models');
const { Joi, validate } = require('express-validation');
const { VALID_CLIENT_PAUSE } = require('../constants/clientPause');
const { MIN_NAME_LENGTH, MAX_NAME_LENGTH } = require('../constants/ttsPreset');

const createTtsPreset = {
  body: Joi.object({
    name: Joi.string().min(MIN_NAME_LENGTH).max(MAX_NAME_LENGTH).required(),
    audioType: Joi.string()
      .valid(...Object.values(Constants.AUDIO_TYPE))
      .required(),
    backgroundMusic: Joi.object({
      name: Joi.string().required(),
      link: Joi.string().uri().required(),
      volume: Joi.number().min(0).max(100).required(),
    }).required(),
    speed: Joi.number().min(0).max(100).required(),
    voiceCode: Joi.string().required(),
    clientPause: Joi.object({
      paragraphBreak: Joi.number()
        .min(0)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      sentenceBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      majorBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
      mediumBreak: Joi.number()
        .min(VALID_CLIENT_PAUSE.MIN)
        .max(VALID_CLIENT_PAUSE.MAX)
        .optional(),
    }).required(),
  }),
};

module.exports = {
  createTtsPresetValidate: validate(createTtsPreset, { keyByField: true }),
};
