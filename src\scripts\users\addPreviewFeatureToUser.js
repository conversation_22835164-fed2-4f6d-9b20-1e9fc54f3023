require('dotenv').config();
require('../../models');

const User = require('../../models/user');
const logger = require('../../utils/logger');

global.logger = logger;

const PREVIEW_PACKAGE_CODE = [
  'STUDIO-PRO-MONTH',
  'STUDIO-PRO-YEAR',
  'STUDIO-ADV-MONTH',
  'STUDIO-ADV-MONTH',
  'STUDIO-TRIAL',
];

const addPreviewFeature = async () => {
  logger.info(`Starting add preview feature`, { ctx: 'RunScript' });

  await User.updateMany(
    { packageCode: { $in: PREVIEW_PACKAGE_CODE } },
    { $push: { features: 'preview' } },
  );

  logger.info(`Add preview feature successfully`, { ctx: 'RunScript' });
};

(async () => {
  await addPreviewFeature();
  process.exit(1);
})();
