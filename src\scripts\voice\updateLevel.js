require('dotenv').config();

const logger = require('../../utils/logger');

global.logger = logger;

require('../../models');
const Voice = require('../../models/voice');
const { VOICE_PROVIDER, VOICE_LEVEL } = require('../../constants');
const {
  GLOBAL_VOICE_FEATURES,
  VIETNAM_VOICE_FEATURES,
} = require('../../constants/voice');

// Vbee
const updateVbeeVoices = async () => {
  logger.info('Updating...', { ctx: 'UpdateVbeeVoices' });

  const ADVANCED_VOICE_CODES = ['hn_male_thanhlong_talk_48k-f5'];

  await Voice.updateMany(
    { provider: VOICE_PROVIDER.VBEE, code: { $nin: ADVANCED_VOICE_CODES } },
    {
      $set: {
        level: VOICE_LEVEL.BASIC,
        features: [VIETNAM_VOICE_FEATURES.BASIC],
      },
    },
  );

  await Voice.updateMany(
    { provider: VOICE_PROVIDER.VBEE, code: { $in: ADVANCED_VOICE_CODES } },
    {
      $set: {
        level: VOICE_LEVEL.ADVANCED,
        features: [VIETNAM_VOICE_FEATURES.ADVANCED],
      },
    },
  );

  logger.info('Done', { ctx: 'UpdateVbeeVoices' });
};

// Google
const updateGoogleVoices = async () => {
  logger.info('Updating...', { ctx: 'UpdateGoogleVoices' });

  // Add BASIC level for Standard voices
  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.GOOGLE,
      code: new RegExp(/Standard/),
    },
    {
      $set: {
        level: VOICE_LEVEL.BASIC,
        features: [GLOBAL_VOICE_FEATURES.BASIC],
      },
    },
  );

  // Add ADVANCED level for Non-Standard voices
  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.GOOGLE,
      code: { $not: new RegExp(/Standard/) },
    },
    {
      $set: {
        level: VOICE_LEVEL.ADVANCED,
        features: [GLOBAL_VOICE_FEATURES.ADVANCED],
      },
    },
  );

  logger.info('Done', { ctx: 'UpdateGoogleVoices' });
};

// Amazon
const updateAmazonVoices = async () => {
  logger.info('Updating...', { ctx: 'UpdateAmazonVoices' });

  const ONLY_ADVANCED_VOICE_CODES = ['Aria', 'Ayanda', 'Olivia'];

  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.AMAZON,
      code: { $nin: ONLY_ADVANCED_VOICE_CODES },
    },
    {
      $set: {
        level: VOICE_LEVEL.BASIC,
        features: [GLOBAL_VOICE_FEATURES.BASIC],
      },
    },
  );

  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.AMAZON,
      code: { $in: ONLY_ADVANCED_VOICE_CODES },
    },
    {
      $set: {
        level: VOICE_LEVEL.ADVANCED,
        features: [GLOBAL_VOICE_FEATURES.ADVANCED],
      },
    },
  );

  logger.info('Done', { ctx: 'UpdateAmazonVoices' });
};

// Microsoft
const updateMicrosoftVoices = async () => {
  logger.info('Updating...', { ctx: 'UpdateMicrosoftVoices' });
  await Voice.updateMany(
    { provider: VOICE_PROVIDER.MICROSOFT },
    {
      $set: {
        level: VOICE_LEVEL.ADVANCED,
        features: [GLOBAL_VOICE_FEATURES.ADVANCED],
        active: false,
      },
    },
  );
  logger.info('Done', { ctx: 'UpdateMicrosoftVoices' });
};

const main = async () => {
  try {
    await updateVbeeVoices();
    await updateGoogleVoices();
    await updateAmazonVoices();
    await updateMicrosoftVoices();
  } catch (error) {
    logger.error(error);
  } finally {
    process.exit(0);
  }
};

main();
