const mongoose = require('mongoose');
const { Constants } = require('@vbee-holding/vbee-tts-models');
const { PRODUCT_TYPE } = require('../constants');

const projectSchema = mongoose.Schema(
  {
    userId: { type: String, required: true },
    title: { type: String, required: true },
    product: {
      type: String,
      required: true,
      enum: Object.values(PRODUCT_TYPE),
    },
    blocks: [
      {
        _id: String,
        text: String,
        voiceCode: String,
        characters: Number,
        breakTime: Number,
        speed: Number,
        elements: [
          {
            _id: false,
            key: String,
            text: String,
            startOffset: Number,
            endOffset: Number,
            name: String,
            value: String,
          },
        ],
        audioLink: String,
        requestId: String,
        status: String,
        audioExpiredAt: Date,
        isSample: { type: Boolean, default: false },
        sampleBlockId: String,
      },
    ],
    isDeleted: { type: Boolean, default: false },
    sampleRate: { type: Number, default: 16000 },
    bitrate: { type: Number, default: 128 },
    audioType: { type: String, default: Constants.AUDIO_TYPE.MP3 },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('Project', projectSchema);
