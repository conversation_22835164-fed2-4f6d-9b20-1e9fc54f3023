require('dotenv').config();
require('../../models');
require('../../services/kafka/producer');

const logger = require('../../utils/logger');
const Request = require('../../models/request');
const { REQUEST_STATUS } = require('../../constants');
const { updateUserRedis, refundCharacters } = require('../shared');

global.logger = logger;

const getUserRequests = async () => {
  const failureRequests = await Request.aggregate([
    { $match: { status: REQUEST_STATUS.FAILURE, refund: false } },
    {
      $group: {
        _id: { userId: '$userId', demo: '$demo', type: '$type' },
        numOfRequests: { $sum: 1 },
        requests: {
          $addToSet: {
            _id: '$_id',
            characters: '$characters',
            packageCode: '$packageCode',
          },
        },
      },
    },
  ]);

  const userRequests = failureRequests.reduce((acc, item) => {
    const { userId, demo, type } = item._id;
    const { numOfRequests, requests } = item;

    if (userId && !demo)
      return [...acc, { userId, numOfRequests, requests, type }];
    return acc;
  }, []);

  return userRequests;
};

(async () => {
  logger.info('Start refund failure characters for user...', {
    ctx: 'RunScript',
  });

  const userRequests = await getUserRequests();
  logger.info(`Get user requests success: ${userRequests.length}`, {
    ctx: 'RunScript',
  });

  await updateUserRedis(userRequests);
  logger.info(`Update User in Cache success`, { ctx: 'RunScript' });

  // Refund characters
  await refundCharacters(userRequests);
  logger.info(`Refund characters success`, { ctx: 'RunScript' });

  logger.info('Refund failure characters for user successfully', {
    ctx: 'RunScript',
  });
})();
