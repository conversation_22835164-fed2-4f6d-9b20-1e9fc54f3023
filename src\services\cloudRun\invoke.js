const { getIdToken } = require('./auth');
const callApi = require('../../utils/callApi');

const TIMEOUT = 5 * 60 * 1000;

const callApiToCloudRun = async (cloudRunUrl, payload) => {
  const idToken = await getIdToken(cloudRunUrl);

  const response = await callApi({
    method: 'POST',
    url: cloudRunUrl,
    headers: {
      Authorization: `Bearer ${idToken}`,
    },
    data: payload,
    timeout: TIMEOUT,
  });

  return response;
};

module.exports = callApiToCloudRun;
