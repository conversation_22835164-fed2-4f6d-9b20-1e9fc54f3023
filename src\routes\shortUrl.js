const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const shortUrlController = require('../controllers/shortUrl');
const { shortUrlValidate } = require('../validations/shortUrl');

/* eslint-disable prettier/prettier */
router.get('/s/:requestId/:token', shortUrlValidate, asyncMiddleware(shortUrlController.handleAudioUrl));
/* eslint-disable prettier/prettier */

module.exports = router;
