const mung = require('express-mung');
const { API_V3_URL } = require('../constants');

const successReq = () => {
  return mung.json((body, req) => {
    const { url } = req;
    const isErrorResponse = body.error_code || body.error_message;
    const isV3Url =
      url === API_V3_URL.API || url.includes(API_V3_URL.GET_REQUEST);
    if (isErrorResponse || isV3Url) return body;
    return { status: 1, result: { ...body } };
  });
};

module.exports = successReq;
