const moment = require('moment');
const { VietnameseHelper } = require('@vbee-holding/vbee-node-shared-lib');
const { getFeatureValue } = require('./growthbook');
const { copyToS3 } = require('./s3');
const { copyGCStoGCSWithRetry } = require('./googleCloudStorage');
const { getPackageUsageOptions } = require('./package');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { STORAGE } = require('../constants/cloudStorage');
const {
  MULTI_ZONE,
  DEFAULT_AWS_REGION,
  DEFAULT_BUCKET_S3,
} = require('../configs');
const { getAwsZone } = require('../daos/awsZone');
const { findUser } = require('../daos/user');

const getAudioName = (title, requestId, userId) => {
  const titleWithoutSpecialCharacter = VietnameseHelper.stripAccents(
    title,
  ).replace(/[^a-zA-Z0-9 ]/g, '');
  const isNewFileName = getFeatureValue(FEATURE_KEYS.NEW_AUDIO_FILE_NAME, {
    userId,
  });

  let fileName = titleWithoutSpecialCharacter
    .trim()
    .replace(/\s+/g, '_')
    .toLowerCase();
  fileName = fileName || requestId;

  fileName = isNewFileName
    ? `${requestId}/${fileName}`
    : `${fileName}_${requestId}`;

  return fileName;
};

const handleCopyAudioS3ToS3 = async (originalAudioLink, request) => {
  const {
    _id: requestId,
    title,
    audioType,
    awsZoneSynthesis,
    awsZoneFunctions = {},
  } = request;

  const { s3Bucket: bucket } = awsZoneFunctions;
  const audioName = getAudioName(title, requestId, request.userId);
  const today = moment().format('YYYY/MM/DD');
  const key = `synthesis/${today}/${audioName}.${audioType}`;

  const audioLink = await copyToS3({
    bucket,
    key,
    url: originalAudioLink,
    awsZoneSynthesis,
  });
  return audioLink;
};

const handleCopyAudioGcsToGcs = async (originalAudioLink, request) => {
  const { _id: requestId, title, audioType, googleCloudStorage = {} } = request;

  const { bucket } = googleCloudStorage;
  const audioName = getAudioName(title, requestId, request.userId);
  const today = moment().format('YYYY/MM/DD');
  const key = `synthesis/${today}/${audioName}.${audioType}`;

  const audioLink = await copyGCStoGCSWithRetry({
    bucket,
    key,
    url: originalAudioLink,
  });
  return audioLink;
};

const detectCloudStorageService = (audioLink) => {
  if (audioLink.includes('amazonaws.com')) return STORAGE.S3;
  if (audioLink.includes('googleapis.com')) return STORAGE.GCS;
  throw new Error(`Unsupported audio link service: ${audioLink}`);
};

const transferHandlers = {
  [STORAGE.S3]: handleCopyAudioS3ToS3,
  [STORAGE.GCS]: handleCopyAudioGcsToGcs,
};

const copyCloudFile = async (originalAudioLink, request) => {
  const cloudStorageFrom = detectCloudStorageService(originalAudioLink);
  const handler = transferHandlers[cloudStorageFrom];

  const audioLink = await handler(originalAudioLink, request);

  return audioLink;
};

const copyLinkToDestinationStorage = async ({
  sourceLink,
  destinationKey,
  userId,
}) => {
  const cloudStorageFrom = detectCloudStorageService(sourceLink);

  const user = await findUser({ _id: userId });
  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });
  const { retentionPeriod } = studioUsageOptions || {};

  let audioLink = sourceLink;
  if (cloudStorageFrom === STORAGE.S3) {
    const getAwsZoneSynthesis = () => {
      const lengthAwsZones = AWS_ZONES_TTS_STUDIO.length;
      const randomIndex = Math.floor(Math.random() * lengthAwsZones);
      const awsZone = AWS_ZONES_TTS_STUDIO[randomIndex];
      return awsZone;
    };
    const awsZoneSynthesis = MULTI_ZONE
      ? getAwsZoneSynthesis() || DEFAULT_AWS_REGION
      : DEFAULT_AWS_REGION;
    const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};
    const s3Bucket = awsZone?.s3Buckets?.[retentionPeriod] || DEFAULT_BUCKET_S3;

    audioLink = await copyToS3({
      bucket: s3Bucket,
      key: destinationKey,
      url: sourceLink,
      awsZoneSynthesis,
    });
  }

  if (cloudStorageFrom === STORAGE.GCS) {
    const configStorage = getFeatureValue(FEATURE_KEYS.CONFIG_STORAGE_TTS, {
      userId,
    });
    const { bucket: gcsBucket = {} } = configStorage?.gcs || {};
    const GCSBucket = gcsBucket[retentionPeriod];

    audioLink = await copyGCStoGCSWithRetry({
      bucket: GCSBucket,
      key: destinationKey,
      url: sourceLink,
    });
  }

  return audioLink;
};

module.exports = {
  getAudioName,
  copyCloudFile,
  detectCloudStorageService,
  copyLinkToDestinationStorage,
};
