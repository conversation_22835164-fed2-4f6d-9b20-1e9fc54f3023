const { ObjectId } = require('mongoose').Types;
const logger = require('../../utils/logger');

const updateOne = async (model, condition, updateFields) => {
  try {
    if (ObjectId.isValid(condition)) {
      const document = await model.findByIdAndUpdate(condition, updateFields, {
        new: true,
        omitUndefined: true,
      });
      return document;
    }

    if (typeof condition === 'object' && condition !== null) {
      const document = await model.findOneAndUpdate(condition, updateFields, {
        new: true,
        omitUndefined: true,
      });
      return document;
    }

    throw new Error();
  } catch (error) {
    logger.error('Error in updateOne', {
      ctx: 'Dao Utils updateOne',
      stack: error.stack,
    });
    throw new Error(error);
  }
};

module.exports = { updateOne };
