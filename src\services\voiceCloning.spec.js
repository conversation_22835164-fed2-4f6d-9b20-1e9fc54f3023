import { it, expect, describe } from 'vitest';
import { isVoiceCloningCode } from './voiceCloning';

describe('voiceCloning', () => {
  describe('isVoiceCloningCode', () => {
    it('valid VC code', () => {
      expect(
        isVoiceCloningCode('s_dongnai_male_username:zero_shot_callcenter_vc'),
      ).toBeTruthy();

      expect(
        isVoiceCloningCode('s_dongnai_male_username_callcenter_vc'),
      ).toBeTruthy();

      expect(
        isVoiceCloningCode('n_dongnai_male_username_callcenter_vc'),
      ).toBeTruthy();
    });

    it('invalid VC code', () => {
      expect(isVoiceCloningCode('x_')).toBeFalsy();
      expect(isVoiceCloningCode(null)).toBeFalsy();
      expect(isVoiceCloningCode(undefined)).toBeFalsy();
    });
  });
});
