const { getAccessToken } = require('./iam');
const callApi = require('../utils/callApi');
const logger = require('../utils/logger');

const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { SYNC_ACCOUNT_FROM_IAM_INTERVAL, ACCOUNT_URL } = require('../configs');
const {
  RATE_LIMIT_TYPE,
  RATE_LIMIT_SOURCE,
} = require('../constants/rateLimiter');
const { checkDomainMatched, getEmailDomain } = require('../utils/email');

class BannedAccountService {
  constructor() {
    this.bannedAccounts = [];
  }

  /** get banned accounts from Store (IAM) */
  // eslint-disable-next-line class-methods-use-this
  async _getFromStore(params) {
    const accessToken = await getAccessToken();

    try {
      const response = await callApi({
        method: 'GET',
        url: `${ACCOUNT_URL}/api/v1/banned-accounts`,
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        params,
      });
      return response;
    } catch (error) {
      logger.error(error, {
        ctx: 'BannedAccountService._getFromStore',
      });
      throw new CustomError(
        errorCodes.BAD_REQUEST,
        `Failed to get banned accounts from IAM: ${error?.message}`,
      );
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async _addToStore({ type, value, feature, banReason, id, enabled }) {
    const accessToken = await getAccessToken();

    try {
      const response = await callApi({
        method: 'POST',
        url: `${ACCOUNT_URL}/api/v1/banned-accounts`,
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        data: {
          type,
          value,
          feature,
          banReason,
          source: RATE_LIMIT_SOURCE.VBEE_TTS_API,
          id,
          enabled,
        },
      });

      const { result } = response;
      const { id: bannedAccountId } = result;
      const existingBannedAccount = this.find({ type, value, feature });
      if (existingBannedAccount) existingBannedAccount.id = bannedAccountId;
    } catch (error) {
      logger.error(error, {
        ctx: 'BannedAccountService._addToStore',
      });
      throw new CustomError(
        errorCodes.BAD_REQUEST,
        `Failed to add banned account to account: ${error?.message}`,
      );
    }
  }

  /** sync banned accounts from Store (IAM) */
  async _syncBannedAccounts() {
    const bannedAccounts = [];

    // ADVISE: these code can be in an Util function to be "get all data from pagination API"
    const limit = 500;
    let offset = 0;
    let hasNext = true;

    while (hasNext) {
      const { result } = await this._getFromStore({ offset, limit });
      const { bannedAccounts: bannedAccountsChunk, total: totalChunk } =
        result || {};
      bannedAccounts.push(...bannedAccountsChunk);
      offset += limit;
      hasNext = bannedAccounts.length < totalChunk;
    }

    this.bannedAccounts = bannedAccounts.map(
      ({ type, value, feature, enabled, id, times }) => ({
        type,
        value,
        feature,
        enabled,
        id,
        times,
      }),
    );
  }

  /** periodically sync banned accounts from Store (IAM) */
  scheduleSync(interval = SYNC_ACCOUNT_FROM_IAM_INTERVAL) {
    const loop = async () => {
      try {
        await this._syncBannedAccounts();
      } catch (err) {
        logger.error(err, { ctx: 'BannedAccountService.scheduleSync' });
      } finally {
        this.syncTimer = setTimeout(loop, interval);
      }
    };

    loop();
  }

  checkBannedAccount({ type, value, feature, isInBlacklistPackageCode }) {
    if (!value || !type) return false;

    switch (type) {
      case RATE_LIMIT_TYPE.IP: {
        // Match IP prefix and feature (or in blacklist package)
        return this.bannedAccounts.some(
          (item) =>
            item?.type === RATE_LIMIT_TYPE.IP &&
            value.startsWith(item.value) &&
            (item.feature === feature || isInBlacklistPackageCode),
        );
      }

      case RATE_LIMIT_TYPE.EMAIL: {
        // Extract all banned email domains for this feature (or in blacklist)
        const emailRules = this.bannedAccounts
          .filter(
            (item) =>
              item?.type === RATE_LIMIT_TYPE.EMAIL &&
              (item.feature === feature || isInBlacklistPackageCode),
          )
          .map((item) => item.value);

        const emailDomain = getEmailDomain(value);
        return checkDomainMatched(emailDomain, emailRules);
      }

      case RATE_LIMIT_TYPE.USER_ID: {
        // Match exact user ID, feature, and enabled status
        return this.bannedAccounts.some(
          (item) =>
            item?.type === RATE_LIMIT_TYPE.USER_ID &&
            item.value === value &&
            item.feature === feature &&
            item.enabled,
        );
      }

      default:
        return false;
    }
  }

  find({ type, value, feature }) {
    return this.bannedAccounts.find((item) => {
      if (!item?.type || !item?.value) return null;

      switch (type) {
        case RATE_LIMIT_TYPE.IP:
          return item.type === type && item.value === value;

        case RATE_LIMIT_TYPE.EMAIL:
          return item.type === type && value && value?.endsWith(item.value);

        case RATE_LIMIT_TYPE.USER_ID:
          return (
            item.type === type &&
            item.value === value?.toString() &&
            item.feature === feature
          );

        default:
          return null;
      }
    });
  }

  updateBannedAccount({ id, enabled, times }) {
    const bannedAccount = this.bannedAccounts.find((item) => item.id === id);
    if (!bannedAccount) return;
    if (enabled !== undefined && enabled !== null)
      bannedAccount.enabled = enabled;
    if (times !== undefined && times !== null) bannedAccount.times = times;
  }

  getAll() {
    return this.bannedAccounts;
  }

  async add({ type, value, feature, banReason, enabled }) {
    // Check if exist in local
    const existingBannedAccount = this.find({ type, value, feature });
    if (existingBannedAccount) {
      this.updateBannedAccount({
        id: existingBannedAccount.id,
        enabled,
        times: existingBannedAccount.times + 1,
      });

      await this._addToStore({
        id: existingBannedAccount.id,
        enabled,
        type: undefined,
        value: undefined,
        feature: undefined,
        banReason: undefined,
      });
      return;
    }

    const payload = {
      type,
      value,
      feature,
      banReason,
      enabled,
    };
    this.bannedAccounts.push({ ...payload, times: 1 });

    await this._addToStore({
      ...payload,
      id: undefined,
    });
  }
}

module.exports = new BannedAccountService();
