const mongoose = require('mongoose');

const communityVoiceStatsSchema = new mongoose.Schema(
  {
    voiceCode: String,
    voiceOwnerId: String,
    totalCredits: Number,
    totalCharacters: Number,
    totalRequests: Number,
    userIds: [String],
    date: Date,
    revenueShareRate: {
      vndPerCredit: Number,
      usdPerCredit: Number,
    },
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model(
  'CommunityVoiceStats',
  communityVoiceStatsSchema,
  'community_voice_stats',
);
