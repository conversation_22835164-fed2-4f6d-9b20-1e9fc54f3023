# ============================================================================
# TTS Caching?
# ============================================================================

### TTS Caching Synthesis
POST {{baseUrl}}/tts/caching
Authorization: Bearer {{token}}
app-id: {{appId}}
Content-Type: application/json

{
    "template": "Hello {name}, welcome to {service}",
    "tags": {
        "name": "John",
        "service": "VBEE TTS"
    },
    "voice_code": "hn_female_ngochuyen_full_48k-fhg",
    "response_type": "indirect",
    "audio_type": "mp3",
    "aes_key": "your-aes-key-here"
}

### Count Text Length for Caching
POST {{baseUrl}}/tts/caching/text-length
Authorization: Bearer {{token}}
app-id: {{appId}}
Content-Type: application/json

{
    "templates": ["Hello {name}", "Welcome {user}"],
    "tags": {
        "name": "John",
        "user": "Alice"
    },
    "aes_key": "your-aes-key-here"
}

