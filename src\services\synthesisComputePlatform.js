const moment = require('moment');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { TTS_CORE_COMPUTE_PLATFORM } = require('../constants');

const getSynthesisComputePlatform = (request) => {
  const synthesisComputePlatform = getFeatureValue(
    FEATURE_KEYS.SYNTHESIS_COMPUTE_PLATFORM,
    {
      ...request,
      product: request.type,
      requestType: request.type,
      localHour: moment().hour(),
    },
  );
  return synthesisComputePlatform || TTS_CORE_COMPUTE_PLATFORM.LAMBDA;
};

module.exports = { getSynthesisComputePlatform };
