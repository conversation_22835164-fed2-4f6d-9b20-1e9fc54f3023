const errorReportDao = require('../daos/errorReport');
const { sendSlackNotification } = require('./notification');
const { getInfoErrorReport } = require('./notification/errorReport');
const { ERROR_REPORT_SLACK_CHANNEL } = require('../configs');
const { checkRequestExists } = require('../daos/request');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const createErrorReport = async (requestId, user, description) => {
  const isRequestExists = await checkRequestExists({ _id: requestId });
  if (!isRequestExists)
    throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { userId, name, phoneNumber, email } = user;

  const createFields = {
    requestId,
    user: { id: userId, name, phoneNumber, email },
    description,
  };

  const report = await errorReportDao.createErrorReport(createFields);

  const blockMessages = getInfoErrorReport({
    name,
    phoneNumber,
    email,
    description,
    requestId,
    createdAt: report.createdAt,
  });

  sendSlackNotification({
    channel: ERROR_REPORT_SLACK_CHANNEL,
    blocks: blockMessages,
    text: `${name} đã báo 1 lỗi`,
  });
};

const getErrorReport = async (errorReportId) => {
  const errorReport = await errorReportDao.findErrorReport(errorReportId);
  if (!errorReport)
    throw new CustomError(code.BAD_REQUEST, 'Error request is not found');

  return errorReport;
};

module.exports = { createErrorReport, getErrorReport };
