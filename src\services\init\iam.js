const sleep = require('../../utils/sleep');
const logger = require('../../utils/logger');

const { getPublicKey, getAccessToken } = require('../iam');

const initPublicKey = async () => {
  let maxRetry = 5;
  while (!global.IAM_PUBLIC_KEY && maxRetry > 0) {
    try {
      const publicKey = await getPublicKey();
      global.IAM_PUBLIC_KEY = publicKey;
    } catch (error) {
      logger.error(error, { ctx: 'InitPublicKey' });
    } finally {
      maxRetry -= 1;
      await sleep(5 * 1000);
    }
  }

  if (!global.IAM_PUBLIC_KEY) {
    logger.error('Cannot get public key from IAM', { ctx: 'InitPublicKey' });
    process.exit(1);
  }
};

const scheduleTokenRefresh = async () => {
  const token = await getAccessToken();
  global.IAM_ACCESS_TOKEN = token;

  // ADVISE: CRITICAL, if we call getAccessToken(), we need to retry immediatelly, and after few retries, throw error

  const TOKEN_REFRESH_INTERVAL = 1 * 60 * 60 * 1000; // 1 hour

  // ADVISE: on performing refresh, the problem is if getAccessToken() failed, we update null to global.IAM_ACCESS_TOKEN, and take 1 hour to have next retry.
  // within 1 hour, global.IAM_ACCESS_TOKEN is invalid
  setInterval(async () => {
    const accessToken = await getAccessToken();
    global.IAM_ACCESS_TOKEN = accessToken;
  }, TOKEN_REFRESH_INTERVAL);
};

const initIAM = async () => {
  await Promise.all([initPublicKey(), scheduleTokenRefresh()]);
};

module.exports = {
  initIAM,
  scheduleTokenRefresh,
};
