const fs = require('fs');
const path = require('path');

const prettierOptions = JSON.parse(
  fs.readFileSync(path.resolve(__dirname, '.prettierrc'), 'utf8'),
);

module.exports = {
  extends: ['airbnb-base', 'prettier'],
  plugins: ['prettier'],
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    'prettier/prettier': ['error', prettierOptions],
    'no-underscore-dangle': 0,
    'no-param-reassign': 0,
    'no-await-in-loop': 0,
    'no-use-before-define': 0,
    'no-restricted-syntax': 0,
    'global-require': 0,
    'no-console': 'error',
  },
  globals: {
    IAM_PUBLIC_KEY: true,
    IAM_ACCESS_TOKEN: true,
    REQUESTS: true,
    CONNECTIONS: true,
    logger: true,
    VOICES: true,
    LANGUAGES: true,
    KAFKA_CONSUMER_GROUP_RANDOM_TTS: true,
    BLACK_WORDS: true,
    REQUEST_DIRECT: true,
    AWS_ZONES_TTS_CACHING: true,
    AWS_ZONES_TTS_STUDIO: true,
    AWS_S3_ACCESS: true,
    KAFKA_CONSUMER_LISTS: true,
    SAMPLE_SCRIPTS: true,
  },
};
