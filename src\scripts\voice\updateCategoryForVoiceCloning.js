require('dotenv').config();

const { initMongoDB } = require('../../models');

initMongoDB();

const VoiceCloning = require('../../models/voiceCloning');
const logger = require('../../utils/logger');

global.logger = logger;

// const data = require('./seedCategoryVoiceCloning.json');
const data = {
  callcenter: ['voicCode'],
  // ...
};

const BATCH_SIZE = 500;

async function updateBatch(category, batch, batchIndex) {
  const operations = batch.map((code) => ({
    updateOne: {
      filter: { code },
      update: { $set: { category } },
    },
  }));

  try {
    const result = await VoiceCloning.bulkWrite(operations);
    logger.info(
      `[${category}] Batch ${batchIndex + 1}: Matched=${
        result.matchedCount
      }, Modified=${result.modifiedCount}`,
    );
  } catch (err) {
    logger.error(
      `[${category}] Batch ${batchIndex + 1} failed: ${err.message}`,
    );
  }
}

(async () => {
  try {
    for (const [category, codes] of Object.entries(data)) {
      logger.info(`Processing category: ${category} (${codes.length} codes)`);

      for (let i = 0; i < codes.length; i += BATCH_SIZE) {
        const batch = codes.slice(i, i + BATCH_SIZE);
        await updateBatch(category, batch, i / BATCH_SIZE);
      }
    }

    logger.info('All voice cloning updates completed by category');
    process.exit(0);
  } catch (error) {
    logger.error(`Error during update: ${error.message}`);
    process.exit(1);
  }
})();
