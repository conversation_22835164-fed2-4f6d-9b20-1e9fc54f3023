const router = require('express').Router();
const asyncMiddleware = require('../../middlewares/async');
const { auth } = require('../../middlewares/auth');
const requestController = require('../../controllers/request');

/* eslint-disable prettier/prettier */
router.get('/requests', auth, asyncMiddleware(requestController.getRequests));
router.get('/requests/:requestId', auth, asyncMiddleware(requestController.getRequest));
/* eslint-disable prettier/prettier */

module.exports = router;
