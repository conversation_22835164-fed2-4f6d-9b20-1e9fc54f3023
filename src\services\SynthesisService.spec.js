import { it, expect, describe } from 'vitest';
import { SynthesisService } from './SynthesisService';

describe('SynthesisService', () => {
  describe('countTextLength', () => {
    it('return nothing', () => {
      expect(SynthesisService.countTextLength('123456', null)).toEqual(6);
    });

    it('should trim whitespace from text', () => {
      expect(SynthesisService.countTextLength('  hello world  ', null)).toEqual(
        11,
      );
    });

    it('should handle empty string', () => {
      expect(SynthesisService.countTextLength(null, null)).toEqual(0);
      expect(SynthesisService.countTextLength('', null)).toEqual(0);
    });

    it('should handle whitespace-only string', () => {
      expect(SynthesisService.countTextLength('   ', null)).toEqual(0);
    });

    it('should remove SSML tags when regex is provided', () => {
      const ssmlRegex = /<[^>]*>/g;
      expect(
        SynthesisService.countTextLength(
          'Hello <break time="1s"/> world',
          ssmlRegex,
        ),
      ).toEqual(12);
    });

    it('should use default regex when regex parameter is undefined', () => {
      expect(
        SynthesisService.countTextLength(
          'Hello <emphasis level="strong">me</emphasis>',
          undefined,
        ),
      ).toEqual(8);
    });

    it('should handle text with multiple SSML tags', () => {
      const ssmlRegex = /<[^>]*>/g;
      expect(
        SynthesisService.countTextLength(
          '<speak>Hello <break/> world <emphasis>test</emphasis></speak>',
          ssmlRegex,
        ),
      ).toEqual(17);
    });
  });

  // TODO: write test for this
  describe('emphasis version v1 v2', () => {
    it('v1', () => {
      // <break time="1s"/>
      expect(SynthesisService.countTextLength('123456', null)).toEqual(6);
    });

    it('v2', () => {
      // <break time=1s/>
      expect(SynthesisService.countTextLength('123456', null)).toEqual(6);
    });
  });
});
