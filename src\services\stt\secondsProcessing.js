const { KAFKA_TOPIC, SYNC_SECONDS_EVENT } = require('../../constants');
const { sendMessage } = require('../kafka/producer');
const logger = require('../../utils/logger');

const spendSttSeconds = ({ userId, requestId, seconds }) => {
  try {
    sendMessage(KAFKA_TOPIC.STT_SECONDS_PROCESSING, {
      key: userId,
      value: {
        event: SYNC_SECONDS_EVENT.SPEND,
        userId,
        requestId,
        seconds,
      },
    });
  } catch (error) {
    logger.error(error, {
      ctx: 'SpendSttSeconds',
      userId,
      requestId,
      seconds,
    });
  }
};

const refundSttSeconds = ({ userId, appId, requestId }) => {
  try {
    sendMessage(KAFKA_TOPIC.STT_SECONDS_PROCESSING, {
      key: userId,
      value: {
        event: SYNC_SECONDS_EVENT.REFUND,
        userId,
        appId,
        requestId,
      },
    });
  } catch (error) {
    logger.error(error, { ctx: 'RefundSttSeconds', userId, appId, requestId });
  }
};

module.exports = { spendSttSeconds, refundSttSeconds };
