import { it, expect, describe } from 'vitest';
import { convertLongToNumber } from './number';

describe('number', () => {
  describe('convertLongToNumber', () => {
    it('Date, or non-object should stay the same', () => {
      expect(convertLongToNumber('xin chào')).toBe('xin chào');

      const aDate = new Date('2024-09-20T00:00:00.000+07:00');
      expect(convertLongToNumber(aDate)).toBe(aDate);
    });
  });
});
