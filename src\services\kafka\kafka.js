const { Kafka, logLevel } = require('kafkajs');
const { KAFKA_CLIENT_ID, KAFKA_BROKERS } = require('../../configs');
const logger = require('../../utils/logger');

const convertLogLevel = (level) => {
  switch (level) {
    case logLevel.ERROR:
    case logLevel.NOTHING:
      return 'error';
    case logLevel.WARN:
      return 'warn';
    case logLevel.INFO:
      return 'info';
    case logLevel.DEBUG:
      return 'debug';
    default:
      return 'error';
  }
};

const LogCreator = () => {
  return ({ namespace, level, log }) => {
    const { message, ...extra } = log;
    logger.log({
      level: convertLogLevel(level),
      message: `${message} ${JSON.stringify(extra)}`,
      ctx: namespace || 'Kafka',
    });
  };
};

const kafka = new Kafka({
  clientId: KAFKA_CLIENT_ID,
  brokers: KAFKA_BROKERS,
  connectionTimeout: 30000,
  authenticationTimeout: 30000,
  logCreator: LogCreator,
});

module.exports = kafka;
