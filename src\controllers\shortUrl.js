const requestService = require('../services/request');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const handleAudioUrl = async (req, res) => {
  const { requestId, token } = req.params;
  const audioUrl = await requestService.handleAudioUrl(requestId, token);
  if (audioUrl) return res.redirect(audioUrl);
  throw new CustomError(errorCodes.BAD_REQUEST);
};

module.exports = { handleAudioUrl };
