const moment = require('moment');

const logger = require('../utils/logger');
const { RandomFactory } = require('../utils/random');
const callApi = require('../utils/callApi');

const {
  REDIS_KEY_PREFIX,
  VN_DOMAIN,
  REGEX,
  REQUEST_TYPE,
  ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS,
  LOADING_SYNTHESIS_FACTOR,
  AUDIO_URL_TYPE,
  PACKAGE_CODE,
  PACKAGE_FEATURE,
  FREE_PACKAGE_CODES,
} = require('../constants');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { STORAGE } = require('../constants/cloudStorage');
const { VOICE_STATUS } = require('../constants/voiceCloning');
const {
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  UPLOAD_URL,
  AUDIO_URL_EXPIRES,
  SYNTHESIS_BY_GATEWAY,
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  VBEE_URL,
  MAX_TITLE_LENGTH,
  GCS_AICORE_TTS_CLIENT_EMAIL,
  GCS_AICORE_TTS_PRIVATE_KEY,
} = require('../configs');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');

const requestDao = require('../daos/request');
const voiceCloningDao = require('../daos/voiceCloning');
const userDao = require('../daos/user');
const { createInProgressRequest } = require('../daos/inProgressRequest');
const { getSynthesisTimeInRedis } = require('../daos/tts');
const {
  increaseDownloadCount,
  updateDownloadStats,
} = require('../daos/request');

const Caching = require('../caching');
const RequestCaching = require('../caching/requestCaching');

const appService = require('./app');
const ttsProcessingService = require('./ttsProcessing');

const { renameS3File, getKeyFromS3Url } = require('./s3');

const { getAccessToken } = require('./iam');
const { getFeatureValue } = require('./growthbook');
const { getAwsZone } = require('../daos/awsZone');
const { getAudioName, detectCloudStorageService } = require('./audio');
const {
  addLanguageDetailToVoice,
  getVoiceWithCreditFactor,
} = require('./voice');
const { getPackageUsageOptions } = require('./package');
const {
  getKeyFromGCSUrl,
  renameGCSFile,
  checkFileExists,
  copyGCStoGCSWithRetry,
} = require('./googleCloudStorage');
const { AuthorizationService } = require('./authorization');
const { isVoiceCloningCode } = require('./voiceCloning');

const SPACE = ' ';
const CHINESE_CHARACTERS_LANGUAGE_CODES = ['cmn-CN', 'ja-JP', 'ko-KR'];

const checkMonetizable = ({ type, packageCode, voice }) => {
  const { code: voiceCode, status } = voice || {};

  const isStudioRequest = type === REQUEST_TYPE.STUDIO;
  const isPaidPackage = !FREE_PACKAGE_CODES.includes(packageCode);
  const isVoiceCloning = isVoiceCloningCode(voiceCode);
  const isPublicVoice = status === VOICE_STATUS.PUBLIC;

  const isMonetizable =
    isStudioRequest && isPaidPackage && isVoiceCloning && isPublicVoice;

  return isMonetizable;
};

/** persist TTSRequest into DB */
const createRequest = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  requestInfo._id = requestInfo.requestId;
  delete requestInfo.requestId;

  const request = await requestDao.createRequest(requestInfo);

  const { _id, createdAt } = requestInfo;
  createInProgressRequest(_id, createdAt);

  return request;
};

const updateParagraphsOfRequest = async (requestId, paragraphs) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { paragraphs: paragraphsExists } = request;

  if (paragraphsExists === undefined) {
    await requestDao.updateRequestById(request._id, { paragraphs });
  }
};

const saveSynthesisTime = async (requestId, startTime) => {
  const { t2aDurations, synthesisDurations } = await getSynthesisTimeInRedis(
    requestId,
  );

  const getMinMaxAverageValueOfArray = (array) => {
    const min = array.length ? Math.min(...array) : 0;
    const max = array.length ? Math.max(...array) : 0;
    const arrayLength = array.length || 1;
    const average = array.reduce((a, b) => a + b, 0) / arrayLength;
    return { min, max, average: average.toFixed(3) };
  };
  const {
    min: minT2aDuration,
    max: maxT2aDuration,
    average: averageT2aDuration,
  } = getMinMaxAverageValueOfArray(t2aDurations);
  const {
    min: minSynthesisDuration,
    max: maxSynthesisDuration,
    average: averageSynthesisDuration,
  } = getMinMaxAverageValueOfArray(synthesisDurations);

  await RequestCaching.updateRequestByIdInRedis(requestId, {
    t2aDuration: {
      min: minT2aDuration,
      max: maxT2aDuration,
      average: averageT2aDuration,
    },
    synthesisDuration: {
      min: minSynthesisDuration,
      max: maxSynthesisDuration,
      average: averageSynthesisDuration,
      start: startTime,
      end: Date.now(),
    },
  });
};

const getApiRequest = async ({ requestId, appToken }) => {
  const selectFields = [
    'app',
    'characters',
    'voiceCode',
    'audioType',
    'speed',
    'bitrate',
    'progress',
    'createdAt',
    'status',
    'audioLink',
    'retentionPeriod',
  ];
  const request = await requestDao.findRequest(
    { _id: requestId },
    selectFields,
  );

  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const {
    app: appId,
    characters,
    voiceCode,
    audioType,
    speed,
    bitrate,
    progress,
    createAt,
    status,
    retentionPeriod,
    createdAt,
  } = request;

  const app = await appService.getApp(appId, false);
  const { secretKey, token } = app;

  // ADVISE: BUSINESS: maybe relate to old API V3. why "no secretKey" is the problem ==> unauthorized?
  if (!secretKey) throw new CustomError(code.UNAUTHORIZED);
  if (appToken !== token) throw new CustomError(code.UNAUTHORIZED);

  const isAudioExpired = moment().isAfter(
    moment(createdAt).add(retentionPeriod, 'days'),
  );

  const result = {
    appId,
    requestId,
    characters,
    voiceCode,
    audioType,
    progress,
    speedRate: speed,
    bitrate,
    createAt,
    status,
  };

  if (isAudioExpired) result.audioExpired = true;
  else if (request.audioLink) {
    const accessToken = await getAccessToken();
    const audioLink = await getAudioDownloadUrl({
      requestId,
      authorization: `Bearer ${accessToken}`,
    });
    result.audioLink = audioLink;
  }

  return result;
};

const modifyFromS3Url = (originalUrl) => {
  const parts = originalUrl.split('/');
  const newUrl = `https://${VN_DOMAIN}/${parts[2].split('.')[0]}/${parts
    .slice(3)
    .join('/')}`;
  return newUrl;
};

const modifyFromGCSUrl = (originalUrl) => {
  const parsedUrl = new URL(originalUrl);
  parsedUrl.hostname = VN_DOMAIN;
  const newUrl = parsedUrl.toString();
  return newUrl;
};

const modifyVNUrl = (originalUrl) => {
  const cloudStorageFrom = detectCloudStorageService(originalUrl);

  switch (cloudStorageFrom) {
    case STORAGE.S3:
      return modifyFromS3Url(originalUrl);
    case STORAGE.GCS:
      return modifyFromGCSUrl(originalUrl);
    default:
      return originalUrl;
  }
};

const updateAudioLink = async (requestId, newTitle) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const newAudioName = getAudioName(newTitle, requestId, request.userId);
  const {
    awsZoneFunctions = {},
    awsZoneSynthesis = DEFAULT_AWS_REGION,
    audioType,
    googleCloudStorage = {},
  } = request;
  const { s3Bucket = DEFAULT_BUCKET_S3 } = awsZoneFunctions;
  const { bucket: gcsBucket } = googleCloudStorage;

  const { audioLink } = request;
  const storage = detectCloudStorageService(audioLink);
  const isGCSStorage = storage === STORAGE.GCS;

  const oldAudioKey = isGCSStorage
    ? getKeyFromGCSUrl(audioLink)
    : getKeyFromS3Url(audioLink);

  const lastIndex = oldAudioKey.lastIndexOf('/');
  const directoryPath = oldAudioKey.substring(0, lastIndex + 1);

  const newKey = `${directoryPath}${newAudioName}.${audioType}`;

  const newAudioLink = isGCSStorage
    ? await renameGCSFile({
        bucketName: gcsBucket,
        oldKey: oldAudioKey,
        newKey,
      })
    : await renameS3File({
        bucketName: s3Bucket,
        oldKey: oldAudioKey,
        newKey,
        awsZoneSynthesis,
      });

  return newAudioLink;
};

const updateRequest = async (requestId, requestInfo) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { title } = requestInfo;
  const { audioLink } = request;
  if (audioLink) {
    const newAudioLink = await updateAudioLink(requestId, title);
    if (!newAudioLink)
      throw new CustomError(
        code.RENAME_TITLE_FAILURE,
        'Update audio link failed',
      );

    requestInfo.audioLink = newAudioLink;
  }

  const updatedRequest = await requestDao.updateRequestById(requestId, {
    ...requestInfo,
  });

  return updatedRequest;
};

const getPendingAndInprogressRequestKey = (userId, requestType) => {
  const baseKey =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS
      : REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS;

  return `${baseKey}_${userId}`;
};

const countPendingAndInProgressReq = async ({
  userId,
  requestType = REQUEST_TYPE.STUDIO,
}) => {
  const requestKey = getPendingAndInprogressRequestKey(userId, requestType);
  const numOfPendAndInprReq = await Caching.RedisRepo.get(requestKey);
  return numOfPendAndInprReq || 0;
};

const countPendingRequests = async ({ userId, requestType }) => {
  const user = await AuthorizationService.getUser(userId);

  const packageCode =
    requestType === REQUEST_TYPE.STUDIO || requestType === REQUEST_TYPE.DUBBING
      ? user?.packageCode
      : user?.apiPackage?.packageCode;
  const packageUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode,
    userUsageOptions: user,
  });
  const { concurrentRequest = 0 } = packageUsageOptions || {};

  const numberOfPendingAndInprReq = await countPendingAndInProgressReq({
    userId,
    requestType,
  });

  const totalPendingReq = Number(numberOfPendingAndInprReq);
  return { totalPendingReq, concurrentRequest };
};

const setPendingAndInProgressReqCount = async ({
  userId,
  requestType = REQUEST_TYPE.STUDIO,
  count = 0,
}) => {
  const requestKey = getPendingAndInprogressRequestKey(userId, requestType);
  await Caching.RedisRepo.set(requestKey, count);
};

const getPresignedUrl = async ({ key, bucket, authorization, storage }) => {
  try {
    const clientEmail = GCS_AICORE_TTS_CLIENT_EMAIL;
    const privateKey = Buffer.from(
      GCS_AICORE_TTS_PRIVATE_KEY,
      'utf-8',
    ).toString('base64');

    const res = await callApi({
      headers: {
        authorization,
        'access-key-id': AWS_ACCESS_KEY_ID,
        'secret-access-key': AWS_SECRET_ACCESS_KEY,
        ...(storage === STORAGE.GCS && {
          'client-email': clientEmail,
        }),
        ...(storage === STORAGE.GCS && {
          'private-key': privateKey,
        }),
      },
      url: `${UPLOAD_URL}/api/v1/files/presigned-url-for-sharing`,
      method: 'GET',
      params: {
        key,
        bucket,
        expiresIn: AUDIO_URL_EXPIRES,
        ...(storage && { storage }),
      },
    });
    return res.result;
  } catch (error) {
    logger.error(error, { ctx: 'FetchAudioURL' });
    return null;
  }
};

const getFirstDownloadDuration = (endedAt) => {
  const now = moment();
  const duration = now.diff(moment(endedAt), 'seconds');
  return duration;
};

const handleUpdateDownloadStats = async ({
  requestId,
  downloadCount,
  endedAt,
}) => {
  const hasDownloaded = downloadCount > 0;
  if (hasDownloaded) {
    await increaseDownloadCount(requestId);
  } else {
    const firstDownloadDuration = getFirstDownloadDuration(endedAt);
    await updateDownloadStats(requestId, firstDownloadDuration);
  }
};

const ensureAudioFileExists = async (request) => {
  const recopyAudioFile = getFeatureValue(FEATURE_KEYS.RECOPY_AUDIO_FILE, {
    userId: request?.userId,
    requestType: request?.type,
  });
  if (!recopyAudioFile) return;

  const { audioLink, sourceAudioLink, googleCloudStorage = {} } = request;

  const storage = detectCloudStorageService(audioLink);
  const isGCSStorage = storage === STORAGE.GCS;
  if (!isGCSStorage) return;

  const { bucket: gcsBucket } = googleCloudStorage;
  const key = getKeyFromGCSUrl(audioLink);
  const isFileExists = await checkFileExists({ bucket: gcsBucket, key });
  if (isFileExists) return;

  logger.warn('Audio file does not exist', {
    ctx: 'ensureAudioFileExists',
    requestId: request._id,
    userId: request.userId,
    sourceUrl: sourceAudioLink,
    destinationUrl: audioLink,
  });

  await copyGCStoGCSWithRetry({ bucket: gcsBucket, key, url: sourceAudioLink });
};

const getPresignedAudioUrl = async (request, authorization) => {
  const { awsZoneFunctions = {}, googleCloudStorage = {} } = request;

  const { s3Bucket = DEFAULT_BUCKET_S3 } = awsZoneFunctions;
  const { bucket: gcsBucket } = googleCloudStorage;

  const storage = detectCloudStorageService(request.audioLink);
  const isGCSStorage = storage === STORAGE.GCS;

  const key = isGCSStorage
    ? getKeyFromGCSUrl(request.audioLink)
    : getKeyFromS3Url(request.audioLink);

  const bucket = isGCSStorage ? gcsBucket : s3Bucket;

  const presignedRes = await getPresignedUrl({
    key,
    bucket,
    authorization,
    storage,
  });

  return presignedRes?.url;
};

const getAudioUrl = async ({ request, authorization }) => {
  // ADVISE: do we really need to get audioUrlType from feature flag?
  const audioUrlType = getFeatureValue(FEATURE_KEYS.AUDIO_URL, {
    userId: request.userId,
    appId: request.app,
    requestType: request.type,
  });

  if (audioUrlType !== AUDIO_URL_TYPE.ORIGINAL_URL) {
    // TODO: Temporary disable with original url type (hn.vbee.vn), but still need handle soon
    await ensureAudioFileExists(request);
  }

  switch (audioUrlType) {
    case AUDIO_URL_TYPE.ORIGINAL_URL: {
      return request.audioLink;
    }

    case AUDIO_URL_TYPE.SHORT_URL: {
      const audioUrl = await getPresignedAudioUrl(request, authorization);
      if (!audioUrl) return null;

      // Cache key `${request_id}_${random_string}` with value <presigned_url> in Cache with TTL equals to presigned_url's expires time
      const randomString = RandomFactory.getGuid();
      const requestId = request._id;
      const cacheKey = `${REDIS_KEY_PREFIX.AUDIO_URL}_${requestId}_${randomString}`;
      const cacheKeyTtl = AUDIO_URL_EXPIRES;
      await Caching.RedisRepo.set(cacheKey, audioUrl, cacheKeyTtl);

      // Return short url
      return `${VBEE_URL}/s/${requestId}/${randomString}`;
    }

    case AUDIO_URL_TYPE.PRESIGNED_URL:
    default: {
      const audioUrl = await getPresignedAudioUrl(request, authorization);
      return audioUrl;
    }
  }
};

const handleAudioUrl = async (requestId, token) => {
  const cacheKey = `${REDIS_KEY_PREFIX.AUDIO_URL}_${requestId}_${token}`;
  const audioUrl = await Caching.RedisRepo.get(cacheKey);
  if (!audioUrl) throw new CustomError(code.BAD_REQUEST);
  return audioUrl;
};

const getCloudFrontUrl = ({ cloudFrontDomain, url }) => {
  if (!cloudFrontDomain) return url;

  const urlObject = new URL(url);
  const s3Domain = urlObject.hostname;
  return url.replace(s3Domain, cloudFrontDomain);
};

const getAudioDownloadUrl = async ({ requestId, authorization }) => {
  const request = await requestDao.findRequestById(requestId);
  const { retentionPeriod, endedAt, downloadCount = 0 } = request;
  const expiresTime = moment(endedAt).add(retentionPeriod, 'days');
  if (moment().isAfter(expiresTime))
    throw new CustomError(code.AUDIO_URL_EXPIRED);

  let audioUrl = await getAudioUrl({ request, authorization });
  if (audioUrl) {
    // Update download stats. Async because we don't want to wait for this when return audio link
    handleUpdateDownloadStats({ requestId, downloadCount, endedAt });

    // ADVISE: do we really need to get feature flag useCloudFrontAsProxy here? what is the case for NOT use CloudFront?
    // if there is a reason for that, explain the business logic
    const useCloudFrontAsProxy = getFeatureValue(
      FEATURE_KEYS.CLOUD_FRONT_AS_PROXY,
      { userId: request.userId, appId: request.app, requestType: request.type },
    );

    if (useCloudFrontAsProxy) {
      const awsZone = await getAwsZone({ region: request.awsZoneSynthesis });
      audioUrl = getCloudFrontUrl({
        cloudFrontDomain: awsZone.s3CloudFront?.[request.retentionPeriod],
        url: audioUrl,
      });
    }

    // Return audio url
    return audioUrl;
  }
  throw new CustomError(code.BAD_REQUEST);
};

// Check if user has free dubbing package or has expiry dubbing package and studio-free-v2 package
const checkUseStudioFreeV2Package = (user) => {
  const isExpireDubbingPackage = moment().isAfter(
    user.dubbing?.packageExpiryDate,
  );
  const isUsingFreeDubbingPackage = !user.dubbing?.packageExpiryDate;
  const isUsingStudioFreeV2Package =
    user.packageCode === PACKAGE_CODE.STUDIO_FREE_V2;

  const isFreePackage =
    (isUsingFreeDubbingPackage || isExpireDubbingPackage) &&
    isUsingStudioFreeV2Package;

  return isFreePackage;
};

const checkIsLimitDownloadAudio = async (user) => {
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user?._id,
    packageCode: user?.packageCode,
    userUsageOptions: user,
  });
  const studioUserDownload = studioUsageOptions?.download;

  return studioUserDownload >= 0;
};

// DEPRECATED
// eslint-disable-next-line no-unused-vars
const checkIsFreePackage = async (user, requestType) => {
  let isFreePackage;
  const studioUsageOptions = await getPackageUsageOptions({
    userId: user._id,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });
  const studioUserFeatures = studioUsageOptions?.features;
  const isV2StudioPaidPackage = studioUserFeatures?.includes(
    PACKAGE_FEATURE.SUBTITLE,
  );
  const isStudioFreeV2Package =
    user.packageCode === PACKAGE_CODE.STUDIO_FREE_V2;
  // If user'features has subtitle and package code is not studio-free-v2, it's paid package, return false
  if (isV2StudioPaidPackage && !isStudioFreeV2Package) return false;

  switch (requestType) {
    case REQUEST_TYPE.STUDIO: {
      const STUDIO_FREE_PACKAGES = [
        PACKAGE_CODE.STUDIO_FREE_V2,
        PACKAGE_CODE.STUDIO_FREE,
      ];
      isFreePackage = STUDIO_FREE_PACKAGES.includes(user.packageCode);
      break;
    }

    case REQUEST_TYPE.DUBBING: {
      const DUBBING_FREE_PACKAGES = [
        PACKAGE_CODE.DUBBING_BASIC,
        PACKAGE_CODE.DUBBING_TRIAL,
      ];

      // Check if user has free dubbing package or has expiry dubbing package and studio-free-v2 package
      isFreePackage = DUBBING_FREE_PACKAGES.includes(user.dubbing?.packageCode);
      // Package is expired if user has no package expiry date or current date is after package expiry date
      const isUsingFreeV2Package = checkUseStudioFreeV2Package(user);
      isFreePackage = isFreePackage || isUsingFreeV2Package;
      break;
    }

    default:
      break;
  }
  return isFreePackage;
};

const getLatestDownloadedAt = (user, requestType) => {
  let latestDownloadedAt;

  switch (requestType) {
    case REQUEST_TYPE.STUDIO: {
      latestDownloadedAt = user.latestDownloadedAt;
      break;
    }

    case REQUEST_TYPE.DUBBING: {
      latestDownloadedAt = user.dubbing?.latestDownloadedAt;
      break;
    }

    default:
      break;
  }

  return latestDownloadedAt;
};

const checkAllowDownload = (latestDownloadedAt) => {
  const hasDownloaded = !!latestDownloadedAt;
  const now = moment();
  const hasDownloadedToday = now.isSame(moment(latestDownloadedAt), 'day');

  const allowDownload = !hasDownloaded || !hasDownloadedToday;
  return allowDownload;
};

const updateLatestDownloadedAt = async (user, requestType) => {
  const userId = user._id;

  const isStudioFreeV2Package =
    requestType === REQUEST_TYPE.STUDIO
      ? user.packageCode === PACKAGE_CODE.STUDIO_FREE_V2
      : checkUseStudioFreeV2Package(user);

  if (isStudioFreeV2Package)
    await userDao.updateStudioLatestDownloadedAt(userId);
  else {
    switch (requestType) {
      case REQUEST_TYPE.STUDIO: {
        await userDao.updateLatestDownloadedAt(userId);
        break;
      }
      case REQUEST_TYPE.DUBBING: {
        await userDao.updateDubbingLatestDownloadedAt(userId);
        break;
      }

      default:
        break;
    }
  }
};

// this function will be used when we need to limit download per day with user
const handleDownloadAudio = async ({ requestId, authorization }) => {
  const request = await requestDao.findRequestById(requestId);
  const { userId, type: requestType } = request;

  const user = await userDao.findUserById(userId);

  const latestDownloadedAt = getLatestDownloadedAt(user, requestType);
  const isLimitDownloadAudio = await checkIsLimitDownloadAudio(user);

  // At this moment, only free packages (Studio or Dubbing) limit downloads per day, and it's 1 download per day { download = 1 }
  // So we only check for free package, and assume max download is 1
  // In the future, if the limit increases, we need to update this logic
  if (isLimitDownloadAudio) {
    const allowDownload = checkAllowDownload(latestDownloadedAt);

    if (allowDownload) {
      const audioUrl = await getAudioDownloadUrl({ requestId, authorization });
      await updateLatestDownloadedAt(user, requestType);
      // sendEventDownloadToDataSenses(request, datasenses);
      return audioUrl;
    }

    throw new CustomError(code.DOWNLOAD_QUOTA_EXCEEDED);
  } else {
    const audioUrl = await getAudioDownloadUrl({ requestId, authorization });
    // sendEventDownloadToDataSenses(request, datasenses);
    return audioUrl;
  }
};

const calculateEstimateProgress = (processTime, characters) => {
  const timeProcessEstimate =
    (characters / 1000) * ESTIMATED_TIME_PER_ONE_THOUSAND_CHARACTERS;

  let progress = processTime / timeProcessEstimate;

  progress = (Math.round(progress * 100) / 100).toFixed(2);
  return Math.min(progress, LOADING_SYNTHESIS_FACTOR.MAX_PROCESSING);
};

const getProgressRequest = async (requestId) => {
  let request;
  request = await RequestCaching.findRequestByIdInRedis(requestId);
  const isRequestNotFound = Object.keys(request).length === 0;
  if (isRequestNotFound) request = await requestDao.findRequestById(requestId);

  const { _id, status, audioLink, characters, processingAt, endedAt } = request;
  let progress = 0;
  if (!SYNTHESIS_BY_GATEWAY)
    progress = await ttsProcessingService.getProgress(requestId);
  else {
    const processTime = moment().diff(moment(processingAt), 'seconds');
    progress = calculateEstimateProgress(processTime, characters);
  }

  return { _id, progress, status, processingAt, endedAt, audioLink };
};

const getTitle = (text, voiceLanguage) => {
  const plainText = text
    .trim()
    .replace(REGEX.ADVANCE_TAG, '')
    .replace(REGEX.OLD_BREAK_TIME, '');

  if (plainText.length <= MAX_TITLE_LENGTH) {
    return plainText;
  }

  const lastSpaceIndex = plainText.lastIndexOf(SPACE, MAX_TITLE_LENGTH - 1);
  const firstSpaceIndex = plainText.indexOf(SPACE);

  if (
    firstSpaceIndex !== -1 &&
    firstSpaceIndex <= MAX_TITLE_LENGTH - 1 &&
    !CHINESE_CHARACTERS_LANGUAGE_CODES.includes(voiceLanguage)
  )
    return plainText.substring(0, lastSpaceIndex + 1);

  return plainText.substring(0, MAX_TITLE_LENGTH);
};

// Convert sentences voice code to string cause it's an object and when return to client, key will be camelCase
const stringifySentencesVoiceCode = (sentencesVoiceCode) =>
  JSON.stringify(sentencesVoiceCode);

const getRequest = async (requestId, userId) => {
  let request = await requestDao.findRequestById(requestId);
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  request.voice = getVoiceWithCreditFactor(request.voice, packageCode); // TODO: Actually, this is wrong because credit factor can be changed depend on feature flag. Temporary return for frontend work.

  if (request?.sentences && request.sentences.length) {
    let sentences = await requestDao.getDetailSentencesByRequestId(requestId);
    sentences = sentences.map((sentence) => {
      let { voice } = sentence;
      voice = getVoiceWithCreditFactor(voice, packageCode);
      return { ...sentence, voice };
    });
    request = { ...request, sentences };
  }

  if (request?.sentencesVoiceCode)
    request.sentencesVoiceCode = stringifySentencesVoiceCode(
      request.sentencesVoiceCode,
    );

  return request;
};

const getRequests = async (query, userId) => {
  const { requests, total } = await requestDao.findRequests(query);
  // ADVISE: duplicated code, same file line 839
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  const resultRequests = requests.map((request) => {
    if (request?.sentencesVoiceCode)
      request.sentencesVoiceCode = stringifySentencesVoiceCode(
        request.sentencesVoiceCode,
      );

    request.voice = getVoiceWithCreditFactor(request.voice, packageCode);
    return request;
  });

  return { requests: resultRequests, total };
};

const addVoiceDetailToSentence = async (sentence, packageCode) => {
  let voice = global.VOICES.find((v) => v.code === sentence.voiceCode);
  if (!voice) {
    const clonedVoice = await voiceCloningDao.findVoiceCloningByCode(
      sentence.voiceCode,
    );
    voice = clonedVoice;
  }

  voice = await addLanguageDetailToVoice(voice);
  voice = getVoiceWithCreditFactor(voice, packageCode);
  return { ...sentence, voice };
};

const getDetailSentencesForRequest = async (request, packageCode) => {
  const sentences = await Promise.all(
    request.sentences.map((sentence) =>
      addVoiceDetailToSentence(sentence, packageCode),
    ),
  );
  return sentences;
};

const getRequestV2 = async (requestId, userId) => {
  let request = await requestDao.findRequest({ _id: requestId });
  if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  request = await requestDao.assignVoiceToRequest(request);
  let voice = await addLanguageDetailToVoice(request.voice);
  voice = getVoiceWithCreditFactor(voice, packageCode);
  request.voice = voice;
  delete request.voiceCode; // Remove voiceCode field cause voice field already has this info

  const hasSentences = request?.sentences && request.sentences.length;
  if (hasSentences) {
    // Add number of sentences to request cause get request v1 add this field
    request.numberOfSentences = request.sentences.length;
    const sentences = await getDetailSentencesForRequest(request, packageCode);
    request = { ...request, sentences };
  }

  if (request?.sentencesVoiceCode)
    request.sentencesVoiceCode = stringifySentencesVoiceCode(
      request.sentencesVoiceCode,
    );

  return request;
};

const getRequestsV2 = async (query, userId) => {
  const { requests, total } = await requestDao.findRequestsV2(query);
  const user = userId ? await AuthorizationService.getUser(userId, false) : {};
  const { packageCode } = user || {};

  const resultRequests = requests.map((request) => {
    if (request?.sentencesVoiceCode)
      request.sentencesVoiceCode = stringifySentencesVoiceCode(
        request.sentencesVoiceCode,
      );

    request.voice = getVoiceWithCreditFactor(request.voice, packageCode);
    return request;
  });

  return { requests: resultRequests, total };
};

module.exports = {
  createRequest,

  updateParagraphsOfRequest,
  saveSynthesisTime,
  getApiRequest,
  modifyVNUrl,
  updateRequest,
  countPendingAndInProgressReq,
  countPendingRequests,
  setPendingAndInProgressReqCount,
  getAudioDownloadUrl,
  getProgressRequest,
  handleAudioUrl,
  getTitle,
  handleDownloadAudio,
  getRequests,
  getRequest,
  getRequestV2,
  getRequestsV2,
  checkMonetizable,
};
