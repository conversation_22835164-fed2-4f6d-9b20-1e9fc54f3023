const {
  RecaptchaEnterpriseServiceClient,
} = require('@google-cloud/recaptcha-enterprise');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const {
  CONSOLE_URL,
  RECAPTCHA_SECRET_KEY,
  RECAPTCHA_ENTERPRISE_PROJECT_ID,
  RECAPTCHA_ENTERPRISE_CREDENTIAL_CLIENT_EMAIL,
  RECAPTCHA_ENTERPRISE_CREDENTIAL_PRIVATE_KEY,
  RECAPTCHA_ENTERPRISE_WEBSITE_SITE_KEY,
  RECAPTCHA_ENTERPRISE_ANDROID_SITE_KEY,
  RECAPTCHA_ENTERPRISE_IOS_SITE_KEY,
} = require('../configs');
const callApi = require('../utils/callApi');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const { getFeatureValue } = require('./growthbook');
const { checkAppDevice } = require('./device');
const { verifyAccessToken } = require('./auth');
const {
  MAPPING_RECAPTCHA_ERROR_CODE,
  MAPPING_RECAPTCHA_ENTERPRISE_ERROR,
  PLATFORM_TYPE,
  RECAPTCHA_TYPE,
} = require('../constants/recaptcha');
const {
  sendRecaptchaErrorToSlackNotification,
} = require('./notification/recaptchaError');

const verifyRecaptchaToken = async (token, user = {}) => {
  const { userId, email } = user;
  if (!token) {
    logger.debug('Verify recaptcha token failure', {
      ctx: 'VerifyRecaptchaTokenFailure',
      userId,
      email,
      error: 'missing-recaptcha',
    });
    throw new CustomError(
      errorCodes.BAD_REQUEST,
      `Request invalid. Please access the API at the link ${CONSOLE_URL}/apps`,
    );
  }

  const response = await callApi({
    method: 'POST',
    url: 'https://www.google.com/recaptcha/api/siteverify',
    params: { secret: RECAPTCHA_SECRET_KEY, response: token },
  });

  if (!response?.success) {
    logger.debug('Verify recaptcha token failure', {
      ctx: 'VerifyRecaptchaTokenFailure',
      userId,
      email,
      response,
    });

    const recaptchaErrorCode =
      MAPPING_RECAPTCHA_ERROR_CODE[response?.errorCodes?.[0]];
    throw new CustomError(
      recaptchaErrorCode || errorCodes.BAD_REQUEST,
      `Request invalid. Please access the API at the link ${CONSOLE_URL}/apps`,
    );
  }

  return response;
};

const getRecaptchaEnterpriseSiteKey = (platform) => {
  switch (platform) {
    case PLATFORM_TYPE.WEBSITE:
      return RECAPTCHA_ENTERPRISE_WEBSITE_SITE_KEY;
    case PLATFORM_TYPE.ANDROID:
      return RECAPTCHA_ENTERPRISE_ANDROID_SITE_KEY;
    case PLATFORM_TYPE.IOS:
      return RECAPTCHA_ENTERPRISE_IOS_SITE_KEY;
    default:
      throw new CustomError(
        errorCodes.BAD_REQUEST,
        `Platform ${platform} invalid`,
      );
  }
};

const client = new RecaptchaEnterpriseServiceClient({
  credentials: {
    client_email: RECAPTCHA_ENTERPRISE_CREDENTIAL_CLIENT_EMAIL,
    private_key: RECAPTCHA_ENTERPRISE_CREDENTIAL_PRIVATE_KEY,
  },
  projectId: RECAPTCHA_ENTERPRISE_PROJECT_ID,
});

const verifyEnterpriseRecaptchaToken = async (token, user = {}, platform) => {
  const { userId, email } = user;
  if (!token) {
    logger.debug('Verify recaptcha token failure', {
      ctx: 'VerifyRecaptchaTokenFailure',
      userId,
      email,
      error: 'missing-recaptcha',
    });
    throw new CustomError(
      errorCodes.BAD_REQUEST,
      `Request invalid. Please access the API at the link ${CONSOLE_URL}/apps`,
    );
  }

  const siteKey = getRecaptchaEnterpriseSiteKey(platform);

  const projectPath = client.projectPath(RECAPTCHA_ENTERPRISE_PROJECT_ID);
  const request = {
    assessment: { event: { token, siteKey } },
    parent: projectPath,
  };

  try {
    const [response] = await client.createAssessment(request);

    if (!response?.tokenProperties?.valid) {
      const recaptchaErrorCode =
        MAPPING_RECAPTCHA_ENTERPRISE_ERROR[
          response?.tokenProperties?.invalidReason
        ];
      logger.debug('Verify recaptcha token failure', {
        ctx: 'VerifyRecaptchaTokenFailure',
        userId,
        email,
        response,
      });
      throw new CustomError(
        recaptchaErrorCode || errorCodes.BAD_REQUEST,
        `Request invalid. Please access the API at the link ${CONSOLE_URL}/apps`,
      );
    }
  } catch (error) {
    const { errorCode, message = '' } = error;

    const isGoogleError = errorCode === errorCodes.BAD_REQUEST;
    if (isGoogleError) {
      sendRecaptchaErrorToSlackNotification({
        message,
        userId,
        email,
        platform,
        recaptchaVersion: RECAPTCHA_TYPE.ENTERPRISE,
      });
      if (message.includes('DEADLINE_EXCEEDED')) return;
    }
    throw new CustomError(errorCode, message);
  }
};

const verifyRecaptchaDemoTTS = async ({
  recaptchaToken,
  platform,
  appVersion,
  userAgent,
  accessToken,
}) => {
  const data = await verifyAccessToken(accessToken);
  const { sub: userId, email } = data || {};
  const isAppDevice = checkAppDevice(userAgent);

  const recaptchaType = getFeatureValue(
    FEATURE_KEYS.RECAPTCHA_TRY_LISTEN_TTS_TYPE,
    { userId, email, isAppDevice, appVersion },
  );

  // ADVISE: modify input argument should be prohibited. The correct way: calculate the platform outside this func, ensure the platform value must be in enum PLATFORM_TYPE
  platform = isAppDevice ? platform : PLATFORM_TYPE.WEBSITE;

  switch (recaptchaType) {
    case RECAPTCHA_TYPE.V3:
      await verifyRecaptchaToken(recaptchaToken);
      break;

    case RECAPTCHA_TYPE.ENTERPRISE:
      await verifyEnterpriseRecaptchaToken(recaptchaToken, {}, platform);
      break;

    default:
      break;
  }
};

module.exports = {
  verifyRecaptchaToken,
  verifyRecaptchaDemoTTS,
  verifyEnterpriseRecaptchaToken,
};
