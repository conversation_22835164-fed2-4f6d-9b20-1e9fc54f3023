const mongoose = require('mongoose');
require('mongoose-long')(mongoose);

const { Long } = mongoose.Types;

const ResetLog = mongoose.Schema(
  {
    userId: String,
    studio: {
      packageCode: String,
      packageExpiryDate: Date,
      oneTime: {
        remainingCharacters: { type: Long, default: 0 },
        bonusCharacters: { type: Long, default: 0 },
        lockCharacters: { type: Long, default: 0 },
      },
      cycle: {
        nextResetAt: Date,
        remainingCredits: { type: Long, default: 0 },
        creditsPerCycle: { type: Long, default: 0 },
      },
      topUp: {
        remainingCredits: { type: Long, default: 0 },
      },
    },
    resetAt: Date,
    resetCreditsPerCycleStatus: String,
    resetCreditsPerCycleNote: String,
    resetCreditsPerCycleLastUpdated: Date,
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model('ResetLog', ResetLog, 'reset_logs');
