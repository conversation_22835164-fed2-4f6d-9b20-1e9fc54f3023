const { REGEX, TTS_CORE_VERSION, SYNTHESIS_TYPE } = require('../constants');

// This original is REGEX.MULTIPLE_TAGS, below is merged from OLD_BREAK_TIME and ADVANCE_TAG
/** SSML tag. When we are NOT in TTS_CORE_VERSION.NEW */
const SSML_MULTIPLE_TAGS =
  /[<]break\s+time[=]([0-9.]+)[s][/][>]|[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]|[<]emphasis\s+level[=]["](?:strong|moderate|reduced)["][>]|[<][/]emphasis[>]|[<]prosody\s+rate[=]["](?:x-fast|fast|medium|slow|x-slow|(?:(\+|-)[0-9.]+%))["][>]|[<][/]prosody[>]/g;

class SynthesisService {
  /**
   * count text length (omit ssml tag which are identified by regex)
   * @param {*} text
   * @param {*} regex to identify ssml tag to omit
   * @returns
   */
  static countTextLength(text, regex) {
    if (!text) return 0;

    regex = regex || SSML_MULTIPLE_TAGS;
    const normalizeText = text.trim().replace(regex, '');
    return normalizeText.length;
  }

  /** count the text length (without SSML tag). It depends on ttsCoreVersion to identify SSML tag */
  static countSentenceTextLength(text, ttsCoreVersion) {
    if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
      return SynthesisService.countTextLength(text, REGEX.ADVANCE_TAG);
    }
    return SynthesisService.countTextLength(text, undefined);
  }

  // ADVISE: should encapsulate the input in its own __TTSRequest (instead of bloating the HttpRequest object with request.sentences)
  /** get synthesis type from request, multi sentences should be grouped */
  static getSynthesisType(request) {
    if (request.sentences?.length) return SYNTHESIS_TYPE.MULTI_VOICE;
    return SYNTHESIS_TYPE.SINGLE_VOICE;
  }
}

module.exports = { SynthesisService };
