const jwt = require('jsonwebtoken');
const moment = require('moment');
const appDao = require('../daos/app');
const { APP_ROLE } = require('../constants');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { syncApp } = require('./sync');
const { AuthorizationService } = require('./authorization');
const { PaygApiAuthorizationService } = require('./authorization/PaygApi');
const { RandomFactory } = require('../utils/random');

/** Get single object by id. By default, sentitive fields are redacted */
const getApp = async (id, shouldRedact = true) => {
  const app = await appDao.findAppWithSecretKey({ _id: id });
  if (!shouldRedact) return app;

  if (app?.secretKey) delete app.secretKey;
  return app;
};

const createApp = async ({ userId, name, expiresAt }) => {
  const user = await AuthorizationService.getUser(userId, true);
  await new PaygApiAuthorizationService(user).canCreateApp(true);

  const secretKey = RandomFactory.getGuid();
  let token = '';

  const expiresTime = expiresAt ? moment(expiresAt).endOf('day') : undefined;
  if (expiresTime) {
    const expiresIn = expiresTime.diff(moment(), 'seconds');
    token = await jwt.sign({}, secretKey, { expiresIn: `${expiresIn}s` });
  } else {
    token = await jwt.sign({}, secretKey);
  }

  const appId = RandomFactory.getGuid();
  const app = await appDao.createApp({
    _id: appId,
    name,
    secretKey,
    token,
    expiresAt: expiresTime,
    members: [{ userId, role: APP_ROLE.ADMIN }],
  });

  const appDB = await getApp(appId, false);

  appDao.storeAppToCache(appDB);
  syncApp(app);

  const { secretKey: appSecretKey, ...appWithoutSecretKey } = app;

  return appWithoutSecretKey;
};

const updateApp = async (appId, updateFields) => {
  const app = await appDao.updateApp(appId, updateFields);
  appDao.updateAppInRedis(appId, updateFields);

  syncApp(app);

  const { secretKey: appSecretKey, ...appWithoutSecretKey } = app;

  return appWithoutSecretKey;
};

const getApps = async ({
  search,
  searchFields,
  dateField,
  query,
  offset,
  limit,
  fields,
  sort,
}) => {
  const queryFields = {};
  if (search) queryFields.search = search;
  if (offset) queryFields.offset = parseInt(offset, 10);
  if (limit) queryFields.limit = parseInt(limit, 10);
  if (fields) queryFields.fields = fields.split(',');
  if (sort) queryFields.sort = sort.split(',');
  if (query) queryFields.query = { ...queryFields.query, ...query };
  if (dateField) queryFields.dateField = dateField;
  if (searchFields) queryFields.searchFields = searchFields.split(',');

  const { apps, total } = await appDao.findApps(queryFields);

  return { apps, total };
};

const checkAppPermission = async (appId, appToken) => {
  const app = await getApp(appId, false);
  const { secretKey, token } = app;

  if (!secretKey) throw new CustomError(errorCodes.UNAUTHORIZED);
  if (appToken !== token) throw new CustomError(errorCodes.UNAUTHORIZED);
};

// ADVISE: extract into PaygApiService. App here is the management entity to manage API usage
module.exports = { createApp, updateApp, getApp, getApps, checkAppPermission };
