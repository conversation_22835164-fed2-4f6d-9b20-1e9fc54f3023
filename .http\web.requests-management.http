# dev.vbee.vn
@requestId = 0eb64f30-7c3f-4be3-a31b-8ed18dd379fc

# ============================================================================
# REQUEST MANAGEMENT ENDPOINTS
# 📋 Request Management (8 endpoints)
# Get requests (V1 & V2)
# Request details, progress, audio access
# Request updates and error reporting
# ============================================================================

### Get Requests (V1)
GET {{baseUrl}}/api/v1/requests?offset=0&limit=10
Authorization: Bearer {{token}}

### Get Requests (V2)
GET {{baseUrl}}/api/v2/requests?offset=0&limit=10&fields=id,title,characters,createdAt,progress,status,voice,audioType,audioLink,retentionPeriod,processingAt,endedAt&sort=createdAt_desc
Authorization: Bearer {{token}}

### Get Specific Request (V1)
GET {{baseUrl}}/api/v1/requests/e787545c-8c68-434f-b17b-23b45e55dbd1
Authorization: Bearer {{token}}

### Get Specific Request (V2)
GET {{baseUrl}}/api/v2/requests/{{requestId}}
Authorization: Bearer {{token}}

### Get Request Progress
GET {{baseUrl}}/api/v1/requests/{{requestId}}/progress
Authorization: Bearer {{token}}


### Get Request Audio
GET {{baseUrl}}/api/v1/requests/{{requestId}}/audio
Authorization: Bearer {{token}}


### Get Request Audio Download URL
GET {{baseUrl}}/api/v1/requests/{{requestId}}/audio-download-url
Authorization: Bearer {{token}}

### Update Request Paragraphs
PUT {{baseUrl}}/api/v1/requests/{{requestId}}/paragraphs
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "paragraphs": [
        {
            "text": "Updated paragraph text",
            "voice_code": "hn_female_ngochuyen_full_48k-fhg"
        }
    ]
}

### Create Error Report for Request
POST {{baseUrl}}/api/v1/requests/{{requestId}}/error-report
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "error_type": "audio_quality",
    "description": "Audio quality is poor",
    "expected_result": "Clear audio output"
}
