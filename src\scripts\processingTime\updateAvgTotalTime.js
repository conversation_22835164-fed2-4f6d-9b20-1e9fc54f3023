require('dotenv').config();
require('../../models');

const moment = require('moment');
const ProcessingTime = require('../../models/processingTime');
const Request = require('../../models/request');

const logger = require('../../utils/logger');

global.logger = logger;

const calculateAvgTotalTime = (totalTime, characters) => {
  const avgProcessingTime = Math.round((totalTime / (characters || 1)) * 1000);
  return avgProcessingTime;
};

const updateAvgTotalTime = async () => {
  const endOfYesterday = moment().subtract(1, 'days').endOf('day').toDate();

  const conditions = { createdAt: { $lte: new Date(endOfYesterday) } };

  const countAvgTotalTime = await ProcessingTime.countDocuments(conditions);

  logger.info(`Total processing time: ${countAvgTotalTime}`, {
    ctx: 'UpdateAvgTotalTime',
  });
  const UPDATE_BATCH = 1000;

  for (let i = 0; i < countAvgTotalTime; i += UPDATE_BATCH) {
    const processingTimes = await ProcessingTime.find(conditions)
      .skip(i)
      .limit(UPDATE_BATCH);

    const bulkUpdate = [];

    for (const processingTime of processingTimes) {
      const { _id: requestId, totalTime } = processingTime;
      const request = await Request.findById(requestId);
      const { characters } = request;
      const avgTotalTime = calculateAvgTotalTime(totalTime, characters);

      bulkUpdate.push({
        updateOne: {
          filter: { _id: requestId },
          update: { $set: { avgTotalTime } },
        },
      });
    }

    await ProcessingTime.bulkWrite(bulkUpdate);

    logger.info(`Updated ${i + UPDATE_BATCH} processing times`, {
      ctx: 'UpdateAvgTotalTime',
    });
  }
};

(async () => {
  await updateAvgTotalTime();
  process.exit(1);
})();
