const NodeDeviceDetector = require('node-device-detector');
const {
  Constants: { DEVICE, CLIENT_TYPE },
} = require('@vbee-holding/vbee-tts-models');
const logger = require('../utils/logger');

const getDevice = (req, res, next) => {
  let device = DEVICE.DESKTOP;
  let deviceInfo = {};
  const userAgent = req.headers['user-agent'];

  const originalUrl = req.baseUrl ? `${req.baseUrl}${req.url}` : req.url;
  const method = req.method.toUpperCase();

  try {
    const detector = new NodeDeviceDetector();
    deviceInfo = detector.detect(userAgent);

    const { type } = deviceInfo?.device || {};

    switch (type) {
      case DEVICE.SMARTPHONE:
      case DEVICE.TABLET:
      case DEVICE.TELEVISION:
        device = type;
        break;

      default:
        break;
    }

    if (userAgent?.includes('Dart')) {
      deviceInfo.client.type = CLIENT_TYPE.APP;
      deviceInfo.device.type = DEVICE.SMARTPHONE;
    }
  } catch (error) {
    logger.error(`${method} - ${originalUrl} - Cannot get device`, {
      ctx: 'GetDevice',
      userAgent,
    });
  }
  req.device = device;
  req.deviceInfo = deviceInfo;

  return next();
};

module.exports = getDevice;
