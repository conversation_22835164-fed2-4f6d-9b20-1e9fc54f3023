require('dotenv').config();
require('../../models');

const InProgressRequest = require('../../models/inProgressRequest');
const { REQUEST_STATUS } = require('../../constants');
const logger = require('../../utils/logger');

global.logger = logger;

const removeInprogressRequests = async () => {
  logger.info('Starting remove in progress requests...', { ctx: 'RunScript' });

  const wrongInprogressRequests = await InProgressRequest.aggregate([
    {
      $lookup: {
        from: 'requests',
        localField: '_id',
        foreignField: '_id',
        as: 'request',
      },
    },
    { $unwind: '$request' },
    { $match: { 'request.status': { $ne: REQUEST_STATUS.IN_PROGRESS } } },
  ]);

  const wrongInprogressRequestIds = wrongInprogressRequests.map(
    (inProgressRequest) => inProgressRequest._id,
  );

  await InProgressRequest.deleteMany({
    _id: { $in: wrongInprogressRequestIds },
  });

  logger.info('Remove in progress requests successfully', { ctx: 'RunScript' });
};

(async () => {
  await removeInprogressRequests();
  process.exit(1);
})();
