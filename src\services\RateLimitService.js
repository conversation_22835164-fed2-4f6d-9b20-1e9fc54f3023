const {
  REDIS_KEY_PREFIX,
  PACKAGE_FEATURE,
  REQUEST_TYPE,
} = require('../constants');
const { RATE_LIMIT_TYPE } = require('../constants/rateLimiter');
const { findRequest } = require('../daos/request');
const BannedAccountService = require('./BannedAccountService');
const Caching = require('../caching');

class RateLimitService {
  constructor(rateLimitConfig) {
    this.rateLimitConfig = rateLimitConfig || {};
  }

  static getCacheKey(userId, feature) {
    if (feature === PACKAGE_FEATURE.PREVIEW)
      return `${REDIS_KEY_PREFIX.PREVIEW_TTS_RATE_LIMIT}_${userId}`;

    return `${REDIS_KEY_PREFIX.TTS_RATE_LIMIT}_${userId}`;
  }

  static getTtl(rateLimitFeatureConfig) {
    return rateLimitFeatureConfig?.ttl;
  }

  static get(rateLimitFeatureConfig) {
    return rateLimitFeatureConfig?.limit;
  }

  getBannedReason(feature) {
    if (feature === PACKAGE_FEATURE.PREVIEW)
      return `User exceeded preview rate limit`;

    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    return `User exceeded ${feature} rate limit ${rateLimitFeatureConfig?.limit} times in ${rateLimitFeatureConfig?.ttl}s`;
  }

  async isExceedPreview(userId, feature) {
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    if (!rateLimitFeatureConfig) return false;

    // Get lastest request demo in db
    const latestRequest = await findRequest(
      { userId, type: REQUEST_TYPE.STUDIO, demo: true },
      ['_id', 'endedAt', 'characters'],
    );
    if (!latestRequest) return false;

    const { endedAt, characters } = latestRequest;
    const textLengthByThousand = characters / 1000;
    const startTime = new Date();
    // Convert to seconds
    const distance = endedAt ? (startTime - endedAt) / 1000 : 0;

    if (
      distance / textLengthByThousand <=
      RateLimitService.getTtl(rateLimitFeatureConfig)
    )
      return true;

    return false;
  }

  async isExceed(userId, feature) {
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    if (!rateLimitFeatureConfig) return false;

    if (feature === PACKAGE_FEATURE.PREVIEW)
      return this.isExceedPreview(userId, feature);

    const rateLimitKey = RateLimitService.getCacheKey(userId, feature);
    const count = await Caching.GlobalCounter.increase(rateLimitKey);
    if (count === 1)
      await Caching.RedisRepo.expire(
        rateLimitKey,
        RateLimitService.getTtl(rateLimitFeatureConfig),
      );

    if (count >= RateLimitService.get(rateLimitFeatureConfig)) return true;

    return false;
  }

  isBanned(userId, feature) {
    const DEFAULT_MAX_VIOLATIONS = 3;
    const rateLimitFeatureConfig = this.rateLimitConfig[feature];
    if (!rateLimitFeatureConfig) return false;

    const bannedAccount = BannedAccountService.find({
      type: RATE_LIMIT_TYPE.USER_ID,
      value: userId,
      feature,
    });

    if (!bannedAccount) return false;

    // Add 1 for the current request
    if (
      bannedAccount.times + 1 >=
      (rateLimitFeatureConfig.maxViolations || DEFAULT_MAX_VIOLATIONS)
    )
      return true;

    return false;
  }
}

module.exports = { RateLimitService };
