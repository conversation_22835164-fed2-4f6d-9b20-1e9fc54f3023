const callbackResultService = require('../services/callbackResult');

const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const getCallbackResult = async (req, res) => {
  const { requestId } = req.params;
  const { authorization } = req.headers;

  if (!authorization) throw new CustomError(errorCodes.UNAUTHORIZED);
  const [tokenType, accessToken] = authorization.split(' ');
  if (tokenType !== 'Bearer') throw new Error(errorCodes.UNAUTHORIZED);

  const callbackResult = await callbackResultService.getCallbackResult(
    requestId,
    accessToken,
  );

  return res.send(callbackResult);
};

module.exports = { getCallbackResult };
