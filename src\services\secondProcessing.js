const { KAFKA_TOPIC, SYNC_SECONDS_EVENT } = require('../constants');
const { sendMessage } = require('./kafka/producer');

const spendSeconds = ({ userId, requestId, seconds }) => {
  sendMessage(KAFKA_TOPIC.SECONDS_PROCESSING, {
    key: userId,
    value: {
      event: SYNC_SECONDS_EVENT.SPEND,
      userId,
      requestId,
      seconds,
    },
  });
};

const refundSeconds = ({ userId, appId, requestId }) => {
  sendMessage(KAFKA_TOPIC.SECONDS_PROCESSING, {
    key: userId,
    value: {
      event: SYNC_SECONDS_EVENT.REFUND,
      userId,
      appId,
      requestId,
    },
  });
};

module.exports = { spendSeconds, refundSeconds };
