require('dotenv').config();
require('../../models');

const Order = require('../../models/order');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting create field lastResetBonusAt ...`, {
    ctx: 'RunScript',
  });
  const missingOrders = await Order.find(
    {
      lastResetBonusAt: { $exists: false },
    },
    { _id: 1 },
  );
  const orderIds = missingOrders.map((order) => order._id);
  await Order.updateMany(
    { _id: { $in: orderIds } },
    { $set: { lastResetBonusAt: new Date() } },
  );

  logger.info(`Create field lastResetBonusAt successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
