const dictionaryDao = require('../daos/dictionary');
const dictionaryService = require('../services/dictionary');

const getDictionary = async (req, res) => {
  const { userId } = req.user;
  const dictionary = await dictionaryDao.findDictionary(userId);

  return res.send({ words: dictionary?.words || [] });
};

const createDictionary = async (req, res) => {
  const { userId } = req.user;
  const { words } = req.body;

  await dictionaryService.createDictionary(userId, words);

  return res.send({});
};

module.exports = { getDictionary, createDictionary };
