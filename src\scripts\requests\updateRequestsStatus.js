require('dotenv').config();
require('../../models');
require('../../services/kafka/producer');

const moment = require('moment');
const logger = require('../../utils/logger');
const Request = require('../../models/request');
const InProgressRequest = require('../../models/inProgressRequest');
const { REQUEST_STATUS } = require('../../constants');
const { updateUserRedis, refundCharacters } = require('../shared');

global.logger = logger;

const getInprogressRequestIds = async () => {
  const checkTime = moment(new Date()).subtract(15, 'minutes').toDate();
  const query = {
    status: REQUEST_STATUS.IN_PROGRESS,
    createdAt: { $lte: checkTime },
  };

  const numOfInprogressRequests = await Request.countDocuments(query);
  logger.info(`Number of inprogress requests: ${numOfInprogressRequests}`, {
    ctx: 'RunScript',
  });

  let inProgressRequestIds = [];

  for (let i = 0; i < numOfInprogressRequests; i += 1000) {
    logger.info(`Getting inprogress requests from ${i} to ${i + 1000}`, {
      ctx: 'RunScript',
    });

    const inprogressRequests = await Request.find(query)
      .skip(i)
      .limit(1000)
      .select('_id')
      .lean();

    inProgressRequestIds = [
      ...inProgressRequestIds,
      ...inprogressRequests.map((request) => request._id),
    ];
  }

  logger.info(
    `Number of inprogress request ids: ${inProgressRequestIds.length}`,
    { ctx: 'RunScript' },
  );

  return inProgressRequestIds;
};

const getRealInprogressRequestIds = async (requestIds) => {
  const inprogressRequests = await InProgressRequest.find({
    _id: { $in: requestIds },
  })
    .select('_id')
    .lean();

  const inprogressRequestIds = inprogressRequests.map((request) => request._id);

  return inprogressRequestIds;
};

const getUserRequests = async (requestIds) => {
  const data = await Request.aggregate([
    { $match: { _id: { $in: requestIds } } },
    {
      $group: {
        _id: { userId: '$userId', demo: '$demo', type: '$type' },
        numOfRequests: { $sum: 1 },
        requests: {
          $addToSet: {
            _id: '$_id',
            characters: '$characters',
            packageCode: '$packageCode',
          },
        },
      },
    },
  ]);

  const userRequests = data.reduce((acc, item) => {
    const { userId, demo, type } = item._id;
    const { numOfRequests, requests } = item;

    if (userId && !demo)
      return [...acc, { userId, numOfRequests, requests, type }];
    return acc;
  }, []);

  return userRequests;
};

const updateRequestsStatus = async (requestIds) => {
  await Request.updateMany(
    { _id: { $in: requestIds } },
    {
      status: REQUEST_STATUS.FAILURE,
      error: 'Request timed out',
    },
  );
};

(async () => {
  logger.info('Starting update status requests...', { ctx: 'RunScript' });

  const inprogressRequestIds = await getInprogressRequestIds();
  const realInprogressRequestIds = await getRealInprogressRequestIds(
    inprogressRequestIds,
  );
  const wrongInprogressRequestIds = inprogressRequestIds.filter(
    (request) => !realInprogressRequestIds.includes(request),
  );
  logger.info(
    `Number of wrong inprogress request ids: ${wrongInprogressRequestIds.length}`,
    { ctx: 'RunScript' },
  );

  const userRequests = await getUserRequests(wrongInprogressRequestIds);

  await updateUserRedis(userRequests);
  logger.info(`Update User in Cache success`, { ctx: 'RunScript' });

  // Refund characters
  await refundCharacters(userRequests);
  logger.info(`Refund characters success`, { ctx: 'RunScript' });

  // Update request status in database
  await updateRequestsStatus(wrongInprogressRequestIds);

  logger.info('Update status requests successfully', { ctx: 'RunScript' });
})();
