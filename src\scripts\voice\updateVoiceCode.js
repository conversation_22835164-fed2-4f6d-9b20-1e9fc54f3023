/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');

const Request = require('../../models/request');
const Tts = require('../../models/tts');

const logger = require('../../utils/logger');

const voiceCodes = [
  { old: 'en-US-Standard-D-MALE', new: 'en-US-Standard-D' },
  { old: 'en-US-Standard-E-FEMALE', new: 'en-US-Standard-E' },
  { old: 'en-US-Wavenet-D-MALE', new: 'en-US-Wavenet-D' },
  { old: 'en-US-Wavenet-F-FEMALE', new: 'en-US-Wavenet-F' },
];

const updateVoiceCodes = async (oldVoiceCode, newVoiceCode) => {
  logger.info(`Starting update ${oldVoiceCode} for request...`, {
    ctx: 'RunScript',
  });

  const requests = await Request.find({ voiceCode: oldVoiceCode })
    .select('voiceCode')
    .lean();

  const totalRequests = requests.length;
  const split = 1;
  let flag = 1;

  for (const [index, request] of requests.entries()) {
    try {
      if (index === 0 || flag * split === index) {
        flag += 1;
        logger.info(
          `Update ${((index * 100) / totalRequests).toFixed(2)}% request`,
          { ctx: 'RunScript' },
        );
      }

      const { _id: requestId } = request;
      await Request.findByIdAndUpdate(requestId, { voiceCode: newVoiceCode });
      await Tts.updateMany({ requestId }, { voiceCode: newVoiceCode });
    } catch (error) {
      logger.error(
        `Update ${oldVoiceCode} for request failed - ${request._id}`,
        { ctx: 'RunScript', stack: error.stack },
      );
      throw error;
    }
  }
};

(async () => {
  for (const voiceCode of voiceCodes) {
    const { old: oldVoiceCode, new: newVoiceCode } = voiceCode;
    await updateVoiceCodes(oldVoiceCode, newVoiceCode);
    logger.info(`Update ${oldVoiceCode} for request successfully`, {
      ctx: 'RunScript',
    });
  }
  process.exit(1);
})();
