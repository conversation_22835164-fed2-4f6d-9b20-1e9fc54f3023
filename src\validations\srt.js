const { Jo<PERSON>, validate } = require('express-validation');
const { DUBBING_SOURCE } = require('../constants/dubbing');

const extractSrtContentFromLink = {
  body: Joi.object({
    link: Joi.string().required(),
    source: Joi.string()
      .valid(...Object.values(DUBBING_SOURCE))
      .required(),
    originalLanguage: Joi.string().trim().optional(),
    isTranslate: Joi.boolean().optional(),
  }),
};

module.exports = {
  extractSrtContentFromLinkValidate: validate(extractSrtContentFromLink, {
    keyByField: true,
  }),
};
