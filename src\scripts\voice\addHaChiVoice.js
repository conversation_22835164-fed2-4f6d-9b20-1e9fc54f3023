require('dotenv').config();
const yargs = require('yargs/yargs');
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const getVoice = (env) => {
  return {
    code: 'hn_female_hachi_book_22k-vc',
    name: '<PERSON><PERSON> <PERSON> <PERSON><PERSON>',
    gender: 'female',
    languageCode: 'vi-VN',
    type: 'Neural TTS',
    provider: 'vbee',
    squareImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ha-chi.png', // bucket of core account
    roundImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ha-chi.png',
    demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_hachi_book_22k-vc.mp3', // bucket of product account
    rank: 3,
    synthesisFunction: `function:a2s-stable-${env}-fonos02-22k-stable`,
    active: true,
    defaultSampleRate: 22050,
    sampleRates: [8000, 16000, 22050],
    level: 'PREMIUM',
    styles: ['narration'],
    beta: true,
    features: ['premium-vietnam-voice'],
  };
};

(async () => {
  const { argv = {} } = yargs(process.argv.slice(2));
  const { env } = argv;
  logger.info(`Starting create Ha Chi voice...`, { ctx: 'RunScript' });
  const voice = getVoice(env);
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Ha Chi successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();

// Run script: node src/scripts/voice/addHaChiVoice.js --env="dev"
