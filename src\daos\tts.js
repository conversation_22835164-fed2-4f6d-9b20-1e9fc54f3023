// ADVISE: what is Tts entity here?
require('dotenv').config();
const {
  TTS_STATUS,
  BREAK_LINE,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REQUEST_TYPE,
} = require('../constants');
const { SILENCE_BREAK_LINE_AUDIO } = require('../configs');
const Tts = require('../models/tts');
const {
  getSearchQuery,
  getSortQuery,
  getSelectQuery,
  getDateQuery,
} = require('./utils/util');

const logger = require('../utils/logger');
const { RandomFactory } = require('../utils/random');

const Caching = require('../caching');
const RequestCaching = require('../caching/requestCaching');

const checkSentenceExistInCache = async (requestId, sentenceIndex) => {
  const sentenceStatusKey = `${REDIS_KEY_PREFIX.SENTENCE_TOKENIZATION_STATUS}_${requestId}_${sentenceIndex}`;
  const sentenceStatus = await Caching.RedisRepo.get(sentenceStatusKey);

  return !!sentenceStatus;
};

const getStudioSentences = ({
  requestId,
  index,
  voiceCode,
  sentences,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const ttsRequests = sentences
    .filter((text) => text === BREAK_LINE || text.trim().length > 0)
    .map((text, subIndex) => ({
      requestId,
      index,
      subIndex,
      text,
      voiceCode,
      status: text === BREAK_LINE ? TTS_STATUS.SUCCESS : TTS_STATUS.IN_PROGRESS,
      audioLink: text === BREAK_LINE ? breakLineAudioUrl : undefined,
      silence: text === BREAK_LINE,
      awsZoneSynthesis,
    }));
  return ttsRequests;
};

const getDubbingSentences = ({
  requestId,
  index,
  voiceCode,
  sentences,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const ttsRequests = sentences
    .filter(
      (sentence) =>
        sentence.content === BREAK_LINE || sentence.content.trim().length > 0,
    )
    .map((sentence, subIndex) => {
      const { content, start, end } = sentence;
      return {
        requestId,
        index,
        subIndex,
        text: content,
        start,
        end,
        voiceCode,
        status:
          content === BREAK_LINE ? TTS_STATUS.SUCCESS : TTS_STATUS.IN_PROGRESS,
        audioLink: content === BREAK_LINE ? breakLineAudioUrl : undefined,
        silence: content === BREAK_LINE,
        awsZoneSynthesis,
      };
    });

  return ttsRequests;
};

const getTtsRequests = ({
  requestId,
  index,
  voiceCode,
  sentences,
  type,
  breakLineAudioUrl,
  awsZoneSynthesis,
}) => {
  const getTtsRequestsFunc =
    type === REQUEST_TYPE.DUBBING ? getDubbingSentences : getStudioSentences;
  return getTtsRequestsFunc({
    requestId,
    index,
    voiceCode,
    sentences,
    breakLineAudioUrl,
    awsZoneSynthesis,
  });
};

const saveSentences = async ({
  requestId,
  index,
  sentences,
  type,
  voiceCode,
  audioType,
  sampleRate = '16000',
  awsZoneSynthesis,
}) => {
  const isSaved = await checkSentenceExistInCache(requestId, index);
  if (isSaved) return null;

  const sentenceStatusKey = `${REDIS_KEY_PREFIX.SENTENCE_TOKENIZATION_STATUS}_${requestId}_${index}`;
  await Caching.GlobalCounter.increase(sentenceStatusKey);

  await Caching.RedisRepo.expire(
    sentenceStatusKey,
    REDIS_KEY_TTL.SENTENCE_TOKENIZATION_STATUS,
  );

  const breakLineAudioUrl = `${SILENCE_BREAK_LINE_AUDIO}-${sampleRate}Hz.${audioType}`;

  const ttsRequests = getTtsRequests({
    requestId,
    index,
    voiceCode,
    sentences,
    type,
    breakLineAudioUrl,
    awsZoneSynthesis,
  });

  const ttsIdRequests = await RequestCaching.saveTtsInRedis({
    requestId,
    index,
    ttsRequests,
  });

  return { ttsIdRequests };
};

const saveAudio = async ({
  requestId,
  index,
  subIndex,
  audioLink,
  t2aDuration,
  synthesisDuration,
}) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    { audioLink, status: TTS_STATUS.SUCCESS, t2aDuration, synthesisDuration },
  );
};

const saveAudioInRedis = async ({
  requestId,
  ttsId,
  audioLink,
  audioName,
  t2aDuration,
  synthesisDuration,
  phrases,
}) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await Caching.RedisRepo.get(ttsKey);
  if (!tts) return;

  const ttsObj = JSON.parse(tts);
  const newTts = {
    ...ttsObj,
    audioName,
    audioLink,
    status: TTS_STATUS.SUCCESS,
    t2aDuration,
    synthesisDuration,
    phrases,
  };
  await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
};

const updateFailureTTS = async ({ requestId, index, subIndex, error }) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    { status: TTS_STATUS.FAILURE, error },
  );
};

const updateFailureTTSInRedis = async ({ requestId, ttsId, error }) => {
  const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
  const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

  const tts = await Caching.RedisRepo.get(ttsKey);
  if (!tts) return null;

  const ttsObj = JSON.parse(tts);
  const newTts = { ...ttsObj, status: TTS_STATUS.FAILURE, error };

  await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);

  return newTts;
};

const checkCompletedIndex = async (requestId, index) => {
  const ttsRequest = await Tts.findOne({
    requestId,
    index,
    status: { $ne: TTS_STATUS.SUCCESS },
  });
  return !ttsRequest;
};

const checkCompletedIndexInRedis = async (requestId, index) => {
  const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(
    requestId,
    index,
  );

  const isNotCompleted = ttsList.some(
    (tts) => tts.status !== TTS_STATUS.SUCCESS,
  );
  return !isNotCompleted;
};

const getAudios = async (requestId) => {
  const tts = await Tts.aggregate([
    { $match: { requestId } },
    { $project: { index: 1, subIndex: 1, audioLink: 1, phrases: 1 } },
    { $sort: { index: 1, subIndex: 1 } },
  ]);

  // ADVISE: duplicated code const audios = tts.reduce((acc, curr)
  const audios = tts.reduce((acc, curr) => {
    const { audioLink, phrases = [] } = curr;
    if (audioLink) return [...acc, audioLink];

    const sortPhrases = phrases.sort((a, b) => a.index - b.index);
    const audioLinks = sortPhrases.map((phrase) => phrase.audioLink);
    return [...acc, ...audioLinks];
  }, []);

  return audios;
};

const getAudiosInRedis = async (requestId) => {
  const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(requestId);

  const sortTtsList = ttsList.sort((a, b) => {
    return a.index - b.index || a.subIndex - b.subIndex;
  });

  // ADVISE: duplicated code const audios = tts.reduce((acc, curr)
  const audios = sortTtsList.reduce((acc, curr) => {
    const { audioLink, phrases = [] } = curr;
    if (audioLink) return [...acc, audioLink];

    const sortPhrases = phrases.sort((a, b) => a.index - b.index);
    const audioLinks = sortPhrases.map((phrase) => phrase.audioLink);
    return [...acc, ...audioLinks];
  }, []);

  return audios;
};

const getSynthesisTime = async (requestId) => {
  const requests = await Tts.find(
    { requestId },
    { t2aDuration: 1, synthesisDuration: 1 },
  ).lean();

  const t2aDurations = requests.reduce(
    (prev, curr) => (curr.t2aDuration ? [...prev, curr.t2aDuration] : prev),
    [],
  );
  const synthesisDurations = requests.reduce(
    (prev, curr) =>
      curr.synthesisDuration ? [...prev, curr.synthesisDuration] : prev,
    [],
  );

  return { t2aDurations, synthesisDurations };
};

const getSynthesisTimeInRedis = async (requestId) => {
  const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(requestId);

  const t2aDurations = ttsList.reduce(
    (prev, curr) => (curr.t2aDuration ? [...prev, curr.t2aDuration] : prev),
    [],
  );
  const synthesisDurations = ttsList.reduce(
    (prev, curr) =>
      curr.synthesisDuration ? [...prev, curr.synthesisDuration] : prev,
    [],
  );

  return { t2aDurations, synthesisDurations };
};

const getTotalTtsByRequestId = async (requestId) => {
  const totalTtsRequest = await Tts.countDocuments({ requestId });
  return totalTtsRequest;
};

const getTotalTtsByRequestIdInRedis = async (requestId) => {
  const request = await RequestCaching.findRequestByIdInRedis(requestId);
  const totalTtsRequest = request?.ttsIds?.length || 0;
  return totalTtsRequest;
};

const getTotalSuccessTtsByRequestId = async (requestId) => {
  const totalTtsRequest = await Tts.countDocuments({
    requestId,
    status: TTS_STATUS.SUCCESS,
  });

  return totalTtsRequest;
};

const getTotalSuccessTtsByRequestIdInRedis = async (requestId) => {
  const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(requestId);

  const totalSuccessTts = ttsList.filter(
    (tts) => tts.status === TTS_STATUS.SUCCESS,
  ).length;

  return totalSuccessTts;
};

const deleteTTSByRequestId = async (requestId) => {
  await Tts.deleteMany({ requestId });
};

const findTts = async ({
  search,
  searchFields = [],
  dateField = 'createdAt',
  query,
  offset,
  limit,
  fields,
  sort,
}) => {
  const s = getSearchQuery(Tts, searchFields, search);

  // eslint-disable-next-line prefer-const
  let { startDate, endDate, ...dataQuery } = query || {};

  if (startDate || endDate) {
    const dateQuery = getDateQuery(dateField, query.startDate, query.endDate);
    dataQuery = { ...dataQuery, ...dateQuery };
  }

  const total = await Tts.countDocuments(
    search ? { $or: s, ...dataQuery } : dataQuery,
  );

  const pipeline = [
    { $match: search ? { $or: s, ...dataQuery } : dataQuery },
    {
      $lookup: {
        from: 'voices',
        localField: 'voiceCode',
        foreignField: 'code',
        as: 'voice',
      },
    },
    { $unwind: '$voice' },
  ];

  const filterPipe = [{ $skip: offset || 0 }, { $limit: limit || null }];

  if (sort) filterPipe.sort = getSortQuery(sort);
  if (fields) filterPipe.fields = getSelectQuery(fields);

  const tts = await Tts.aggregate([...pipeline, ...filterPipe]);

  return { tts, total };
};

const countRealTts = async (requestId) => {
  const total = await Tts.countDocuments({
    requestId,
    silence: { $ne: true },
  });
  return total;
};

const updateCachePhrases = async ({ requestId, index, subIndex, phrases }) => {
  await Tts.updateOne(
    { requestId, index, subIndex },
    {
      phrases,
      index,
      subIndex,
      requestId,
      status: TTS_STATUS.SUCCESS,
      t2aDuration: 0,
      synthesisDuration: 0,
    },
  );
};

const updateCachePhrasesInRedis = async ({
  requestId,
  ttsId,
  index,
  subIndex,
  phrases,
}) => {
  try {
    const ttsKey = `${REDIS_KEY_PREFIX.TTS}_${requestId}_${ttsId}`;
    const ttsKeyTtl = REDIS_KEY_TTL.SYNTHESIS_TTS;

    const tts = await Caching.RedisRepo.get(ttsKey);
    if (!tts) return;

    const ttsObj = JSON.parse(tts);
    const newTts = {
      ...ttsObj,
      phrases,
      requestId,
      index,
      subIndex,
      status: TTS_STATUS.SUCCESS,
      t2aDuration: 0,
      synthesisDuration: 0,
    };

    await Caching.RedisRepo.set(ttsKey, JSON.stringify(newTts), ttsKeyTtl);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

const migrateTtsFromRedisToDB = async (requestId) => {
  try {
    const ttsList = await RequestCaching.getTtsFromRequestIdInRedis(requestId);
    const migratedTtsKey = `${REDIS_KEY_PREFIX.MIGRATED_TTS}_${requestId}`;
    const checkMigrate = await Caching.GlobalCounter.increase(migratedTtsKey);

    if (checkMigrate > 1) return;

    await Tts.insertMany(ttsList);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
  }
};

module.exports = {
  saveSentences,
  saveAudio,
  saveAudioInRedis,
  updateFailureTTS,
  updateFailureTTSInRedis,
  checkCompletedIndex,
  checkCompletedIndexInRedis,
  getAudios,
  getAudiosInRedis,
  getSynthesisTime,
  getSynthesisTimeInRedis,
  getTotalTtsByRequestId,
  getTotalTtsByRequestIdInRedis,
  getTotalSuccessTtsByRequestId,
  getTotalSuccessTtsByRequestIdInRedis,
  deleteTTSByRequestId,
  findTts,
  countRealTts,
  updateCachePhrases,

  updateCachePhrasesInRedis,
  migrateTtsFromRedisToDB,
};
