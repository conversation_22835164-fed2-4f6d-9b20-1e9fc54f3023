/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');
require('../../services/init/iam');

const { VBEE_DUBBING_URL } = require('../../configs');
const Request = require('../../models/request');
const { getAccessToken } = require('../../services/iam');
const logger = require('../../utils/logger');
const callApi = require('../../utils/callApi');
const CustomError = require('../../errors/CustomError');
const code = require('../../errors/code');
const { PACKAGE_TYPE } = require('../../constants');

global.logger = logger;

const countRequests = (query) => Request.countDocuments(query);

const getRequests = (query, limit) => Request.find(query).limit(limit);

const updateRequest = async (requestId, updatedFields) =>
  Request.findByIdAndUpdate(requestId, updatedFields);

const createProject = async (projectData) => {
  try {
    const { status, result } = await callApi({
      url: `${VBEE_DUBBING_URL}/api/v1/projects`,
      method: 'POST',
      data: projectData,
      headers: { Authorization: `Bearer ${global.ACCESS_TOKEN}` },
    });

    if (status !== 1) throw new Error();
    return result;
  } catch (err) {
    logger.error(err, { ctx: 'CreateProject' });
    throw new CustomError(
      code.INTERNAL_SERVER_ERROR,
      'Failed to create dubbing project',
    );
  }
};

const processRequest = async (request) => {
  const { _id: requestId, userId, status: requestStatus } = request;

  const projectData = {
    userId,
    title: request.title,
    speed: request.speed,
    voiceCode: request.voiceCode,
    currentSubtitleLink: request.subtitleLink,
    status: requestStatus,
    latestRequestId: requestId,
    createdAt: request.createdAt,
  };

  try {
    const project = await createProject(projectData);
    await updateRequest(requestId, { projectId: project.id });
  } catch (err) {
    logger.error(err, {
      ctx: 'CreateProject',
      userId,
      requestId,
    });
  }
};

const updateProjectIdIntoOldRequest = async () => {
  logger.info('Starting update project id into old request...', {
    ctx: 'RunScript',
  });

  global.ACCESS_TOKEN = await getAccessToken();

  const queryFindOldRequest = {
    type: PACKAGE_TYPE.DUBBING,
    projectId: { $exists: false },
    hidden: false,
    subtitleLink: { $ne: '' },
  };
  const numOfRequests = await countRequests(queryFindOldRequest);

  logger.info(`Found ${numOfRequests} requests`, {
    ctx: 'RunScript',
  });

  const BATCH_SIZE = 100;

  for (let i = 0; i < numOfRequests; i += BATCH_SIZE) {
    logger.info(`Processing ${i} - ${i + BATCH_SIZE} requests`, {
      ctx: 'RunScript',
    });

    const requests = await getRequests(queryFindOldRequest, BATCH_SIZE);
    if (!requests.length) break;

    await Promise.all(requests.map((request) => processRequest(request)));
  }

  logger.info('Created Done !!!', {
    ctx: 'UpdateProjectIdIntoOldRequest',
  });
};

(async () => {
  await updateProjectIdIntoOldRequest();
  process.exit(1);
})();
