/** Feature-flag keys */
const FEATURE_KEYS = {
  RECAPTCHA_TTS_TYPE: 'recaptcha-tts-type',
  RECAPTCHA_TRY_LISTEN_TTS_TYPE: 'recaptcha-try-listen-tts-type',
  DECRYPT_S3_KEY: 'decrypt-s3-key',
  AUDIO_URL: 'audio-url',
  DUBBING_BY_UNIT_SECOND: 'dubbing-by-unit-second',
  MULTIPLE_INPUT_DUBBING: 'multiple-input-dubbing',
  HEALTHCHECK: 'healthcheck',
  GRACEFUL_SHUTDOWN: 'graceful-shutdown',
  KAFKA_HEALTHCHECK: 'kafka-healthcheck',
  CLOUD_FRONT_AS_PROXY: 'cloud-front-as-proxy',
  TIMESTAMP_WORDS: 'timestamp-words',
  DATASENSES: 'datasenses',
  SYNTHESIS_COMPUTE_PLATFORM: 'synthesis-compute-platform',
  MIGRATE_FREE_PLAN: 'migrate-free-plan',
  VOICE_CLONING: 'voice-cloning',
  BL<PERSON>K_SPAM_EMAIL: 'block-spam-email',
  NON_VBEE_CREDITS_FACTOR: 'non-vbee-credits-factor',
  VOICE_CREDITS_BY_LEVEL: 'voice-credits-by-level',
  BLOCK_SYNTHESIS_REQUEST_FOR_SPAM_ACCOUNT:
    'block-synthesis-request-for-spam-account',
  LOG_LEVEL: 'log-level',
  NEW_AUDIO_FILE_NAME: 'new-audio-file-name',
  PROCESS_PRIORITIZE_REQUEST: 'process-prioritize-request',
  MIGRATE_VOICE_CLONING_FEATURE: 'migrate-voice-cloning-feature',
  MULTILINGUAL_VOICES: 'multilingual-voices',
  USE_USAGE_OPTIONS_IN_PACKAGE: 'use-usage-options-in-package',
  CREDIT_FACTOR: 'credit-factor',
  LOCK_ONE_TIME_CREDITS_ADVANCED_VOICE: 'lock-one-time-credits-advanced-voice',
  UPDATE_VOICES_BY_USER_EOL_DATE: 'update-voices-by-user-eol-date',
  API_REAL_TIME: 'api-real-time',
  CONFIG_STORAGE_TTS: 'config-storage-tts',
  EMPHASIS_FEATURE_V2: 'emphasis-feature-v2',
  USE_PUBLIC_VOICE_CLONING: 'use-public-voice-cloning',
  MAPPING_TTS_GATE_VOICE: 'mapping-tts-gate-voice',
  RATE_LIMIT_FEATURE: 'rate-limit-feature',
  RECOPY_AUDIO_FILE: 'recopy-audio-file',
};

module.exports = { FEATURE_KEYS };
