# ============================================================================
# VOICE & LANGUAGE ENDPOINTS
# 🎤 Voice & Language (8 endpoints)
# Get languages and voices (V1 & V2)
# Voice management (create, update)
# Voice cloning functionality

# ============================================================================

### Get languages
GET {{baseUrl}}/api/v1/languages

### Get voices
GET {{baseUrl}}/api/v1/voices

### Get voices V2 (with auth)
GET {{baseUrl}}/api/v2/voices
Authorization: Bearer {{token}}

### Create voices (Admin only)
POST {{baseUrl}}/api/v1/voices
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "voices": [
        {
            "code": "sample_voice_code",
            "name": "Sample Voice",
            "language": "vi",
            "gender": "female"
        }
    ]
}

### Update voice (Admin only)
PUT {{baseUrl}}/api/v1/voices/{{voiceId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Updated Voice Name",
    "active": true
}

### Get voice cloning voices
GET {{baseUrl}}/api/v1/voices/voice-cloning
Authorization: Bearer {{token}}

### Create voice cloning voice
POST {{baseUrl}}/api/v1/voices/voice-cloning
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "My Cloned Voice",
    "audioUrl": "https://example.com/audio.wav"
}

### Update cloned voice
PUT {{baseUrl}}/api/v1/voices/voice-cloning
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "voiceId": "voice-id",
    "name": "Updated Cloned Voice"
}
