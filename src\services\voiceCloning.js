const moment = require('moment');
const CustomError = require('../errors/CustomError');
const errorCode = require('../errors/code');
const voiceCloningDao = require('../daos/voiceCloning');
const callApi = require('../utils/callApi');
const {
  LANGUAGE_CODE,
  VC_SAMPLE_RATES,
  VC_DEFAULT_SAMPLE_RATE,
  VOICE_STATUS,
} = require('../constants/voiceCloning');
const { TTS_GATE_URL, AI_VOICE_TOKEN, AI_VOICE_APP_ID } = require('../configs');
const {
  VOICE_PROVIDER,
  VOICE_CLONING_TYPE,
  VOICE_TYPE,
} = require('../constants');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');

/** VoiceCloning code: start with prefix [ncs], someName(province), gender, username, VC category (podcast, or callcenter), suffix (vc) */
const VC_VOICE_CODE_REGEX =
  /[ncs]_[a-z]+_(male|female)_[a-z0-9]+(_zero_shot)?_[a-z]+_vc+$/;

const createVoiceInTTSGate = async (ttsPayload = {}) => {
  const res = await callApi({
    headers: { authorization: `Bearer ${AI_VOICE_TOKEN}` },
    url: `${TTS_GATE_URL}/api/v1/voices`,
    method: 'POST',
    data: {
      ...ttsPayload,
      appId: AI_VOICE_APP_ID,
      code: ttsPayload?.ttsGateCode || ttsPayload?.code,
    },
  });

  if (res.status !== 1)
    throw new CustomError(
      errorCode.CALL_API_CREATE_VOICE_IN_TTS_GATE_FAILED,
      res.message,
    );
};

/** check code string to see is it match VC standard of naming */
const isVoiceCloningCode = (code) => VC_VOICE_CODE_REGEX.test(code);

const getTTSGateVoiceType = (code, type) => {
  const isClonedVoice = isVoiceCloningCode(code);
  const isZeroShotVoice =
    isClonedVoice && type === VOICE_CLONING_TYPE.ZERO_SHOT;

  if (isZeroShotVoice) return VOICE_TYPE.ZERO_SHOT;

  if (isClonedVoice) return VOICE_TYPE.CLONING;

  return VOICE_TYPE.TTS;
};

const createVoiceCloningVoice = async ({
  userId,
  code,
  name,
  gender,
  avatar,
  locale,
  province,
  status,
  type,
  ttsGateCode,
  sampleLink,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);

  const createVoiceData = {
    code,
    name,
    image: avatar,
    gender,
    locale,
    province,
    status,
    languageCode: LANGUAGE_CODE.VI,
    provider: VOICE_PROVIDER.VBEE_VOICE_CLONING,
    squareImage: avatar,
    roundImage: avatar,
    sampleRates: VC_SAMPLE_RATES,
    defaultSampleRate: VC_DEFAULT_SAMPLE_RATE,
    type,
    ttsGateCode,
  };

  if (!voice) await createVoiceInTTSGate(createVoiceData);

  const ttsGateVoiceType = getTTSGateVoiceType(code, type);

  const voiceCloningVoice = await voiceCloningDao.createVoiceCloningVoice({
    ...createVoiceData,
    userId,
    type: ttsGateVoiceType,
    demo: sampleLink,
  });

  return voiceCloningVoice;
};

/**   
  1. `isValidateVoice` is used to prevent crash layout of project screen
      More info in func getVoiceInfoByCodes, getProject
      TODO: Dont base on input to split action of a function (high coupling)
      TODO: Add requestType to prevent api request can use public voice
  2. This func also get voice for get sample audio in pro voice cloning (from route /api/v1/tts)
   But when voice-cloning call to vbee-tts-api, voice doesnt have status, so at the moment, we still need to check status
   TODO: Mark a request come from voice-cloning or not, if true => dont need to check voice condition
*/
const getClonedVoiceByCode = async ({
  code,
  userId,
  isValidateVoice = true,
  canUseProVoiceCloning,
  canUseInstantVoiceCloning,
}) => {
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  // TODO: Remove FF
  const canUsePublicVoice = getFeatureValue(
    FEATURE_KEYS.USE_PUBLIC_VOICE_CLONING,
    { userId, voiceCode: code },
  );

  const {
    status,
    userId: voiceOwnerId,
    discardAt,
    type = VOICE_CLONING_TYPE.FINETUNE_SCRIPTS_RECORDING,
  } = voice;

  if (!isValidateVoice) return voice;

  if (status === VOICE_STATUS.PUBLIC) {
    if (!canUsePublicVoice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

    if (discardAt && moment().isAfter(discardAt))
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    return voice;
  }

  if (status === VOICE_STATUS.PRIVATE) {
    // Voice is private but users use voice of another user
    if (userId !== voiceOwnerId)
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    // Voice is private, users use their own voice but now they are not permitted to use this voice
    // Becase we compare userId and voiceOwnerId before, so from this to end of func, we dont need to compare them again
    if (
      type === VOICE_CLONING_TYPE.FINETUNE_SCRIPTS_RECORDING &&
      !canUseProVoiceCloning
    )
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);

    // At this time, only studio request can use instant voice so we need this condition
    if (type === VOICE_CLONING_TYPE.ZERO_SHOT && !canUseInstantVoiceCloning)
      throw new CustomError(errorCode.DISCARDED_VOICE_CLONING);
  }

  return voice;
};

const updateClonedVoice = async (updateData) => {
  const {
    userId,
    code,
    name,
    avatar,
    status,
    demo,
    active,
    retentionDays,
    discardAt,
    category,
  } = updateData;
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  if (voice.userId !== userId)
    throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const updateVoiceData = {
    name,
    image: avatar,
    squareImage: avatar,
    roundImage: avatar,
  };

  if (status) updateVoiceData.status = status;
  if (demo) updateVoiceData.demo = demo;
  if (active !== undefined && active !== null) updateVoiceData.active = active;
  if (retentionDays !== undefined && retentionDays !== null)
    updateVoiceData.retentionDays = retentionDays;
  if (discardAt) updateVoiceData.discardAt = discardAt;
  if (category) updateVoiceData.category = category;

  await voiceCloningDao.updateClonedVoiceInfo(code, updateVoiceData);
};

const getVoiceCloningVoice = async (code) => {
  // ADVISE: PERFORMANCE: should be cache using CacheService
  const voice = await voiceCloningDao.findVoiceCloningByCode(code);
  return voice;
};

const getCommunityVoiceCloningForUser = async (userId) => {
  const voices = await voiceCloningDao.findCommunityVoiceCloningForUser(userId);
  return voices;
};

module.exports = {
  createVoiceCloningVoice,
  getClonedVoiceByCode,
  updateClonedVoice,
  isVoiceCloningCode,
  getVoiceCloningVoice,
  getCommunityVoiceCloningForUser,
};
