/* eslint-disable no-underscore-dangle */
require('dotenv').config();
const logger = require('../../utils/logger');

global.logger = logger;
require('../../models');
const VoiceCloning = require('../../models/voiceCloning');

const formatVoiceCloningCode = (prefix, initalCode) => {
  const parts = initalCode.replace(/-/g, '_').split('_');

  if (parts.length < 4) {
    throw new Error('Input is not in the expected format.');
  }

  const region = parts[0];
  const gender = parts[1];
  const name = parts[2];
  const vendor = parts.slice(3).join('');

  return `${prefix}_${region}_${gender}_${name}_${vendor}_vc`;
};

const createVbeePublicVoices = async (seedVoices) => {
  logger.info('Starting create vbee public voices', {
    ctx: 'CreateVbeePublicVoices',
  });

  const operations = seedVoices.map((voice) => {
    const voiceCode = formatVoiceCloningCode('n', voice.code);
    return {
      updateOne: {
        filter: { code: voiceCode },
        update: { $set: { ...voice, code: voiceCode } },
        upsert: true,
      },
    };
  });

  try {
    await VoiceCloning.bulkWrite(operations);
    logger.info('Create Vbee public voices successfully', { ctx: 'RunScript' });
  } catch (error) {
    logger.error(`Failed to create Vbee public voices: ${error.message}`, {
      ctx: 'RunScript',
      error,
    });
  }
};

(async () => {
  const voiceCloningPublicData = require('./publicVbeeVoices.json');
  await createVbeePublicVoices(voiceCloningPublicData);
  process.exit(0);
})();
