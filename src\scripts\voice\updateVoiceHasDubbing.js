require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update voice has dubbing...`, {
    ctx: 'RunScriptUpdateVoiceHasDubbing',
  });

  const voices = require('./seedVoiceHasDubbing.json');
  for (const voice of voices) {
    const { code, hasDubbing } = voice;
    await Voice.findOneAndUpdate({ code }, { hasDubbing });
  }

  logger.info(`Update voice has dubbing successfully`, {
    ctx: 'RunScriptUpdateVoiceHasDubbing',
  });
  process.exit(1);
})();
