const { Joi, validate } = require('express-validation');
const { REQUEST_TYPE } = require('../constants');

const createAwsZone = {
  body: Joi.object({
    name: Joi.string().trim().required(),
    region: Joi.string().trim().required(),
    weight: Joi.number().min(1).required(),
    allowRequestTypes: Joi.array()
      .items(Joi.string().valid(...Object.values(REQUEST_TYPE)))
      .required(),
    normalizerFunctionName: Joi.string().trim().optional(),
    sentenceTokenizerFunctionName: Joi.string().trim().optional(),
    newSentenceTokenizerFunctionName: Joi.string().trim().optional(),
    textToAllophoneFunctionName: Joi.string().trim().optional(),
    synthesisFunctionName: Joi.string().trim().optional(),
    joinSentencesFunctionName: Joi.string().trim().optional(),
    defaultS3Bucket: Joi.string().trim().optional(),
  }),
};

module.exports = {
  createAwsZoneValidate: validate(createAwsZone, { keyByField: true }),
};
