const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const { auth } = require('../middlewares/auth');
const srtController = require('../controllers/srt');
const { extractSrtContentFromLinkValidate } = require('../validations/srt');

/* eslint-disable prettier/prettier */
router.post('/srt/extract-from-link', auth, extractSrtContentFromLinkValidate, asyncMiddleware(srtController.extractSrtContentFromLink));
/* eslint-disable prettier/prettier */


module.exports = router;
