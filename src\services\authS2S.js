const jwt = require('jsonwebtoken');
const camelCaseKeys = require('camelcase-keys');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const { IAM_CLIENT_ID } = require('../configs');

/** Verify token for Service to Service authentication */
const verifyAccessToken = async (accessToken, role) => {
  let data;
  let userId;
  let resourceAccess;
  try {
    const publicKey = IAM_PUBLIC_KEY;
    data = jwt.verify(accessToken, publicKey);
    ({ sub: userId, resource_access: resourceAccess } = data);
    if (!userId) throw new Error(errorCodes.UNAUTHORIZED, 'userId not found');

    const { roles = [] } = resourceAccess[IAM_CLIENT_ID] || {};
    const hasAccess = roles.includes(role);
    if (!hasAccess)
      // ADVISE: should use CustomError as standard coding convention
      throw new Error(errorCodes.UNAUTHORIZED, 'hasAccess not found');
  } catch (error) {
    logger.error(error.stack, { ctx: 'authS2S.verifyAccessToken' });
    throw new CustomError(errorCodes.UNAUTHORIZED);
  }

  return camelCaseKeys(data);
};

module.exports = { verifyAccessToken };
