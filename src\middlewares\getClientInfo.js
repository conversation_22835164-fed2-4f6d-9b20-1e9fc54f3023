const { HttpProtocolHelper } = require('@gurucore/lakdak');

/** decorate the request with extra ClientInfo (ip, physical location) of browser/client */
const getClientInfo = (req, res, next) => {
  const { headers } = req;
  const clientInfo = HttpProtocolHelper.extractClientInfo(headers);

  // ADVISE: avoid adding properties directly to the req, to avoid modify built-in req properies, and avoid override clashing with other middlewares
  /** @deprecated this line should be deleted after 3 iterations without seeing any problem */
  Object.assign(req, clientInfo);

  // When naming information attached to an HttpRequest in middleware for later use by subsequent middleware or controllers, a good approach focuses on clarity, consistency, and avoiding potential conflicts.
  Object.assign(req, { __clientInfo: clientInfo });

  return next();
};

module.exports = getClientInfo;
