const md5 = require('md5');
require('dotenv').config();
const { Constants } = require('@vbee-holding/vbee-tts-models');
const {
  REQUEST_STATUS,
  REQUEST_TYPE,
  KAFKA_TOPIC,
  SME_PACKAGE_CODES,
} = require('../constants');

const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const { getVoiceByCode } = require('./voice');
const { checkVoicePermission } = require('../utils/tts');
const { RandomFactory } = require('../utils/random');
const {
  preCheckSynthesisApiRequest,
  getTextFromTemplateAndTags,
  getTags,
} = require('./preprocessing');
const { createRequest } = require('./request');
const { sendMessage } = require('./kafka/producer');
const { updateRequestById } = require('../daos/request');
const { handleTtsSuccess } = require('./ttsProcessing');
const { sendPendingRequestToKafka } = require('./queue');
const { SynthesisService } = require('./SynthesisService');

const getAwsZoneSynthesis = () => {
  const lengthAwsZones = AWS_ZONES_TTS_CACHING.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_CACHING[randomIndex];
  return awsZone;
};

const handleCachingSynthesisRequest = async ({
  app,
  template,
  tags: tagsString,
  aesKey,
  voiceCode,
  outputType,
  responseType,
  backgroundMusic,
  speedRate,
  bitrate,
  sampleRate,
  callbackUrl,
  ip,
  serviceType,
  clientUserId,
  sessionId,
  receivedAt,
  endAuthProcessAt,
  clientSendRequestAt,
}) => {
  const requestId = RandomFactory.getGuid();
  const requestCreatedAt = new Date();
  const awsZoneSynthesis = getAwsZoneSynthesis();

  const {
    appId,
    userId,
    text,
    tags,
    features,
    price,
    packageCode,
    retentionPeriod,
    textLength,
  } = await preCheckSynthesisApiRequest({
    app,
    tagsString,
    template,
    sessionId,
    requestId,
    aesKey,
  });

  const endPreCheckAt = new Date();
  const endUploadBackgroundMusicAt = new Date();

  const voice = await getVoiceByCode(voiceCode);
  if (!voice) throw new CustomError(code.INVALID_VOICE_CODE);
  if (!voice.global && !checkVoicePermission(features, voice.features))
    throw new CustomError(code.UNAVAILABLE_VOICE);

  if (!voice.cachingFunction)
    throw new CustomError(code.TTS_CACHING_NOT_SUPPORT_THIS_VOICE);
  if (sampleRate && !voice.sampleRates.includes(sampleRate))
    throw new CustomError(
      code.INVALID_SAMPLE_RATE,
      `This voice only support sample rate of ${JSON.stringify(
        voice.sampleRates,
      )}`,
    );
  const endCheckVoiceAt = new Date();

  const request = {
    ip,
    requestId,
    template,
    tags: tagsString,
    text,
    characters: textLength,
    audioType: Constants.AUDIO_TYPE.WAV,
    speed: speedRate,
    createdAt: requestCreatedAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: sampleRate
      ? sampleRate.toString()
      : voice.defaultSampleRate.toString(),
    retentionPeriod,
    app: appId,
    userId,
    callbackUrl,
    responseType,
    outputType,
    type: REQUEST_TYPE.API_CACHING,
    packageCode,
    price: SME_PACKAGE_CODES.includes(packageCode) ? price : 0,
    aesKey,
    sessionId,
    serviceType,
    clientUserId,
    awsZoneSynthesis,
  };
  if (backgroundMusic) request.backgroundMusic = { link: backgroundMusic };

  await createRequest(request);

  logger.info('Created caching request', {
    ctx: 'CachingRequest',
    appId,
    sessionId,
    requestId,
  });
  const sendRequestSynthesisAt = new Date();

  updateRequestById(requestId, {
    timeProcess: {
      clientSendRequestAt: clientSendRequestAt
        ? new Date(clientSendRequestAt)
        : receivedAt,
      receivedAt,
      endAuthProcessAt,
      endPreCheckAt,
      endUploadBackgroundMusicAt,
      endCheckVoiceAt,
      sendRequestSynthesisAt,
    },
  });
  sendMessage(KAFKA_TOPIC.CACHING_SYNTHESIS_REQUEST, {
    value: {
      userId,
      requestId,
      sessionId,
      template,
      tags: JSON.stringify(tags),
      voiceCode,
      speedRate,
      bitrate,
      sampleRate,
      backgroundMusic,
      awsZoneSynthesis,
    },
  });

  return request;
};

const handleCachingSynthesisSuccess = async ({
  requestId,
  statusCode,
  audioLink,
  totalTime,
  cachingMessage,
  getSynthesisRequestAt,
  startInvokeLambdaFunctionAt,
  endInvokeLambdaFunctionAt,
}) => {
  const endedAt = new Date();
  const getSynthesisResponseAt = Date.now();

  const finalRequest = await updateRequestById(requestId, {
    progress: 100,
    endedAt,
    cachingTime: totalTime,
    status: REQUEST_STATUS.SUCCESS,
    audioLink,
    cachingStatusCode: statusCode,
    cachingMessage,
    'timeProcess.getSynthesisRequestAt': getSynthesisRequestAt,
    'timeProcess.startInvokeLambdaFunctionAt': startInvokeLambdaFunctionAt,
    'timeProcess.endInvokeLambdaFunctionAt': endInvokeLambdaFunctionAt,
    'timeProcess.getSynthesisResponseAt': getSynthesisResponseAt,
  });
  const preSendResponseAt = new Date();

  handleTtsSuccess(finalRequest, preSendResponseAt);
};

const handleCachingSynthesisFailure = async ({
  requestId,
  statusCode,
  cachingMessage,
  cachingTime,
}) => {
  const request = await updateRequestById(requestId, {
    cachingTime,
    cachingStatusCode: statusCode,
    cachingMessage,
  });

  const { userId } = request;

  await sendPendingRequestToKafka({
    userId,
    requestId,
  });
};

const getDetailDecryptedTemplates = ({
  templates,
  tags: tagsString,
  aesKey,
}) => {
  const tags = getTags({ tagsString, aesKey });

  const detailDecryptedTemplates = templates.reduce(
    (acc, curr) => {
      let { texts, totalLength } = acc;
      const text = getTextFromTemplateAndTags({ template: curr.text, tags });
      totalLength += SynthesisService.countTextLength(text, undefined);
      texts = [
        ...texts,
        { id: curr.id, hashText: md5(text), length: text.length },
      ];
      return { texts, totalLength };
    },
    {
      texts: [],
      totalLength: 0,
    },
  );

  return detailDecryptedTemplates;
};

module.exports = {
  handleCachingSynthesisRequest,
  handleCachingSynthesisSuccess,
  handleCachingSynthesisFailure,
  getDetailDecryptedTemplates,
};
