const SampleScript = require('../models/sampleScript');

const createSampleScripts = async (dataSampleScripts) => {
  const sampleScripts = await SampleScript.insertMany(dataSampleScripts);
  return sampleScripts;
};

const getActiveSampleScripts = async () => {
  const sampleScripts = await SampleScript.find({ isActive: true })
    .sort({ rank: 1 })
    .lean();
  return sampleScripts;
};

const deleteSampleScripts = async () => {
  await SampleScript.deleteMany();
};

module.exports = {
  createSampleScripts,
  getActiveSampleScripts,
  deleteSampleScripts,
};
