// ADVISE: Should bring to shared-lib. name and body of function is mismatch or hard to understand
/** With the provided object, convert (rescursively) all member with Long value type (is a type from Mongoose) to number */
const convertLongToNumber = (obj) => {
  if (obj instanceof Date) {
    return obj;
  }
  if (typeof obj !== 'object' || obj === null) return obj;

  if (
    'low' in obj &&
    'high' in obj &&
    typeof obj.low === 'number' &&
    typeof obj.high === 'number'
  ) {
    return obj.high * 2 ** 32 + obj.low; // Convert Mongoose.Long to Number
  }

  if (Array.isArray(obj)) {
    return obj.map(convertLongToNumber);
  }

  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      convertLongToNumber(value),
    ]),
  );
};

module.exports = { convertLongToNumber };
