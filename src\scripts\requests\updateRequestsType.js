/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');
const { REQUEST_TYPE } = require('../../constants');
const Request = require('../../models/request');
const logger = require('../../utils/logger');

global.logger = logger;

const updateRequestsType = async () => {
  logger.info('Starting update requests type...', { ctx: 'RunScript' });

  await Request.updateMany(
    { type: { $exists: false } },
    { type: REQUEST_TYPE.STUDIO },
  );

  logger.info('Update requests type successfully', { ctx: 'RunScript' });
};

(async () => {
  await updateRequestsType();
  process.exit(1);
})();
