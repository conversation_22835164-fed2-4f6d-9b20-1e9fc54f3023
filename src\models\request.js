const mongoose = require('mongoose');
const { Constants } = require('@vbee-holding/vbee-tts-models');
const { SERVER_ENV } = require('../configs');
const {
  REQUEST_STATUS,
  RESPONSE_TYPE,
  REQUEST_TYPE,
  TTS_CORE_VERSION,
  TTS_CORE_COMPUTE_PLATFORM,
  OUTPUT_TYPE,
  V3_API_TYPE,
  AUDIO_DOMAIN_TYPE,
} = require('../constants');

const requestSchema = new mongoose.Schema(
  {
    _id: String,
    title: String,
    text: String,
    subtitleLink: String,
    voiceCode: String,
    characters: Number,
    credits: Number,
    seconds: Number,
    sentences: [
      {
        _id: false,
        text: String,
        voiceCode: String,
        speed: Number,
        breakTime: Number,
        characters: Number,
        credits: Number,
        isMonetizable: { type: Boolean, default: false },
        elements: [
          {
            _id: false,
            key: String,
            text: String,
            startOffset: Number,
            endOffset: Number,
            name: String,
            value: String,
          },
        ],
      },
    ],
    audioType: { type: String, enum: Object.values(Constants.AUDIO_TYPE) },
    backgroundMusic: {
      name: String,
      link: String,
      volume: Number,
    },
    volume: Number,
    speed: Number,
    audioLink: String,
    sourceAudioLink: String,
    bitrate: Number,
    sampleRate: String,
    clientPause: {
      paragraphBreak: Number,
      sentenceBreak: Number,
      majorBreak: Number,
      mediumBreak: Number,
    },
    pronunciationDict: [{ word: String, pronunciation: String, _id: false }],
    userId: String,
    status: {
      type: String,
      enum: Object.values(REQUEST_STATUS),
      default: REQUEST_STATUS.IN_PROGRESS,
    },
    createdAt: Date,
    processingAt: Date, // time start process request
    endedAt: Date, // time ended process request
    usedCharacters: {
      paid: Number,
      bonus: Number,
    },
    wallet: {
      cycle: Number,
      topUp: Number,
      oneTime: Number,
      bonus: Number,
      custom: Number,
    },
    paid: { type: Boolean, default: false },
    refund: { type: Boolean, default: false },
    progress: { type: Number, default: 0 },
    demo: { type: Boolean, default: false },
    isPronunciationPreview: { type: Boolean, default: false },
    error: String,
    errorCode: String,
    paragraphs: [
      {
        _id: false,
        key: String,
        text: String,
        elements: [
          {
            _id: false,
            key: String,
            text: String,
            startOffset: Number,
            endOffset: Number,
            name: String,
            value: String,
          },
        ],
      },
    ],
    retentionPeriod: Number,
    sentenceTokenizerDuration: Number,
    t2aDuration: { min: Number, max: Number, average: Number },
    synthesisDuration: {
      min: Number,
      max: Number,
      average: Number,
      start: Number,
      end: Number,
    },
    joinAudioDuration: Number,
    firstStreamAudioDuration: Number,
    type: { type: String, enum: Object.values(REQUEST_TYPE) },
    audioDomainType: {
      type: String,
      enum: Object.values(AUDIO_DOMAIN_TYPE),
      default: AUDIO_DOMAIN_TYPE.S3,
    },
    responseType: { type: String, enum: Object.values(RESPONSE_TYPE) },
    app: { type: String, ref: 'App' },
    ttsRequestId: String,
    callbackUrl: String,
    ip: String,
    version: { type: String, enum: Object.values(TTS_CORE_VERSION) },
    synthesisComputePlatform: {
      type: String,
      enum: Object.values(TTS_CORE_COMPUTE_PLATFORM),
      default: TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
    },
    packageCode: String,
    price: Number,
    outputType: {
      type: String,
      enum: Object.values(OUTPUT_TYPE),
      default: OUTPUT_TYPE.LINK,
    },
    v3ApiType: {
      type: String,
      enum: Object.values(V3_API_TYPE),
    },
    awsZoneSynthesis: String,
    awsZoneFunctions: {
      normalizerFunction: { type: String },
      sentenceTokenizerFunction: { type: String },
      newSentenceTokenizerFunction: { type: String },
      textToAllophoneFunction: { type: String },
      synthesisFunction: { type: String },
      joinSentencesFunction: { type: String },
      srtFunction: { type: String },
      s3Bucket: { type: String },
    },
    googleCloudStorage: {
      bucket: { type: String },
    },
    fromVn: Boolean,
    template: String,
    tags: String,
    aesKey: String, // Use for encrypting value in tags, that were hashed. this key get from request
    cachingRate: Number, // Conversion rate of characters using cache
    reusedRate: Number, // reused audio rate
    cachingTime: Number, // Time process request from caching tts system
    cachingMessage: String, // Message from TTS Caching system
    cachingStatusCode: Number,
    sessionId: String, // Used to trace calls, corresponding to callId
    server: { type: String, default: SERVER_ENV },
    hidden: { type: Boolean, default: false },
    serviceType: String,
    clientUserId: String,
    timeProcess: {
      clientSendRequestAt: Date,
      receivedAt: Date,
      endAuthProcessAt: Date,
      endPreCheckAt: Date,
      endUploadBackgroundMusicAt: Date,
      endCheckVoiceAt: Date,
      sendRequestSynthesisAt: Date,
      getSynthesisRequestAt: Date,
      startInvokeLambdaFunctionAt: Date,
      endInvokeLambdaFunctionAt: Date,
      getSynthesisResponseAt: Date,
      preSendResponseAt: Date,
      startSendResponseAt: Date,
      sendResponseEndAt: Date,
      processDuration: Number,
    },
    downloadCount: Number,
    firstDownloadDuration: Number,
    deviceInfo: {},
    projectId: String,
    sentencesVoiceCode: {},
    returnTimestampWords: { type: Boolean, default: false },
    datasenses: {},
    gateRequestId: String,
    stt: {
      sampleRateHertz: Number,
      sampleSizeByte: Number,
      channel: Number,
      fileContent: String,
      fileUrl: String,
      configAudio: {},
      text: String,
      textRaw: String,
    },
    isPriority: { type: Boolean, default: false },
    isRealTime: { type: Boolean, default: false },
    isMonetizable: { type: Boolean, default: false },
  },
  {
    _id: false,
    timestamps: { updatedAt: true },
    versionKey: false,
  },
);

module.exports = mongoose.model('Request', requestSchema);
