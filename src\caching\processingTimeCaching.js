const { REDIS_KEY_PREFIX, REDIS_KEY_TTL } = require('../constants');

const Caching = require('.');

const _createKey = (requestId) =>
  `${REDIS_KEY_PREFIX.TTS_PROCESSING_TIME}_${requestId}`;

/** read from Caching */
const getProcessingTime = async (requestId) => {
  const processingTimeKey = _createKey(requestId);
  const processingTime = await Caching.RedisRepo.get(processingTimeKey);
  // @ts-ignore
  return processingTime ? JSON.parse(processingTime || '{}') : {};
};

/** write Caching */
const setProcessingTime = async (requestId, processingTime) => {
  const processingTimeKey = _createKey(requestId);
  await Caching.RedisRepo.set(
    processingTimeKey,
    JSON.stringify(processingTime),
    REDIS_KEY_TTL.TTS_PROCESSING_TIME,
  );
};

/** clear to Caching */
const deleteProcessingTime = async (requestId) => {
  const processingTimeKey = _createKey(requestId);
  await Caching.Manager.del(processingTimeKey);
};

/** save to Caching */
const saveStepProcessingTime = async ({
  requestId,
  step,
  startTime = Date.now(),
  additionalFields,
}) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  const processingTime = await getProcessingTime(requestId);
  if (!processingTime && !additionalFields) return;

  const updatedProcessingTime = {
    ...processingTime,
    [`${step}Time`]: duration,
    ...additionalFields,
  };
  await setProcessingTime(requestId, updatedProcessingTime);
};

/** check from Caching */
const checkSaveProcessingTime = async (requestId) => {
  const key = `${REDIS_KEY_PREFIX.SAVE_PROCESSING_TIME}_${requestId}`;

  // ADVISE: IMPORTANT: this is never-expire key, might lead to MEMORY LEAK in Redis
  const isFirstSave = await Caching.RedisRepo.setIfNotExists(key, 'true');
  const isSaved = !isFirstSave;
  return isSaved;
};

// ADVISE: about the naming, since this is a refactoring to move func here, I keep the old naming. In future, we can rename to find(), getTts() because we're already inside "RequestCaching" domain/scope (Request and Redis words are redundant)
module.exports = {
  getProcessingTime,
  deleteProcessingTime,
  saveStepProcessingTime,
  checkSaveProcessingTime,
};
