const logger = require('../../utils/logger');
const { getAllBlackWords } = require('../../daos/blackWord');

const initBlackWords = async () => {
  // ADVISE: calling directly to DAO from initialization process is not good. Should move getAllBlackWords() into BlackWordService
  const blackWords = await getAllBlackWords();

  // ADVISE: get rid of using global.BLACK_WORDS to increase readability, debug/test, IDE integration because dependencies are hidden.
  global.BLACK_WORDS =
    blackWords?.map((blackWord) => blackWord.word.toLowerCase()) || [];

  logger.info('BlackWords: Init successfully,', { ctx: 'Service.BlackWords' });
};

module.exports = { initBlackWords };
