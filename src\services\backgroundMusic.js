const backgroundMusicDao = require('../daos/backgroundMusic');
const { isValidUrl } = require('../utils/string');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const createBackgroundMusic = async (userId, name, link) => {
  if (!isValidUrl(link))
    throw new CustomError(errorCodes.BAD_REQUEST, 'Link invalid');

  let bgMusicName = name;
  if (!bgMusicName) {
    const { pathname } = new URL(link);
    const replaceName = pathname.split('/')[1];
    bgMusicName = replaceName;
  }

  const backgroundMusic = await backgroundMusicDao.createBackgroundMusic({
    userId,
    name: bgMusicName,
    link,
  });
  return backgroundMusic;
};

// ADVISE: extract to internal microservice BackgroundMusicService, reduce code for this API
module.exports = { createBackgroundMusic };
