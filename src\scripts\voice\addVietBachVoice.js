require('dotenv').config();
const yargs = require('yargs/yargs');
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const getVoice = (env) => {
  return {
    code: 'hn_male_vietbach_child_22k-vc',
    name: 'HN - Việt Bách',
    gender: 'male',
    languageCode: 'vi-VN',
    type: 'Neural TTS',
    provider: 'vbee',
    squareImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/viet-bach.png', // bucket of core account
    roundImage:
      'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/viet-bach.png',
    demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_vietbach_child_22k-vc.mp3', // bucket of product account
    rank: 2,
    synthesisFunction: `function:a2s-stable-${env}-vietbach-22k-stable`,
    active: true,
    defaultSampleRate: 22050,
    sampleRates: [8000, 16000, 22050],
    level: 'PREMIUM',
    styles: ['child'],
    beta: true,
    features: ['premium-vietnam-voice'],
  };
};

(async () => {
  const { argv = {} } = yargs(process.argv.slice(2));
  const { env } = argv;
  logger.info(`Starting create Việt Bách voice...`, { ctx: 'RunScript' });
  const voice = getVoice(env);
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Việt Bách successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();

// Run script: node src/scripts/voice/addVietBachVoice.js --env="dev"
