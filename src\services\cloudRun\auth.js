const { GoogleAuth } = require('google-auth-library');
const jwt = require('jsonwebtoken');

const {
  CLOUD_RUN_CLIENT_EMAIL,
  CLOUD_RUN_PRIVATE_KEY,
} = require('../../configs');

const auth = new GoogleAuth({
  credentials: {
    client_email: CLOUD_RUN_CLIENT_EMAIL,
    private_key: CLOUD_RUN_PRIVATE_KEY,
  },
});

const CACHE_TOKENS = {};
const DEFAULT_EXPIRATION = 3600 * 1000; // 1 hour

const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) return decoded.exp * 1000;
  } catch (error) {
    logger.error(error, { ctx: 'GetTokenExpiration' });
  }

  return Date.now() + DEFAULT_EXPIRATION;
};

const getIdToken = async (cloudRunUrl) => {
  const now = Date.now();

  if (CACHE_TOKENS[cloudRunUrl] && CACHE_TOKENS[cloudRunUrl].expiresAt > now)
    return CACHE_TOKENS[cloudRunUrl].token;

  const client = await auth.getIdTokenClient(cloudRunUrl);
  const idToken = await client.idTokenProvider.fetchIdToken(cloudRunUrl);

  const expiresAt = getTokenExpiration(idToken);

  CACHE_TOKENS[cloudRunUrl] = {
    token: idToken,
    expiresAt,
  };

  return idToken;
};

module.exports = { getIdToken };
