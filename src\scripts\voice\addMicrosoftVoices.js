require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');
const { findLanguages } = require('../../daos/language');
const { VOICE_PROVIDER } = require('../../constants');
const { generateMSVoiceSampleRates } = require('../../utils/sampleRate');
const {
  getSameElementsIn2Array,
  hasSameElementIn2Array,
} = require('../../utils/array');
const {
  getMicrosoftVoices,
  getVoiceFeatures,
  getRoundImage,
  getSquareImage,
} = require('../shared');

global.logger = logger;

const getAllReadyLanguageCode = async () => {
  const { languages } = await findLanguages();

  const languageCodes = languages.map((language) => language.code);
  return languageCodes;
};

const getLanguageCode = (voiceCode, locale) => {
  const MULTILINGUAL_CODE = 'multilingual';
  if (voiceCode?.includes('Multilingual')) return MULTILINGUAL_CODE;

  return locale;
};

const upsertVoice = async (voice) => {
  await Voice.updateOne({ code: voice.code }, voice, { upsert: true });
  logger.info(`Update voice ${voice.name} successfully`);
};

const checkValidVoice = (voice, readyLanguageCodes) => {
  const validLocale =
    readyLanguageCodes.includes(voice.Locale) && voice.Locale !== 'vi-VN'; // Not use Vietnamese voice
  const validSecondaryLocale = hasSameElementIn2Array(
    readyLanguageCodes,
    voice?.SecondaryLocaleList || [],
  );

  return validLocale || validSecondaryLocale;
};

const createMicrosoftVoices = async () => {
  const voices = await getMicrosoftVoices();
  const readyLanguageCodes = await getAllReadyLanguageCode();

  const usedVoices = [];

  for (const voice of voices) {
    const validLanguageCodes = checkValidVoice(voice, readyLanguageCodes);
    if (validLanguageCodes) usedVoices.push(voice);
  }

  const microsoftVoices = usedVoices.map((voice) => ({
    code: voice.ShortName,
    name: voice.DisplayName,
    gender: voice.Gender?.toLowerCase(),
    languageCode: getLanguageCode(voice.DisplayName, voice.Locale),
    secondaryLanguageCodes: getSameElementsIn2Array(
      readyLanguageCodes,
      voice.SecondaryLocaleList,
    ),
    type: voice.VoiceType,
    provider: VOICE_PROVIDER.MICROSOFT,
    rank: 999,
    active: true,
    defaultSampleRate: voice.SampleRateHertz,
    sampleRates: generateMSVoiceSampleRates(voice.SampleRateHertz),
    level: voice.VoiceType === 'Standard' ? 'STANDARD' : 'PREMIUM',
    squareImage: getSquareImage(voice.Gender),
    roundImage: getRoundImage(voice.Gender),
    features: getVoiceFeatures(voice.VoiceType),
    demo: `https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/microsoft/${voice.ShortName}.mp3`,
  }));

  await Promise.all(
    microsoftVoices.map(async (voice) => {
      await upsertVoice(voice);
    }),
  );

  logger.info(`Created ${microsoftVoices.length} Microsoft voices`);
};

(async () => {
  await createMicrosoftVoices();
  process.exit(1);
})();
