require('dotenv').config();
require('../../models');

const Language = require('../../models/language');
const logger = require('../../utils/logger');
const multilingualLanguage = require('./multilingualLanguage.json');

global.logger = logger;

const addMultilingualLanguage = async () => {
  try {
    await Language.updateOne(
      { code: multilingualLanguage.code },
      multilingualLanguage,
      { upsert: true },
    );
  } catch (error) {
    logger.error('Add Multilingual language', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting add Multilingual language...`, {
    ctx: 'RunScript',
  });
  await addMultilingualLanguage();
  logger.info(`Add Multilingual language successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
