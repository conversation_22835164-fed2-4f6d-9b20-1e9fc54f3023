const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const awsZoneController = require('../controllers/awsZone');
const { auth, hasRole } = require('../middlewares/auth');
const { createAwsZoneValidate } = require('../validations/awsZone');

/* eslint-disable prettier/prettier */
router.post('/aws-zone', createAwsZoneValidate, auth, hasRole('create-aws-zone'), asyncMiddleware(awsZoneController.createAwsZone));
/* eslint-disable prettier/prettier */

module.exports = router;
