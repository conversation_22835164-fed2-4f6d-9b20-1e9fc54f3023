const { MONGO_URI, SERVICE } = require('../../configs');
const { FEATURE_KEYS } = require('../../constants/featureKeys');
const { getFeatureValue } = require('../growthbook');
const { isDownstreamReady } = require('./downstream');

const isMongoReady = () => {
  const mongooseConnections = require('mongoose').connections || [];
  // no uri and no connection
  if (mongooseConnections.length <= 0 && !MONGO_URI) {
    return true;
  }
  // provided uri but no connection
  if (mongooseConnections.length <= 0 && MONGO_URI) {
    return false;
  }
  return require('mongoose').connection.readyState === 1;
};

const isKafkaReady = () => {
  if (KAFKA_CONSUMER_LISTS.length <= 0) return true;
  return KAFKA_CONSUMER_LISTS.every((consumer) => consumer.isReady);
};

const isKafkaHealthy = () => {
  if (KAFKA_CONSUMER_LISTS.length <= 0) return true;
  return KAFKA_CONSUMER_LISTS.every((consumer) => consumer.isHealthy);
};

const isReady = () => {
  // ADVISE: remote config, should be in centralized ConfigService (cachable, fallback, ...)
  const checkHealth = getFeatureValue(FEATURE_KEYS.HEALTHCHECK, {
    service: SERVICE,
  });
  if (checkHealth) {
    return isMongoReady() && isKafkaReady() && isDownstreamReady();
  }
  return true;
};
const isHealthy = () => {
  // ADVISE: remote config, should be in centralized ConfigService (cachable, fallback, ...)
  const checkHealth = getFeatureValue(FEATURE_KEYS.HEALTHCHECK, {
    service: SERVICE,
  });
  if (checkHealth) {
    return isMongoReady() && isKafkaHealthy();
  }
  return true;
};

module.exports = {
  isReady,
  isHealthy,
};
