const { setPolyfills, GrowthBook } = require('@growthbook/growthbook');
const crossFetch = require('cross-fetch');
const EventSource = require('eventsource');
const logger = require('../utils/logger');

const {
  GROWTH_BOOK_API_HOST,
  GROWTH_BOOK_CLIENT_KEY,
  LOADING_FEATURES_REALTIME_INTERVAL,
} = require('../configs');

setPolyfills({
  // ADVISE: we should unify the runtime to Node 18 or later and remove this polyfill (also remove the cross-fetch deps)
  fetch: crossFetch, // Required for Node 17 or earlier
  EventSource, // Optional, can make feature rollouts faster
});

let GrowthBookSingleton;

const initGrowthBook = async () => {
  GrowthBookSingleton = new GrowthBook({
    apiHost: GROWTH_BOOK_API_HOST,
    clientKey: GROWTH_BOOK_CLIENT_KEY,
  });

  await GrowthBookSingleton.loadFeatures({ timeout: 10000 });
  logger.info('GrowthBook: load features for the first time,', {
    ctx: 'GrowthBook',
  });

  logger.info('GrowthBook: Init successfully,', {
    ctx: 'GrowthBook',
    apiHost: GROWTH_BOOK_API_HOST,
  });
};

const scheduleLoadFeaturesRealtime = async () => {
  try {
    await GrowthBookSingleton.loadFeatures({ timeout: 10000 });
  } catch (error) {
    logger.error(error, { ctx: 'LoadFeaturesRealtime' });
  } finally {
    setTimeout(
      scheduleLoadFeaturesRealtime,
      LOADING_FEATURES_REALTIME_INTERVAL,
    );
  }
};

// ADVISE: sometime this is a kind of get remote config value, should be in centralized ConfigService (cachable, fallback, ...)
const getFeatureValue = (featureKey, attributes) => {
  // ADVISE: we create a new GrowthBook local instance for caching the evaluation result, Avoid calling remote API for each request of getFeatureValue
  const growthBookLocal = new GrowthBook();
  const features = GrowthBookSingleton.getFeatures();
  growthBookLocal.setFeatures(features);
  growthBookLocal.setAttributes(attributes);

  const featureValue = growthBookLocal.getFeatureValue(featureKey);
  growthBookLocal.destroy();

  return featureValue;
};

module.exports = {
  initGrowthBook,
  scheduleLoadFeaturesRealtime,
  getFeatureValue,
};
