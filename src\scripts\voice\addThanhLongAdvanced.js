require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const voice = {
  code: 'hn_male_thanhlong_talk_48k-f5',
  name: '<PERSON><PERSON> <PERSON> <PERSON><PERSON>',
  gender: 'male',
  languageCode: 'vi-VN',
  provider: 'vbee',
  squareImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/thanh-long.png',
  roundImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/thanh-long.png',
  demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_thanhlong_talk_48k-f5.mp3',
  rank: 3,
  active: true,
  defaultSampleRate: 22050,
  sampleRates: [8000, 16000, 22050],
  level: 'ADVANCED',
  beta: true,
  features: ['advanced-vietnam-voice'],
};

(async () => {
  logger.info(`Starting create <PERSON>h Long Advanced voice...`, {
    ctx: 'RunScript',
  });
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Thanh Long Advanced voice successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
