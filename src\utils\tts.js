const moment = require('moment');
const {
  GLOBAL_VOICE_FEATURES,
  VIETNAM_VOICE_FEATURES,
} = require('../constants/voice');
const { PACKAGE_HAS_EOL } = require('../constants/package');

const isActivePremiumOneTimeCredits = (user) => {
  const oneTimeCredits = user?.remainingCharacters;
  const oneTimePackageCode = user?.studio?.oneTime?.packageCode;
  const oneTimePackageExpiryDate =
    user?.studio?.oneTime?.packageExpiryDate || user?.packageExpiryDate;

  if (!oneTimePackageCode || !oneTimePackageExpiryDate || !oneTimeCredits)
    return false;

  const isActive = moment().isBefore(oneTimePackageExpiryDate);
  const isPackageHasEol = PACKAGE_HAS_EOL.includes(oneTimePackageCode);

  return isActive && isPackageHasEol;
};

const canUseEOLVoice = ({ voice, user }) => {
  const voiceEOLDate = voice?.eolDate;
  const userEOLDate = user?.studio?.oneTime?.eolDate;
  const oneTimePackageCode = user?.studio?.oneTime?.packageCode;

  if (!voiceEOLDate || !userEOLDate || !oneTimePackageCode) return false;

  const latestEOLDate = new Date(
    Math.max(new Date(voiceEOLDate).getTime(), new Date(userEOLDate).getTime()),
  );

  const isAfterEOL = moment().isAfter(latestEOLDate);
  if (isAfterEOL) return false;

  return PACKAGE_HAS_EOL.includes(oneTimePackageCode);
};

const checkVoicePermission = (
  userFeatures = [],
  voiceFeatures = [],
  user = {},
) => {
  if (isActivePremiumOneTimeCredits(user))
    userFeatures = [
      ...userFeatures,
      VIETNAM_VOICE_FEATURES.PREMIUM,
      GLOBAL_VOICE_FEATURES.PREMIUM,
    ];

  // TODO: Remove after migration
  const enabledMigrateFeatures = false;
  if (enabledMigrateFeatures) {
    if (userFeatures.includes('global-voice'))
      userFeatures = [...userFeatures, ...Object.values(GLOBAL_VOICE_FEATURES)];

    if (userFeatures.includes(VIETNAM_VOICE_FEATURES.BASIC))
      userFeatures = [...userFeatures, VIETNAM_VOICE_FEATURES.STANDARD];

    if (userFeatures.includes(VIETNAM_VOICE_FEATURES.PRO))
      userFeatures = [...userFeatures, VIETNAM_VOICE_FEATURES.PREMIUM];

    if (userFeatures.includes(VIETNAM_VOICE_FEATURES.STANDARD))
      userFeatures = [...userFeatures, VIETNAM_VOICE_FEATURES.BASIC];

    if (userFeatures.includes(VIETNAM_VOICE_FEATURES.PREMIUM))
      userFeatures = [...userFeatures, VIETNAM_VOICE_FEATURES.ADVANCED];

    if (userFeatures.includes(GLOBAL_VOICE_FEATURES.STANDARD))
      userFeatures = [...userFeatures, GLOBAL_VOICE_FEATURES.BASIC];

    if (userFeatures.includes(GLOBAL_VOICE_FEATURES.PREMIUM))
      userFeatures = [...userFeatures, GLOBAL_VOICE_FEATURES.ADVANCED];
  }

  return (
    !voiceFeatures?.length || // If voiceFeatures is empty, return true
    voiceFeatures.every((feature) => userFeatures.includes(feature)) // If all voiceFeatures are included in userFeatures, return true
  );
};

module.exports = { checkVoicePermission, canUseEOLVoice };
