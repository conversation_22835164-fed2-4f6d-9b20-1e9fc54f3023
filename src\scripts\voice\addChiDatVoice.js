require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const voice = {
  code: 'sg_male_chidat_ebook_48k-phg',
  name: '<PERSON><PERSON> <PERSON> <PERSON><PERSON>',
  gender: 'male',
  languageCode: 'vi-VN',
  type: 'Neural TTS',
  provider: 'vbee',
  squareImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/chi-dat.png',
  roundImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/chi-dat.png',
  demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_male_chidat_ebook_48k-phg.wav',
  rank: 3,
  synthesisFunction: '',
  active: true,
  defaultSampleRate: 22050,
  sampleRates: [8000, 16000, 22050],
  level: 'PREMIUM',
  styles: ['story'],
  beta: true,
  features: ['premium-vietnam-voice'],
};

(async () => {
  logger.info(`Starting create Chi Dat voice...`, { ctx: 'RunScript' });
  await Voice.updateOne({ code: voice.code }, voice, {
    upsert: true,
    new: true,
  });

  logger.info(`Create Chi Dat voice successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
