const { RESPONSE_TYPE } = require('../constants');
const cachingSynthesisService = require('../services/cachingSynthesis');

const cachingSynthesis = async (req, res) => {
  const { apiApp: app, receivedAt } = req;
  const { publicIP: ip } = req.__clientInfo;

  const endAuthProcessAt = new Date();
  logger.info(`Caching request: ${JSON.stringify(req.body)}`, {
    ctx: 'CachingRequest',
    ip,
    appId: app?._id,
    sessionId: req.body?.sessionId,
  });

  const {
    template,
    tags,
    aesKey,
    voiceCode,
    outputType,
    responseType,
    backgroundMusic,
    speedRate,
    bitrate,
    sampleRate,
    callbackUrl,
    serviceType,
    clientUserId,
    sessionId,
    clientSendRequestAt,
  } = req.body;

  const request = await cachingSynthesisService.handleCachingSynthesisRequest({
    app,
    template,
    tags,
    aesKey,
    voiceCode,
    outputType,
    responseType,
    backgroundMusic,
    speedRate,
    bitrate,
    sampleRate,
    callbackUrl,
    ip,
    sessionId,
    serviceType,
    clientUserId,
    receivedAt,
    endAuthProcessAt,
    clientSendRequestAt,
  });

  if (responseType === RESPONSE_TYPE.DIRECT) {
    global.REQUEST_DIRECT[request.requestId] = res;
    return null;
  }

  return res.send(request);
};

const countTextLength = async (req, res) => {
  const { templates, aesKey, tags } = req.body;

  const { texts, totalLength } =
    cachingSynthesisService.getDetailDecryptedTemplates({
      templates,
      aesKey,
      tags,
    });

  return res.send({ texts, totalLength });
};

module.exports = { cachingSynthesis, countTextLength };
