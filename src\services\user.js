const moment = require('moment');
const retry = require('retry');

const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const userDao = require('../daos/user');
const resetLogDao = require('../daos/resetLog');
const requestDao = require('../daos/request');
const orderDao = require('../daos/order');
const appDao = require('../daos/app');
const migratedDubbingUserDao = require('../daos/migratedDubbingUser');

const logger = require('../utils/logger');
const { omitIsNil } = require('../utils/omit');
const {
  SYNC_CHARACTERS_EVENT,
  KAFKA_TOPIC,
  PACKAGE_TYPE,
  REQUEST_TYPE,
  REQUEST_STATUS,
  // APP_ROLE,
  PACKAGE_CODE,
  APP_ROLE,
  PACKAGE_FEATURE,
  RESET_CREDITS_STATUS,
  WALLET_TYPES,
  LOCK_ACTIONS,
  VOICE_PROVIDER,
  VOICE_LEVEL,
} = require('../constants');
const { sendMessage } = require('./kafka/producer');
// const { findApp } = require('../daos/app');
const {
  API_CHARACTERS_RUN_OUT_NUMBER,
  API_CHARACTERS_REACHED_ZERO_NUMBER,
} = require('../configs');
const { getFeatureValue } = require('./growthbook');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const packageService = require('./package');
const code = require('../errors/code');
const getErrorMessage = require('../errors/message');
const { RATE_LIMIT_TYPE } = require('../constants/rateLimiter');
const { findMigratedDubbingUser } = require('./migratedDubbingToStudioUser');
const { convertLongToNumber } = require('../utils/number');
const {
  getUserUpdateCredits,
  getUserUpdateCreditsProcessWallet,
} = require('./processCredits');
const { checkVoicePermission } = require('../utils/tts');
const { PACKAGE_HAS_EOL } = require('../constants/package');
const sleep = require('../utils/sleep');
const { AuthorizationService } = require('./authorization');
const BannedAccountService = require('./BannedAccountService');
const voiceCloningService = require('./voiceCloning');
const { getEmailDomain, checkDomainMatched } = require('../utils/email');

const updateByNewDubbingPackage = async ({
  userId,
  orderId,
  packageCode,
  packageExpiryDate,
  concurrentRequest,
  download,
  retentionPeriod,
  features,
  price,
  isGift,
  packagePrice,
  packageUsdPrice,
  renewalType,
}) => {
  const packageData = {
    'dubbing.packageCode': packageCode,
    'dubbing.packageExpiryDate': packageExpiryDate,
    'dubbing.concurrentRequest': concurrentRequest,
    'dubbing.download': download,
    'dubbing.retentionPeriod': retentionPeriod,
    'dubbing.features': features,
    'dubbing.price': price,
  };

  await userDao.updateUserById(userId, packageData);
  await orderDao.updateOrderById(orderId, {
    userId,
    activePackage: true,
    type: PACKAGE_TYPE.DUBBING,
  });

  sendMessage(KAFKA_TOPIC.ORDER_PAID, {
    value: {
      userId,
      type: PACKAGE_TYPE.DUBBING,
      orderId,
      packageCode,
      packageExpiryDate,
      concurrentRequest,
      download,
      retentionPeriod,
      features,
      price,
      isGift,
      packagePrice,
      packageUsdPrice,
      renewalType,
    },
  });
};

const updateByNewPackage = async ({
  userId,
  orderId,
  orderType,
  isTopUp,
  packageCode,
  expiresIn,
  maxLengthInputText,
  maxLengthDemoInput,
  speedConvert,
  concurrentRequest,
  download,
  retentionPeriod,
  features,
  packageStartDate,
  packageExpiryDate,
  price,
  maxPreview,
  maxInstantVoiceSynthesis,
  isGift,
  packagePrice,
  packageUsdPrice,
  renewalType,
  cyclePackageInfo,
  refPackageCode,
  isCustomPackage,
  // isFirstCycleOrder,
}) => {
  const packageData = {
    packageCode,
    packageStartDate,
    packageExpiryDate,
    maxLengthInputText,
    maxLengthDemoInput,
    speedConvert,
    concurrentRequest,
    download,
    retentionPeriod,
    features,
    price,
    maxPreview,
    maxInstantVoiceSynthesis,
    isGift,
    packagePrice,
    packageUsdPrice,
    renewalType,
    refPackageCode,
  };

  if (isTopUp) {
    sendMessage(KAFKA_TOPIC.ORDER_PAID, {
      value: {
        userId,
        type: orderType,
        ...packageData,
      },
    });
    return;
  }

  if (!packageCode || !expiresIn)
    throw new CustomError(errorCodes.BAD_REQUEST, 'Missing package data');

  if (
    cyclePackageInfo &&
    cyclePackageInfo?.nextResetAt &&
    cyclePackageInfo?.period
  ) {
    packageData['studio.cycle.nextResetAt'] = cyclePackageInfo.nextResetAt;
    packageData['studio.cycle.period'] = cyclePackageInfo.period;
  }

  if (isCustomPackage) {
    packageData['studio.cycle.nextResetAt'] = null;
    packageData['studio.cycle.period'] = 0;
  }

  const order = await orderDao.findOrder({ _id: orderId });
  if (order && order.activePackage) return;

  if (
    orderType === PACKAGE_TYPE.DUBBING ||
    orderType === PACKAGE_TYPE.MBF_DUBBING
  ) {
    await updateByNewDubbingPackage({
      userId,
      orderId,
      packageCode,
      packageExpiryDate,
      concurrentRequest,
      download,
      retentionPeriod,
      features,
      price,
      isGift,
      packagePrice,
      packageUsdPrice,
      renewalType,
    });
    return;
  }

  // if (isFirstCycleOrder)
  packageData['studio.oneTime.packageExpiryDate'] = packageExpiryDate;

  const updateFields =
    orderType === PACKAGE_TYPE.API ? { apiPackage: packageData } : packageData;

  const updatedUser = await userDao.updateUserById(userId, updateFields);

  await orderDao.updateOrderById(orderId, {
    userId,
    activePackage: true,
    type: orderType,
  });
  await appDao.updateAppByUserIdInRedis(userId);
  if (!refPackageCode && updatedUser?.refPackageCode)
    await userDao.removeRefPackageCodeByUserId(userId);

  const studioCreditSendToKafka = {
    cycle: {
      nextResetAt: updatedUser?.studio?.cycle?.nextResetAt,
      period: updatedUser?.studio?.cycle?.period,
    },
    topUp: updatedUser?.studio?.topUp,
  };

  // Unlock all credits for studio package
  if (orderType === PACKAGE_TYPE.STUDIO) await userDao.unlockAllCredits(userId);

  sendMessage(KAFKA_TOPIC.ORDER_PAID, {
    value: {
      userId,
      type: orderType,
      studio: convertLongToNumber(studioCreditSendToKafka),
      ...packageData,
    },
  });
};

// TODO: Handle other reward types by split this function
const handleRedeemReferral = async ({ user, characters }) => {
  const { packageCode, packageExpiryDate } = user || {};
  const updateFields = {};

  const isFreeUser = packageService.checkFreePackage(packageCode);
  if (isFreeUser) {
    // Add delay to prevent race condition when add bonus characters to free user
    // This possible happen when pay_order event and redeem_referral event run at the same time
    // This is a temporary solution to prevent the issue
    // TODO: Remove this after we have a better solution
    await sleep(2000);
    user = await getUserByIdWithRetry(user?._id);
  }

  const {
    studio,
    remainingCharacters: userRemainingCharacters = 0,
    bonusCharacters: userBonusCharacters = 0,
  } = user;
  const { custom, cycle, oneTime } = studio || {};
  const {
    remainingCredits: userRemainingCustomCredits = 0,
    capturedReferralCredits: userCapturedReferralCustomCredits = 0,
  } = custom || {};
  const {
    remainingCredits: userRemainingCycleCredits = 0,
    capturedReferralCredits: userCapturedReferralCycleCredits = 0,
  } = cycle || {};
  const { capturedReferralCredits: userCapturedReferralOneTimeCredits = 0 } =
    oneTime || {};

  const isExpiredUser =
    packageExpiryDate && moment(packageExpiryDate).isBefore(moment());

  if (isFreeUser) {
    updateFields.bonusCharacters = userBonusCharacters + characters;
  }

  const isUsingCustomPackage =
    !isExpiredUser && packageService.checkCustomPackage(packageCode);
  const isUsingOneTimePackage =
    !isExpiredUser && packageService.checkOldPackage(packageCode);
  const isUsingCyclePackage =
    !isExpiredUser &&
    !isFreeUser &&
    !isUsingCustomPackage &&
    !isUsingOneTimePackage;

  if (isUsingCustomPackage) {
    updateFields['studio.custom.remainingCredits'] =
      userRemainingCustomCredits + characters;
    updateFields['studio.custom.capturedReferralCredits'] =
      userCapturedReferralCustomCredits + characters;
  }

  if (isUsingOneTimePackage) {
    updateFields.remainingCharacters = userRemainingCharacters + characters;
    updateFields['studio.oneTime.capturedReferralCredits'] =
      userCapturedReferralOneTimeCredits + characters;
  }

  if (isUsingCyclePackage) {
    updateFields['studio.cycle.remainingCredits'] =
      userRemainingCycleCredits + characters;
    updateFields['studio.cycle.capturedReferralCredits'] =
      userCapturedReferralCycleCredits + characters;
  }

  const updatedUser = await userDao.updateUserById(user?._id, updateFields);

  sendMessage(KAFKA_TOPIC.SYNC_REDEEM_REFERRAL, {
    value: {
      userId: user?._id,
      remainingCharacters: Number(updatedUser?.remainingCharacters),
      bonusCharacters: Number(updatedUser?.bonusCharacters),
      studio: convertLongToNumber(updatedUser?.studio),
      rewardsCharacters: characters,
    },
  });
};

const handlePayOrder = async ({
  userId,
  orderId,
  orderType,
  isTopUp = false,
  userRemainingCharacters = 0,
  userLockCharacters = 0,
  credits = 0,
  characters = 0,
  bonusCharacters = 0,
  isFreePackage,
  studio = {},
  cyclePackageInfo,
  topUpPackageInfo,
  currentPackageCode,
  isRolloverCredits = false,
  isCustomPackage,
}) => {
  const userUpdateFields = {};
  const { topUp: userTopUp = {}, cycle: userCycle = {} } = studio;
  const { remainingCredits: userTopUpRemainingCredits = 0 } = userTopUp;
  const {
    remainingCredits: userCycleRemainingCredits = 0,
    creditsPerCycle,
    purchasedTopUpCredits = 0,
  } = userCycle;

  switch (orderType) {
    case PACKAGE_TYPE.API: {
      userUpdateFields['apiCharacters.bonusCharacters'] = bonusCharacters;
      if (!isFreePackage) {
        userUpdateFields['apiCharacters.remainingCharacters'] =
          userRemainingCharacters + userLockCharacters + characters;
        userUpdateFields['apiCharacters.lockCharacters'] = 0;
      }
      break;
    }

    default: {
      userUpdateFields.bonusCharacters = bonusCharacters;

      if (!isFreePackage) {
        userUpdateFields.remainingCharacters =
          userRemainingCharacters + userLockCharacters + characters;
        userUpdateFields.lockCharacters = 0;
      }

      // handle activate free package when cron job has not run yet
      if (isFreePackage) {
        const isOldPackage = packageService.checkOldPackage(currentPackageCode);

        const newLockCharacters = isOldPackage
          ? userLockCharacters + userRemainingCharacters
          : 0;

        userUpdateFields.remainingCharacters = 0;
        userUpdateFields.lockCharacters = newLockCharacters;
      }

      if (isTopUp) {
        userUpdateFields['studio.topUp.remainingCredits'] =
          userTopUpRemainingCredits + credits;
        userUpdateFields['studio.cycle.purchasedTopUpCredits'] =
          purchasedTopUpCredits + credits;
        break;
      }

      if (isCustomPackage)
        userUpdateFields['studio.custom.remainingCredits'] = credits;

      // If user buy cycle package then reset custom credits to zero
      if (!isCustomPackage)
        userUpdateFields['studio.custom.remainingCredits'] = 0;

      // If user buy cycle package or custom package then reset purchasedTopUpCredits and topUp credits to 0
      userUpdateFields['studio.cycle.purchasedTopUpCredits'] = 0;
      userUpdateFields['studio.topUp.remainingCredits'] =
        topUpPackageInfo?.credits || 0;

      userUpdateFields['studio.cycle.remainingCredits'] =
        cyclePackageInfo?.creditsPerCycle + cyclePackageInfo?.credits || 0;

      userUpdateFields['studio.cycle.creditsPerCycle'] =
        cyclePackageInfo?.creditsPerCycle || 0;

      userUpdateFields['studio.cycle.capturedRolloverCredits'] =
        cyclePackageInfo?.credits || 0;
      userUpdateFields['studio.topUp.capturedRolloverCredits'] =
        topUpPackageInfo?.credits || 0;

      userUpdateFields['studio.custom.capturedReferralCredits'] = 0;
      userUpdateFields['studio.oneTime.capturedReferralCredits'] = 0;
      userUpdateFields['studio.cycle.capturedReferralCredits'] = 0;

      // Handle rollover credits for user has auto renew package (isRolloverCredits = true)
      if (isRolloverCredits) {
        const { rolloverCycleCredits, rolloverTopUpCredits } =
          calculateRolloverCredits({
            creditsPerCycle,
            remainingCycleCredits: userCycleRemainingCredits,
            remainingTopUpCredits: userTopUpRemainingCredits,
          });

        userUpdateFields['studio.cycle.remainingCredits'] =
          rolloverCycleCredits + cyclePackageInfo?.creditsPerCycle || 0;
        userUpdateFields['studio.cycle.capturedRolloverCredits'] =
          rolloverCycleCredits || 0;
        userUpdateFields['studio.topUp.remainingCredits'] =
          rolloverTopUpCredits;
        userUpdateFields['studio.topUp.capturedRolloverCredits'] =
          rolloverTopUpCredits;
        userUpdateFields['studio.cycle.capturedReferralCredits'] = 0;
      }

      break;
    }
  }
  await orderDao.updateOrderById(orderId, {
    userId,
    payOrder: true,
    type: orderType,
  });

  const user = await userDao.updateUserById(userId, userUpdateFields);

  const isApiOrder = orderType === PACKAGE_TYPE.API;
  const remainingCharacters = isApiOrder
    ? user?.apiCharacters?.remainingCharacters
    : user?.remainingCharacters;
  const newBonusCharacters = isApiOrder
    ? user?.apiCharacters?.bonusCharacters
    : user?.bonusCharacters;
  const lockCharacters = isApiOrder
    ? user?.apiCharacters?.lockCharacters
    : user?.lockCharacters;

  sendMessage(KAFKA_TOPIC.ORDER_PAID, {
    value: {
      userId,
      type: orderType,
      remainingCharacters: Number(remainingCharacters),
      bonusCharacters: Number(newBonusCharacters),
      lockCharacters: Number(lockCharacters),
      studio: convertLongToNumber(user?.studio),
    },
  });
};

const handleCancelOrder = async ({
  userId,
  orderId,
  orderType,
  userRemainingCharacters = 0,
  userLockCharacters = 0,
  userBonusCharacters = 0,
  characters = 0,
  bonusCharacters = 0,
  beforePackage,
}) => {
  const hasBeforePackageExpired =
    beforePackage?.packageExpiryDate &&
    moment(beforePackage.packageExpiryDate).isBefore(moment());

  const hasBeforePackageFree = [
    PACKAGE_CODE.STUDIO_BASIC,
    PACKAGE_CODE.STUDIO_TRIAL,
    PACKAGE_CODE.STUDIO_FREE,
    PACKAGE_CODE.STUDIO_FREE_V2,
    PACKAGE_CODE.STUDIO_FREE_V3,
  ].includes(beforePackage?.packageCode);

  let updatePaidCharacters = hasBeforePackageExpired
    ? userRemainingCharacters + userLockCharacters - characters
    : userRemainingCharacters - characters;
  let updateBonusCharacters = hasBeforePackageExpired
    ? 0
    : userBonusCharacters - bonusCharacters;

  updatePaidCharacters = updatePaidCharacters < 0 ? 0 : updatePaidCharacters;
  updateBonusCharacters = updateBonusCharacters < 0 ? 0 : updateBonusCharacters;

  let userUpdateFields = {};

  switch (orderType) {
    case PACKAGE_TYPE.API: {
      if (beforePackage) {
        userUpdateFields.apiPackage = beforePackage;
        // If before package is expired then set lock characters
        if (hasBeforePackageExpired) {
          userUpdateFields['apiCharacters.lockCharacters'] =
            updatePaidCharacters;
          userUpdateFields['apiCharacters.remainingCharacters'] = 0;
          userUpdateFields['apiCharacters.bonusCharacters'] = 0;
        } else {
          userUpdateFields['apiCharacters.remainingCharacters'] =
            updatePaidCharacters;
          userUpdateFields['apiCharacters.bonusCharacters'] =
            updateBonusCharacters;
        }
      } else {
        userUpdateFields.$unset = { apiPackage: '', apiCharacters: '' };
      }

      break;
    }

    default: {
      if (beforePackage)
        userUpdateFields = { ...userUpdateFields, ...beforePackage };

      // If before package is expired then set lock characters
      if (hasBeforePackageExpired) {
        const STUDIO_FREE_PACKAGES = [
          PACKAGE_CODE.STUDIO_FREE,
          PACKAGE_CODE.STUDIO_FREE_V2,
          PACKAGE_CODE.STUDIO_FREE_V3,
        ];
        userUpdateFields.lockCharacters = updatePaidCharacters;
        userUpdateFields.remainingCharacters = 0;
        // Set bonus characters
        userUpdateFields.bonusCharacters = STUDIO_FREE_PACKAGES.includes(
          beforePackage.packageCode,
        )
          ? 3000
          : 0;
      } else if (hasBeforePackageFree) {
        userUpdateFields.remainingCharacters = 0;
        userUpdateFields.lockCharacters = updatePaidCharacters;
        userUpdateFields.bonusCharacters = 3000;
      } else {
        userUpdateFields.remainingCharacters = updatePaidCharacters;
        userUpdateFields.bonusCharacters = updateBonusCharacters;
      }

      userUpdateFields.studio = {
        cycle: {
          nextResetAt:
            beforePackage?.studio?.cycle?.nextResetAt ||
            beforePackage?.packageExpiryDate,
          remainingCredits: beforePackage?.studio?.cycle?.remainingCredits || 0,
          purchasedTopUpCredits:
            beforePackage?.studio?.cycle?.purchasedTopUpCredits || 0,
          creditsPerCycle: beforePackage?.studio?.cycle?.creditsPerCycle || 0,
          capturedRolloverCredits:
            beforePackage?.studio?.cycle?.capturedRolloverCredits || 0,
        },
        topUp: {
          remainingCredits: beforePackage?.studio?.topUp?.remainingCredits || 0,
        },
      };

      break;
    }
  }

  await orderDao.updateOrderById(orderId, { cancelOrder: true });
  await userDao.updateUserById(userId, userUpdateFields);

  // TODO: send notification to user
};

const handlePayOrderBySwitchVersion = async ({
  userId,
  orderId,
  orderType,
  characters = 0,
  bonusCharacters = 0,
  lockCharacters = 0,
}) => {
  const userUpdateFields =
    orderType === PACKAGE_TYPE.API
      ? {
          'apiCharacters.remainingCharacters': characters,
          'apiCharacters.bonusCharacters': bonusCharacters,
          'apiCharacters.lockCharacters': lockCharacters,
        }
      : {
          remainingCharacters: characters,
          bonusCharacters,
          lockCharacters,
        };

  await orderDao.updateOrderById(orderId, {
    userId,
    payOrder: true,
    type: orderType,
  });
  const user = await userDao.updateUserById(userId, userUpdateFields);

  sendMessage(KAFKA_TOPIC.ORDER_PAID, {
    value: {
      userId,
      type: orderType,
      remainingCharacters: Number(user.remainingCharacters),
      bonusCharacters: Number(user.bonusCharacters),
      lockCharacters: Number(user.lockCharacters),
    },
  });
};

const handleSpendStudioCredits = ({
  sentencesCreditsInfo,
  user,
  credits,
  blockedCredits,
}) => {
  const isFreeUser = packageService.checkFreePackage(user.packageCode);
  let userUpdateFields = {};
  if (!sentencesCreditsInfo)
    userUpdateFields = getUserUpdateCredits({
      user,
      credits,
      blockedCredits,
    });
  else
    userUpdateFields = getUserUpdateCreditsProcessWallet({
      sentencesCreditsInfo,
      currWallets: {
        onetimeCredits: user.remainingCharacters + user.bonusCharacters || 0,
        cycleCredits: user?.studio?.cycle?.remainingCredits || 0,
        topUpCredits: user?.studio?.topUp?.remainingCredits || 0,
        customCredits: user?.studio?.custom?.remainingCredits || 0,
      },
      isFreeUser,
    });

  return userUpdateFields;
};

const processRequestNotEnoughCredits = async ({
  userId,
  requestId,
  characters,
  userUpdateFields,
}) => {
  logger.warn('Remaining characters is not enough, skip request as failed', {
    userId,
    requestId,
    characters,
    userUpdateFields,
    ctx: 'HandleSpendCharacters',
  });
  await requestDao.updateRequestById(requestId, {
    status: REQUEST_STATUS.FAILURE,
    usedCharacters: 0,
    paid: false,
    error: getErrorMessage(code.EXCEED_CHARACTERS),
    errorCode: code.EXCEED_CHARACTERS,
  });
  sendMessage(KAFKA_TOPIC.TTS_FAILURE, {
    value: {
      requestId,
      userId,
      paidCharacters: 0,
      bonusCharacters: 0,
    },
  });
};

const handleSpendCharacters = async ({
  userId,
  user,
  requestId,
  requestType,
  characters,
  userBonusCharacters,
  userRemainingCharacters,
  blockedCredits,
  sentencesCreditsInfo,
}) => {
  const usedCharacters = {};
  let userUpdateFields = {};
  let wallet = {};

  // With api keep old logic, studio spend characters with new logic
  if (requestType === REQUEST_TYPE.API) {
    if (characters >= userBonusCharacters) {
      userUpdateFields.bonusCharacters = 0;
      userUpdateFields.remainingCharacters =
        userRemainingCharacters - (characters - userBonusCharacters);

      usedCharacters.bonus = userBonusCharacters;
      usedCharacters.paid = characters - userBonusCharacters;
    } else {
      userUpdateFields.bonusCharacters = userBonusCharacters - characters;

      usedCharacters.bonus = characters;
      usedCharacters.paid = 0;
    }
  } else {
    userUpdateFields = handleSpendStudioCredits({
      sentencesCreditsInfo,
      user,
      credits: characters,
      blockedCredits,
    });

    // Apply with flow process sentences credits info
    if (!userUpdateFields) {
      await processRequestNotEnoughCredits({
        userId,
        requestId,
        characters,
        userUpdateFields,
      });
      return;
    }

    usedCharacters.paid =
      user.remainingCharacters - userUpdateFields.remainingCharacters || 0;
    usedCharacters.bonus =
      user.bonusCharacters - userUpdateFields.bonusCharacters || 0;
    wallet = {
      cycle:
        user?.studio?.cycle?.remainingCredits -
          userUpdateFields?.['studio.cycle.remainingCredits'] || 0,
      topUp:
        user?.studio?.topUp?.remainingCredits -
          userUpdateFields?.['studio.topUp.remainingCredits'] || 0,
      bonus: user.bonusCharacters - userUpdateFields.bonusCharacters || 0,
      oneTime:
        user.remainingCharacters - userUpdateFields.remainingCharacters || 0,
      custom:
        user?.studio?.custom?.remainingCredits -
          userUpdateFields?.['studio.custom.remainingCredits'] || 0,
    };
  }

  if (requestType === REQUEST_TYPE.API) {
    userUpdateFields = Object.keys(userUpdateFields).reduce((acc, key) => {
      acc[`apiCharacters.${key}`] = userUpdateFields[key];
      return acc;
    }, {});

    const remainingCharacters =
      user?.apiCharacters?.remainingCharacters +
      user?.apiCharacters?.bonusCharacters -
      characters;
    const { isSendWarning } = user;

    if (
      remainingCharacters < API_CHARACTERS_RUN_OUT_NUMBER &&
      !isSendWarning?.apiCharactersRunningOut
    ) {
      userUpdateFields['isSendWarning.apiCharactersRunningOut'] = true;
      sendMessage(KAFKA_TOPIC.API_CHARACTERS_RUN_OUT, {
        value: {
          userId,
          remainingCharacters,
        },
      });
    }

    if (remainingCharacters > API_CHARACTERS_RUN_OUT_NUMBER)
      userUpdateFields['isSendWarning.apiCharactersRunningOut'] = false;

    if (
      remainingCharacters < API_CHARACTERS_REACHED_ZERO_NUMBER &&
      !isSendWarning?.apiCharactersReachedZero
    ) {
      userUpdateFields['isSendWarning.apiCharactersReachedZero'] = true;
      sendMessage(KAFKA_TOPIC.API_CHARACTERS_REACHED_ZERO, {
        value: {
          userId,
          remainingCharacters,
        },
      });
    }

    if (remainingCharacters > API_CHARACTERS_REACHED_ZERO_NUMBER)
      userUpdateFields['isSendWarning.apiCharactersReachedZero'] = false;
  }

  const isExceedCustomCredits =
    userUpdateFields?.['studio.custom.remainingCredits'] < 0;

  // for free package, if remainingCharacters < 0 then set request to fail dont update characters
  if (isExceedCustomCredits) {
    await processRequestNotEnoughCredits({
      userId,
      requestId,
      characters,
      userUpdateFields,
    });
    return;
  }
  await requestDao.updateRequestById(requestId, {
    usedCharacters,
    paid: true,
    wallet,
  });
  await userDao.updateUserById(userId, userUpdateFields);
};

const handleRefundCharacters = async ({
  userId,
  requestId,
  requestType,
  usedCharacters = {},
  usedWallet = {},
  user = {},
}) => {
  const STUDIO_REQUEST_TYPE = [REQUEST_TYPE.STUDIO, REQUEST_TYPE.DUBBING];
  const isStudioRequest = STUDIO_REQUEST_TYPE.includes(requestType);

  const userCharacters = isStudioRequest ? user : user.apiCharacters;
  const { bonus: usedBonus = 0, paid: usedPaid = 0 } = usedCharacters;

  let userUpdateFields = {
    bonusCharacters: userCharacters.bonusCharacters + usedBonus,
    remainingCharacters: userCharacters.remainingCharacters + usedPaid,
  };

  if (requestType === REQUEST_TYPE.API) {
    userUpdateFields = Object.keys(userUpdateFields).reduce((acc, key) => {
      acc[`apiCharacters.${key}`] = userUpdateFields[key];
      return acc;
    }, {});
  }

  const {
    cycle: creditsFromCycle = 0,
    topUp: creditsFromTopUp = 0,
    oneTime: creditsFromOneTime = 0,
    bonus: creditsFromBonus = 0,
    custom: creditsFromCustom = 0,
  } = usedWallet;

  // If user has cycle or topUp and used credits from cycle or topUp
  // Then refund credits to cycle or topUp
  if (user?.studio?.cycle) {
    userUpdateFields['studio.cycle.remainingCredits'] =
      user.studio.cycle.remainingCredits + creditsFromCycle;
  }
  if (user?.studio?.topUp) {
    userUpdateFields['studio.topUp.remainingCredits'] =
      user.studio.topUp.remainingCredits + creditsFromTopUp;
  }

  if (user?.studio?.custom)
    userUpdateFields['studio.custom.remainingCredits'] =
      user.studio.custom.remainingCredits + creditsFromCustom;

  userUpdateFields = {
    ...userUpdateFields,
    remainingCharacters: user.remainingCharacters + creditsFromOneTime,
    bonusCharacters: user.bonusCharacters + creditsFromBonus,
  };

  await requestDao.updateRequestById(requestId, { refund: true });
  await userDao.updateUserById(userId, userUpdateFields);
};

const handleResetMonthlyCharacters = async ({
  userId,
  orderId,
  orderType,
  characters = 0,
  bonusCharacters = 0,
}) => {
  logger.info('HandleResetMonthlyCharacters', {
    ctx: 'UpdateByCharacters',
    userId,
    orderId,
    orderType,
    characters,
    bonusCharacters,
  });

  await orderDao.updateOrderById(orderId, {
    userId,
    lastResetMonthly: new Date(),
    type: orderType,
  });
  await userDao.updateUserById(
    userId,
    orderType === PACKAGE_TYPE.API
      ? {
          'apiCharacters.remainingCharacters': characters,
          'apiCharacters.bonusCharacters': bonusCharacters,
        }
      : { remainingCharacters: characters, bonusCharacters },
  );
};

const handleLockCharacters = async (
  userId,
  userRemainingCharacters = 0,
  orderType,
) => {
  await userDao.updateUserById(
    userId,
    orderType === PACKAGE_TYPE.API
      ? {
          'apiCharacters.remainingCharacters': 0,
          'apiCharacters.lockCharacters': userRemainingCharacters,
          'apiCharacters.lockedCharactersAt': new Date(),
        }
      : {
          remainingCharacters: 0,
          lockCharacters: userRemainingCharacters,
          lockedCharactersAt: new Date(),
        },
  );
};

const handleRefundPreviewCharacters = async ({
  userId,
  characters,
  userRemainingCharacters,
}) => {
  await userDao.updateUserById(userId, {
    remainingCharacters: userRemainingCharacters + characters,
  });
};

const calculateRolloverCredits = ({
  creditsPerCycle,
  remainingCycleCredits = 0,
  remainingTopUpCredits = 0,
}) => {
  const MAX_ROLLOVER_CYCLES = 2;
  const maxRolloverCredits = creditsPerCycle * MAX_ROLLOVER_CYCLES;

  const rolloverCycleCredits = Math.min(
    remainingCycleCredits,
    maxRolloverCredits,
  );

  const rolloverTopUpCredits = Math.min(
    maxRolloverCredits - rolloverCycleCredits,
    remainingTopUpCredits,
  );

  return { rolloverCycleCredits, rolloverTopUpCredits };
};

const calculateNextResetAt = ({
  isPackageExpired,
  packageStartDate,
  packageExpiryDate,
  nextResetAt,
}) => {
  const cycleNumber = moment().diff(moment(packageStartDate), 'months') + 1;

  // If package is expired then no need to reset next reset at
  if (isPackageExpired) return nextResetAt;

  // Next reset at is package start date add current cycle months
  let newNextResetAt = moment(packageStartDate)
    .add(cycleNumber, 'months')
    .endOf('day')
    .toDate();

  // If new next reset at is after package expiry date then set next reset at to package expiry date
  if (moment(nextResetAt).isAfter(packageExpiryDate))
    newNextResetAt = packageExpiryDate;

  return newNextResetAt;
};

const isAdvancedGlobalVoice = (voice) => {
  if (!voice) return false;

  const isGlobalVoice =
    voice.provider &&
    ![VOICE_PROVIDER.VBEE, VOICE_PROVIDER.VBEE_VOICE_CLONING].includes(
      voice.provider,
    );

  return voice.level === VOICE_LEVEL.ADVANCED && isGlobalVoice;
};

// Check lock one time credits for advanced global voice
const shouldLockOneTimeCredits = ({ user, voice }) => {
  if (!isAdvancedGlobalVoice(voice)) return false;

  const useLockOneTimeCreditsAdvancedVoice = getFeatureValue(
    FEATURE_KEYS.LOCK_ONE_TIME_CREDITS_ADVANCED_VOICE,
    {
      userId: user._id,
      voiceCode: voice.code,
      voiceName: voice.name,
    },
  );

  return useLockOneTimeCreditsAdvancedVoice;
};

// Check lock cycle credits for user has order pending
const shouldLockCycleCredits = ({ user, voice }) => {
  const { features = [] } = user || {};
  if (voice && !checkVoicePermission(features, voice?.features)) return true;
  const isBlockUserCycleCredits = user?.studio?.cycle?.isLocked;

  return isBlockUserCycleCredits;
};

// Check lock topUp credits for user has order pending
const shouldLockTopUpCredits = ({ user, voice }) => {
  const { features = [] } = user || {};
  if (voice && !checkVoicePermission(features, voice?.features)) return true;

  const isBlockUserCycleCredits = user?.studio?.topUp?.isLocked;

  return isBlockUserCycleCredits;
};

const shouldLockCustomCredits = (user) => {
  const isBlockUserCustomCredits = user?.studio?.custom?.isLocked;
  return isBlockUserCustomCredits;
};

const updateByCharacters = async ({
  event,
  userId,
  orderId,
  orderType,
  isTopUp,
  requestId,
  credits,
  characters,
  bonusCharacters,
  remainingCharacters,
  isApiCharacters,
  lockCharacters,
  isFreePackage,
  beforePackage,
  packageCode,
  logId,
  cyclePackageInfo,
  topUpPackageInfo,
  blockedCredits,
  isRolloverCredits,
  isMoveRemainingToLock = true,
  isCustomPackage,
  sentencesCreditsInfo,
}) => {
  const STUDIO_REQUEST_TYPE = [REQUEST_TYPE.STUDIO, REQUEST_TYPE.DUBBING];

  let user = await AuthorizationService.getUser(userId, false);
  // TODO: Check user exist and retry if not found
  switch (event) {
    case SYNC_CHARACTERS_EVENT.PAY_ORDER: {
      if (!user) {
        logger.error('User not found', {
          userId,
          ctx: 'UpdateByCharacters',
          event,
        });
        user = await getUserByIdWithRetry(userId);
      }

      const order = await orderDao.findOrder({ _id: orderId });
      if (order && order.payOrder) break;

      const isApiPayg = orderType === PACKAGE_TYPE.API && characters < 0;
      if (isApiPayg) characters = 0;

      // ADVISE: duplicated code in this same file userRemainingCharacters, userLockCharacters
      const userRemainingCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.remainingCharacters || 0
          : user?.remainingCharacters || 0;
      const userLockCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.lockCharacters || 0
          : user?.lockCharacters || 0;

      await handlePayOrder({
        userId,
        orderId,
        orderType,
        isTopUp,
        userRemainingCharacters,
        userLockCharacters,
        credits,
        characters,
        bonusCharacters,
        isFreePackage,
        packageCode,
        studio: user?.studio,
        cyclePackageInfo,
        topUpPackageInfo,
        currentPackageCode: user?.packageCode,
        isRolloverCredits,
        isCustomPackage,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.CANCEL_ORDER_AFTER_PAID: {
      const order = await orderDao.findOrder({ _id: orderId });
      if (order?.cancelOrder) break;

      // ADVISE: duplicated code in this same file userRemainingCharacters, userLockCharacters
      const userRemainingCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.remainingCharacters || 0
          : user?.remainingCharacters || 0;
      const userLockCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.lockCharacters || 0
          : user?.lockCharacters || 0;
      const userBonusCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.bonusCharacters || 0
          : user?.bonusCharacters || 0;

      await handleCancelOrder({
        userId,
        orderId,
        orderType,
        userRemainingCharacters,
        userLockCharacters,
        userBonusCharacters,
        characters,
        bonusCharacters,
        beforePackage,
      });

      break;
    }

    // TODO: check with API request
    case SYNC_CHARACTERS_EVENT.SPEND: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const request = await requestDao.findRequestById(requestId);
      if (!request) {
        logger.error('Request not found', {
          requestId,
          ctx: 'UpdateByCharacters',
        });
        break;
      }

      if (request.paid) break;

      const { type: requestType } = request;
      orderType = requestType;

      const userBonusCharacters = STUDIO_REQUEST_TYPE.includes(requestType)
        ? user.bonusCharacters
        : user.apiCharacters.bonusCharacters;
      const userRemainingCharacters = STUDIO_REQUEST_TYPE.includes(requestType)
        ? user.remainingCharacters
        : user.apiCharacters.remainingCharacters;

      await handleSpendCharacters({
        userId: user._id,
        user,
        requestId,
        requestType,
        characters,
        userBonusCharacters,
        userRemainingCharacters,
        blockedCredits,
        sentencesCreditsInfo,
      });

      break;
    }

    // TODO: check with API request
    case SYNC_CHARACTERS_EVENT.REFUND: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const request = await requestDao.findRequestById(requestId);

      if (!request) {
        logger.error('Request not found', {
          requestId,
          ctx: 'UpdateByCharacters',
        });
        break;
      }

      if (request.refund || request.demo) break;

      const { type: requestType } = request;
      orderType = requestType;

      await handleRefundCharacters({
        userId: user._id,
        requestId,
        requestType,
        usedCharacters: request.usedCharacters,
        usedWallet: request?.wallet,
        user,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.RESET_BONUS_CHARACTERS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      // Get field and value to check
      const fieldCheckUpdated =
        bonusCharacters === 0 ? 'lastResetToZeroAt' : 'lastResetToDefaultAt';
      const valueCheckUpdated = user[fieldCheckUpdated];

      // Check if order has been reset today
      const isDuplicateUpdate =
        valueCheckUpdated && moment().isSame(valueCheckUpdated, 'day');

      logger.info(`Order has been reset today: ${isDuplicateUpdate}`, {
        ctx: 'UpdateByCharacters',
        userId,
        [fieldCheckUpdated]: valueCheckUpdated,
      });
      if (isDuplicateUpdate) break;

      // If received bonus characters is 0 and user has not reset to zero today
      // then reset to zero and update lastResetToZeroAt
      // If received bonus characters is greater than 0 and user has not reset to default today
      // then reset to default and update lastResetToDefaultAt
      await userDao.updateUserById(userId, {
        bonusCharacters,
        [fieldCheckUpdated]: new Date(),
      });

      sendMessage(KAFKA_TOPIC.RESET_FREE_CHARACTERS, {
        key: userId,
        value: {
          userId,
          bonusCharacters,
        },
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.RESET_MONTHLY_CHARACTERS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const order = await orderDao.findOrder({ _id: orderId });

      const isDuplicate =
        order?.lastResetMonthly &&
        moment().isSame(order.lastResetMonthly, 'day');
      if (isDuplicate) break;

      await handleResetMonthlyCharacters({
        userId,
        orderId,
        orderType,
        characters,
        bonusCharacters,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.LOCK_CHARACTERS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const userLockedCharactersAt =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.lockedCharactersAt
          : user?.lockedCharactersAt;
      const isDuplicate =
        userLockedCharactersAt &&
        moment().isSame(userLockedCharactersAt, 'day');
      if (isDuplicate) break;

      let userRemainingCharacters =
        orderType === PACKAGE_TYPE.API
          ? user?.apiCharacters?.remainingCharacters || 0
          : user?.remainingCharacters || 0;

      if (!isMoveRemainingToLock) userRemainingCharacters = 0;

      await handleLockCharacters(userId, userRemainingCharacters, orderType);

      break;
    }

    // TODO: check with API request
    case SYNC_CHARACTERS_EVENT.REFUND_PREVIEW_CHARACTERS: {
      if (!user) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      await handleRefundPreviewCharacters({
        userId,
        characters,
        userRemainingCharacters: user.remainingCharacters,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.SWITCH_VERSION: {
      const order = await orderDao.findOrder({ _id: orderId });
      if (order && order.payOrder) break;

      await handlePayOrderBySwitchVersion({
        userId,
        orderId,
        orderType,
        characters,
        bonusCharacters,
        lockCharacters,
      });

      break;
    }

    // TODO: check with API package
    case SYNC_CHARACTERS_EVENT.ADD_BONUS_CHARACTERS: {
      if (!userId) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const newBonusCharacters = user.bonusCharacters + bonusCharacters;
      await userDao.updateUserById(userId, {
        bonusCharacters: newBonusCharacters < 0 ? 0 : newBonusCharacters,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.ADD_REMAINING_CHARACTERS: {
      if (!userId) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }
      if (isApiCharacters) {
        const newApiCharacters = {
          remainingCharacters:
            (user.apiCharacters?.remainingCharacters || 0) +
            remainingCharacters,
          bonusCharacters: user.apiCharacters?.bonusCharacters || 0,
          lockCharacters: user.apiCharacters?.lockCharacters || 0,
          lockedCharactersAt: user.apiCharacters?.lockedCharactersAt,
        };

        await userDao.updateUserById(userId, {
          apiCharacters: newApiCharacters,
        });

        break;
      }
      const newRemainingCharacters =
        user.remainingCharacters + remainingCharacters;
      await userDao.updateUserById(userId, {
        remainingCharacters:
          newRemainingCharacters < 0 ? 0 : newRemainingCharacters,
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.RESET_CREDITS_PER_CYCLE: {
      if (!userId) {
        logger.error('User not found', { userId, ctx: 'UpdateByCharacters' });
        break;
      }

      const {
        packageStartDate,
        packageExpiryDate,
        studio: { cycle, topUp } = {},
      } = user;

      const {
        remainingCredits: remainingCycleCredits,
        creditsPerCycle,
        nextResetAt,
      } = cycle || {};
      const { remainingCredits: remainingTopUpCredits } = topUp || {};

      const { rolloverCycleCredits, rolloverTopUpCredits } =
        calculateRolloverCredits({
          creditsPerCycle,
          remainingCycleCredits,
          remainingTopUpCredits,
        });

      const isNextResetAtNotYesterday = !moment(nextResetAt).isSame(
        moment().subtract(1, 'days'),
        'day',
      );
      if (isNextResetAtNotYesterday) {
        await resetLogDao.updateLogById(logId, {
          resetCreditsPerCycleStatus: RESET_CREDITS_STATUS.FAILURE,
          resetCreditsPerCycleLastUpdated: moment().toDate(),
          resetCreditsPerCycleNote: `Next reset at is not yesterday: ${nextResetAt}`,
        });
        break;
      }

      const isPackageExpired =
        moment(packageExpiryDate).isSameOrBefore(nextResetAt);

      // Calculate new remaining cycle credits and top up credits
      const newRemainingCycleCredits = isPackageExpired
        ? 0
        : creditsPerCycle + rolloverCycleCredits;
      const newRemainingTopUpCredits = isPackageExpired
        ? 0
        : rolloverTopUpCredits;

      // Capture rollover cycle credits and top up credits
      const capturedRolloverCycleCredits = isPackageExpired
        ? 0
        : rolloverCycleCredits;
      const capturedRolloverTopUpCredits = isPackageExpired
        ? 0
        : rolloverTopUpCredits;

      const newNextResetAt = calculateNextResetAt({
        isPackageExpired,
        packageStartDate,
        packageExpiryDate,
        nextResetAt,
      });

      const userUpdateFields = {
        'studio.cycle.remainingCredits': newRemainingCycleCredits,
        'studio.cycle.nextResetAt': newNextResetAt,
        'studio.cycle.purchasedTopUpCredits': 0,
        'studio.cycle.capturedReferralCredits': 0,
        'studio.cycle.capturedRolloverCredits': capturedRolloverCycleCredits,
        'studio.topUp.remainingCredits': newRemainingTopUpCredits,
        'studio.topUp.capturedRolloverCredits': capturedRolloverTopUpCredits,
      };

      await userDao.updateUserById(userId, userUpdateFields);

      await resetLogDao.updateLogById(logId, {
        resetCreditsPerCycleStatus: RESET_CREDITS_STATUS.SUCCESS,
        resetCreditsPerCycleLastUpdated: moment().toDate(),
      });

      break;
    }

    case SYNC_CHARACTERS_EVENT.REDEEM_REFERRAL: {
      if (!user) {
        logger.error('User not found', {
          userId,
          ctx: 'UpdateByCharacters',
          event,
        });
        user = await getUserByIdWithRetry(userId);
      }

      // TODO: Handle other reward types
      await handleRedeemReferral({ user, characters });
      break;
    }

    default:
      return;
  }

  user = await AuthorizationService.getUser(userId, false);
  const userRemainingCharacters =
    orderType === PACKAGE_TYPE.API
      ? user?.apiCharacters?.remainingCharacters || 0
      : user?.remainingCharacters || 0;
  const userBonusCharacters =
    orderType === PACKAGE_TYPE.API
      ? user?.apiCharacters?.bonusCharacters || 0
      : user?.bonusCharacters || 0;
  const userLockCharacters =
    orderType === PACKAGE_TYPE.API
      ? user?.apiCharacters?.lockCharacters || 0
      : user?.lockCharacters || 0;
  let userApiCharacters;
  if (isApiCharacters) {
    userApiCharacters = {
      remainingCharacters: user.apiCharacters?.remainingCharacters,
      bonusCharacters: user.apiCharacters?.bonusCharacters,
      lockCharacters: user.apiCharacters?.lockCharacters,
      syncCharactersAt: new Date(),
    };
  }

  logger.info('Sync characters', {
    ctx: 'UpdateByCharacters',
    userId,
    orderType,
    remainingCharacters: userRemainingCharacters,
    bonusCharacters: userBonusCharacters,
    lockCharacters: userLockCharacters,
  });
  const syncData = {
    event,
    userId,
    orderId,
    orderType,
    remainingCharacters: userRemainingCharacters,
    bonusCharacters: userBonusCharacters,
    lockCharacters: userLockCharacters,
    apiCharacters: userApiCharacters,
    syncCharactersAt: new Date(),
    beforePackage,
    studio: user?.studio,
    nextResetAt: user?.studio?.cycle?.nextResetAt,
  };
  if (event === SYNC_CHARACTERS_EVENT.PAY_ORDER) {
    if (orderType === PACKAGE_TYPE.API)
      syncData.totalApiCharacters =
        user?.apiCharacters?.remainingCharacters || 0;
    else syncData.totalStudioCharacters = user?.remainingCharacters || 0;
  }

  sendMessage(KAFKA_TOPIC.SYNC_CHARACTERS, {
    key: userId,
    value: syncData,
  });
};

const migrateAccount = async ({
  overwrite = false,
  userId,
  remainingCharacters,
  bonusCharacters,
  lockCharacters,
  packageCode,
  packageExpiryDate,
  maxLengthInputText,
  speedConvert,
  concurrentRequest,
  download,
  retentionPeriod,
  features,
  overwriteAt,
}) => {
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: BUSINESS can we throw/return ASAP when there is no user? there will be nothing to update?

  if (
    overwrite &&
    overwriteAt &&
    user.overwriteAt &&
    moment(overwriteAt).isSameOrBefore(user.overwriteAt)
  )
    return;
  if (!overwrite && user?.isMigrated) return;

  const updateData = {
    remainingCharacters,
    bonusCharacters,
    lockCharacters,
    packageCode,
    packageExpiryDate,
    maxLengthInputText,
    speedConvert,
    concurrentRequest,
    download,
    retentionPeriod,
    features,
  };
  if (overwrite) updateData.overwriteAt = overwriteAt;
  else updateData.isMigrated = true;

  await userDao.updateUserById(userId, omitIsNil(updateData, { deep: true }));
};

const decreaseMaxPreviewByRequest = async (requestId) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request?.userId) return;

  await userDao.decreaseMaxPreview(request.userId);
};

const getUsersNotResetCharacter = async ({
  packageCode,
  totalBonusCharacters,
  limit,
}) => {
  const userIds = await userDao.findUsersNotResetCharacter({
    packageCode,
    totalBonusCharacters,
    limit,
  });
  return userIds;
};

const getUsersNotResetSecond = async ({
  packageCode,
  totalBonusSeconds,
  limit,
}) => {
  const userIds = await userDao.findUsersNotResetSecond({
    packageCode,
    totalBonusSeconds,
    limit,
  });
  return userIds;
};

const migrateVoiceCloningFeatureForUserUsePackageV1 = async (user) => {
  const isMigrate = getFeatureValue(
    FEATURE_KEYS.MIGRATE_VOICE_CLONING_FEATURE,
    { packageCode: user.packageCode, userId: user._id },
  );
  const isExpired = moment().isAfter(user.packageExpiryDate);
  // If user is not expired a not need to migrate or user have voice cloning feature then return user
  const migrateVoiceCloningFeature =
    !!isMigrate &&
    !isExpired &&
    !user.features?.includes(PACKAGE_FEATURE.AI_VOICE_CLONING);

  if (!migrateVoiceCloningFeature) return user;

  const updatedUser = await userDao.migrateVoiceCloningFeatures(user._id);
  return updatedUser;
};

const getUserByUserId = async (userId) => {
  const user = await userDao.findUserById(userId);
  return user;
};

const getUser = async (userId, ip) => {
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: BUSINESS can we throw/return ASAP when there is no user? there will be nothing to migrate?

  // ADVISE: this is get action, should not have side-effect like resetBonusCharacters or migrate ...
  await resetBonusCharacters(user, ip);

  // Migrate voice cloning feature for user use package v1
  const migratedUser = await migrateVoiceCloningFeatureForUserUsePackageV1(
    user,
  );

  const migratedDubbing = await findMigratedDubbingUser(user);
  return { ...migratedUser, migratedDubbing };
};

const getUserByIdWithRetry = async (userId) => {
  return new Promise((resolve, reject) => {
    const operation = retry.operation({
      retries: 3, // Maximum 3 retries
      factor: 2, // Exponential backoff factor
      minTimeout: 1000, // Start with 1 second delay
      maxTimeout: 10000, // Max 10 seconds between retries
      randomize: true, // Add some randomization to prevent thundering herd
    });

    operation.attempt(async (currentAttempt) => {
      try {
        const user = await AuthorizationService.getUser(userId, false);

        // Throw error if user not found to retry
        if (!user) throw new Error('User not found');
        resolve(user);
      } catch (err) {
        if (operation.retry(err)) {
          logger.warn(
            `Get user by id attempt ${currentAttempt} failed, retrying...`,
            { ctx: 'GetUserByIdWithRetry', userId },
          );
          return;
        }
        reject(operation.mainError());
      }
    });
  });
};

const resetBonusCharacters = async (user, ip) => {
  try {
    const { _id: userId, packageCode, lastResetToDefaultAt } = user;

    // Check if user use package that reset bonus characters everyday
    const userUsingResetCharactersPackage = [
      PACKAGE_CODE.STUDIO_FREE,
      PACKAGE_CODE.STUDIO_FREE_V2,
      PACKAGE_CODE.STUDIO_FREE_V3,
      PACKAGE_CODE.STUDIO_BASIC,
    ].includes(packageCode);
    if (!userUsingResetCharactersPackage) return;

    const hasResetCharacters =
      lastResetToDefaultAt && moment().isSame(lastResetToDefaultAt, 'day');
    if (hasResetCharacters) {
      logger.info(`Order has been reset today: ${hasResetCharacters}`, {
        ctx: 'ResetBonusCharacters',
        userId,
        lastResetToDefaultAt,
      });
      return;
    }

    const bonusCharacter = await packageService.getBonusCredits(user, ip);

    const characterData = {
      event: SYNC_CHARACTERS_EVENT.RESET_BONUS_CHARACTERS,
      userId,
      orderType: PACKAGE_TYPE.STUDIO,
      bonusCharacters: bonusCharacter,
    };
    await updateByCharacters(characterData);
  } catch (error) {
    logger.error(error, { ctx: 'ResetBonusCharacters' });
  }
};

// ADVISE: rename to getAppOwnerUser. Duplicated with this.findAdminApp()
const getUserFromApp = async (app) => {
  const { members = [] } = app;
  const appAdmin = members.find((member) => member.role === APP_ROLE.ADMIN);
  if (!appAdmin) throw new CustomError(code.NOT_FOUND, 'App admin not found');

  // ADVISE: check the naming in runtime, why userId is an object?
  const { userId: appAdminUser } = appAdmin;
  const { _id: userId } = appAdminUser;

  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: BUSINESS can we throw if user not found? this is invalid data, data mismatch problem
  return user;
};

// ADVISE: rename to isSpamAccount
const checkSpamAccount = async ({
  email,
  ip,
  packageCode,
  userId,
  feature,
}) => {
  // ADVISE: remote config, should be in centralized ConfigService (to be cachable, has fallback value, ...)
  const featureConfig = getFeatureValue(
    FEATURE_KEYS.BLOCK_SYNTHESIS_REQUEST_FOR_SPAM_ACCOUNT,
  );
  if (!featureConfig) return false;

  const {
    checkIp,
    checkEmail,
    checkUserId,
    // NOTE: At this moment, packageCodes is an array of free package codes
    packageCodes = [],
    whitelistIps = [],
    whitelistEmails = [],
  } = featureConfig;

  const isInBlacklistPackageCode = packageCodes.includes(packageCode);

  // If paid user, we don't need to check banned account with feature TTS
  // TODO: Remove this logic, when user upgrade to paid package, we will remove item in banned account
  if (!isInBlacklistPackageCode && feature === PACKAGE_FEATURE.TTS)
    return false;

  // Check user id
  if (checkUserId) {
    const isBlockedFeature = BannedAccountService.checkBannedAccount({
      type: RATE_LIMIT_TYPE.USER_ID,
      value: userId,
      feature,
      isInBlacklistPackageCode,
    });

    if (isBlockedFeature) return true;
  }

  const isWhitelistedIp = whitelistIps.includes(ip);
  const emailDomain = getEmailDomain(email);
  const isWhitelistedEmail = checkDomainMatched(emailDomain, whitelistEmails);
  if (isWhitelistedEmail && isWhitelistedIp) return false;

  const shouldCheckEmail = checkEmail && !isWhitelistedEmail;
  const shouldCheckIp = checkIp && !isWhitelistedIp;

  if (shouldCheckEmail) {
    const isBannedEmail = BannedAccountService.checkBannedAccount({
      type: RATE_LIMIT_TYPE.EMAIL,
      value: email,
      feature,
      isInBlacklistPackageCode,
    });
    if (isBannedEmail) return true;
  }

  if (shouldCheckIp) {
    const isBannedIp = BannedAccountService.checkBannedAccount({
      type: RATE_LIMIT_TYPE.IP,
      value: ip,
      feature,
      isInBlacklistPackageCode,
    });
    if (isBannedIp) return true;
  }

  return false;
};

const findAdminApp = async (app) => {
  const { members = [] } = app;
  const appAdmin = members.find((member) => member.role === APP_ROLE.ADMIN);
  if (!appAdmin) throw new CustomError(code.NOT_FOUND, 'App admin not found');

  // ADVISE: check the naming in runtime, why userId is an object?
  const { userId: appAdminUser } = appAdmin;
  const { _id: userId } = appAdminUser;

  // ADVISE: this is wasted, because caller of this func only use _id or something already existed in members.user_id
  const adminUser = await AuthorizationService.getUser(userId, false);
  // ADVISE: BUSINESS can we throw if adminUser not found? this is invalid data, data mismatch problem
  return adminUser;
};

const handleMigrateDubbingToStudio = async ({
  event = '',
  userId,
  orderId,
  orderType,
  characters,
  lockCharacters,
  packageCode,
  oldCharactersAndSeconds,
}) => {
  const updateFields = {
    remainingCharacters: characters,
    lockCharacters,
    packageCode,
    'dubbing.remainingSeconds': 0,
    'dubbing.lockSeconds': 0,
  };

  await userDao.updateUserById(userId, updateFields);
  await migratedDubbingUserDao.updateMigratedDubbingUser(
    { userId },
    { ...oldCharactersAndSeconds },
  );

  const syncStudioData = {
    event,
    userId,
    orderId,
    orderType,
    remainingCharacters: characters,
    bonusCharacters: 0,
    lockCharacters,
    syncCharactersAt: new Date(),
  };

  sendMessage(KAFKA_TOPIC.SYNC_CHARACTERS, {
    key: userId,
    value: syncStudioData,
  });

  const syncDubbingData = {
    event,
    userId,
    orderId,
    remainingSeconds: 0,
    lockSeconds: 0,
    totalDubbingSeconds: 0,
    syncCharactersAt: new Date(),
  };

  sendMessage(KAFKA_TOPIC.SYNC_SECONDS, {
    key: userId,
    value: syncDubbingData,
  });
};

const lockCredits = async ({ userId, action, walletTypes }) => {
  const isLocked = action === LOCK_ACTIONS.LOCK;
  await userDao.updateLockStudioCredits(userId, walletTypes, isLocked);
};

const getActiveWalletOfUserWithCurrentVoice = (user, voice) => {
  const isEolVoice = voice?.eolDate;

  const oneTimeExpiryDate =
    user?.stutio?.oneTime?.packageExpiryDate || user?.packageExpiryDate;
  const oneTimePackageCode =
    user?.studio?.oneTime?.packageCode || user?.packageCode;

  const isFreeUser = packageService.checkFreePackage(user?.packageCode);

  const canUseOneTimeCredit =
    isFreeUser ||
    (oneTimePackageCode &&
      oneTimeExpiryDate &&
      moment().isBefore(oneTimeExpiryDate));

  const { packageCode } = user;

  const isUsingFreePackage = packageService.checkFreePackage(packageCode);
  const isUsingCustomPackage = packageService.checkCustomPackage(packageCode);
  const isUsingOneTimePackage = packageService.checkOldPackage(packageCode);
  const isUsingCyclePackage =
    !isUsingFreePackage && !isUsingCustomPackage && !isUsingOneTimePackage;

  const DEFAULT_WALLET_ORDER = [
    {
      wallet: WALLET_TYPES.STUDIO_TOP_UP,
      expiryDate: user?.studio?.cycle?.nextResetAt || user?.packageExpiryDate,
    },
    {
      wallet: WALLET_TYPES.STUDIO_CYCLE,
      expiryDate: user?.studio?.cycle?.nextResetAt || user?.packageExpiryDate,
    },
    {
      wallet: WALLET_TYPES.STUDIO_ONE_TIME,
      expiryDate:
        user?.studio?.oneTime?.packageExpiryDate || user?.packageExpiryDate,
    },
    {
      wallet: WALLET_TYPES.STUDIO_CUSTOM,
      expiryDate: user?.packageExpiryDate,
    },
  ].sort((a, b) => {
    if (!a.expiryDate) return 1;
    if (!b.expiryDate) return -1;

    const dateA = moment(a.expiryDate);
    const dateB = moment(b.expiryDate);

    if (!dateA.isValid()) return 1;
    if (!dateB.isValid()) return -1;

    if (dateA.valueOf() === dateB.valueOf() && isEolVoice) {
      // If it's an EOL voice, prioritize ONE_TIME wallet
      if (a.wallet === WALLET_TYPES.STUDIO_ONE_TIME) return -1;
      if (b.wallet === WALLET_TYPES.STUDIO_ONE_TIME) return 1;
    }

    return dateA.valueOf() - dateB.valueOf();
  });

  const wallet = DEFAULT_WALLET_ORDER.reduce((acc, item) => {
    acc[item.wallet] = true;
    return acc;
  }, {});

  if (isUsingFreePackage) {
    wallet[WALLET_TYPES.STUDIO_CUSTOM] = false;
    wallet[WALLET_TYPES.STUDIO_CYCLE] = false;
    wallet[WALLET_TYPES.STUDIO_TOP_UP] = false;
  }

  if (isUsingCustomPackage) {
    wallet[WALLET_TYPES.STUDIO_CYCLE] = false;
    wallet[WALLET_TYPES.STUDIO_TOP_UP] = false;
    wallet[WALLET_TYPES.STUDIO_CUSTOM] = !shouldLockCustomCredits(user);
  }

  if (isUsingCyclePackage) {
    wallet[WALLET_TYPES.STUDIO_CUSTOM] = false;
    wallet[WALLET_TYPES.STUDIO_CYCLE] = !shouldLockCycleCredits({
      user,
      voice,
    });
    wallet[WALLET_TYPES.STUDIO_TOP_UP] = !shouldLockTopUpCredits({
      user,
      voice,
    });
  }

  wallet[WALLET_TYPES.STUDIO_ONE_TIME] = canUseOneTimeCredit;
  if (!isEolVoice && isAdvancedGlobalVoice(voice))
    wallet[WALLET_TYPES.STUDIO_ONE_TIME] = false;

  if (isEolVoice) {
    const isOneTimCreditsCanUseEOL =
      PACKAGE_HAS_EOL.includes(oneTimePackageCode);
    wallet[WALLET_TYPES.STUDIO_ONE_TIME] =
      canUseOneTimeCredit && isOneTimCreditsCanUseEOL;
  }

  return Object.entries(wallet)
    .filter(([, isActive]) => isActive)
    .map(([walletType]) => walletType);
};

const createShareVoiceProfile = async ({
  userId,
  userSlug,
  displayName,
  bio,
}) => {
  const dataUpdate = {
    userSlug,
    displayName,
    bio,
  };

  const userByUserSlug = await userDao.getUserByUserSlug(userSlug);
  if (userByUserSlug)
    throw new CustomError(
      code.USER_SLUG_ALREADY_EXIST,
      'User slug already exists',
    );

  const userByUserId = await userDao.findUserById(userId);
  if (userByUserId.shareVoiceProfile.userSlug)
    throw new CustomError(
      code.USER_SLUG_ALREADY_EXIST,
      'User slug already exists',
    );

  const userUpdated = await userDao.updateShareVoiceProfile(userId, dataUpdate);

  return userUpdated;
};

const checkCommunityVoiceRevenueAccess = async (userId) => {
  const user = await userDao.findUserById(userId);
  if (user?.hasEverHadCommunityVoice) return true;

  const { total } = await voiceCloningService.getCommunityVoiceCloningForUser(
    userId,
  );
  if (total > 0) {
    await userDao.updateUserById(userId, {
      hasEverHadCommunityVoice: true,
    });
    return true;
  }

  return false;
};

module.exports = {
  handleMigrateDubbingToStudio,
  updateByNewPackage,
  updateByCharacters,
  migrateAccount,
  decreaseMaxPreviewByRequest,
  getUsersNotResetCharacter,
  getUsersNotResetSecond,
  resetBonusCharacters,
  getUserByUserId,
  getUser,
  getUserFromApp,
  checkSpamAccount,
  findAdminApp,
  lockCredits,
  isAdvancedGlobalVoice,
  shouldLockCycleCredits,
  shouldLockTopUpCredits,
  shouldLockOneTimeCredits,
  shouldLockCustomCredits,
  getActiveWalletOfUserWithCurrentVoice,
  createShareVoiceProfile,
  checkCommunityVoiceRevenueAccess,
};
