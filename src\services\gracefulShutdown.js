const sleep = require('../utils/sleep');
const { getFeatureValue } = require('./growthbook');

const { SERVICE } = require('../configs');
const { FEATURE_KEYS } = require('../constants/featureKeys');

const gracefulShutdownLongPollingRequests = async () => {
  // check if long polling requests are not empty, wait for 10 seconds
  // check max 3 times then force abort all requests.
  const isLongPollingRequestsYetDone = () =>
    Object.keys(REQUEST_DIRECT).length > 0;

  let retry = 1;
  while (isLongPollingRequestsYetDone() && retry <= 3) {
    try {
      await sleep(10 * 1000);
    } catch (e) {
      logger.error(e, { ctx: 'GracefulShutdown' });
    } finally {
      retry += 1;
    }
  }
  if (isLongPollingRequestsYetDone()) {
    logger.warn('Force abort all long polling requests', {
      ctx: 'GracefulShutdown',
      numberOfDirectRequests: Object.keys(REQUEST_DIRECT).length,
    });
  }
};

const gracefulShutdownWebSocketServer = async () => {
  // check if requests are not empty, wait for 10 seconds
  // check max 3 times then force close all connections
  let count = 1;
  while (Object.keys(REQUESTS).length > 0 && count <= 3) {
    try {
      await sleep(10 * 1000);
    } catch (e) {
      logger.error(e, { ctx: 'GracefulShutdown' });
    } finally {
      count += 1;
    }
  }
  if (Object.keys(REQUESTS).length > 0) {
    logger.warn('Force close all websocket connections', {
      ctx: 'GracefulShutdown',
      numberOfConnections: Object.keys(REQUESTS).length,
    });
  }
  require('./ws').shutdown();
};

const gracefulShutdown = async () => {
  const enabledGracefulShutdown = await getFeatureValue(
    FEATURE_KEYS.GRACEFUL_SHUTDOWN,
    { service: SERVICE },
  );
  if (!enabledGracefulShutdown) return;

  logger.warn('Graceful shutdown starts', { ctx: 'GracefulShutdown' });
  // wait 3 seconds for Pod IP to be removed from control plane
  await sleep(3 * 1000);
  await Promise.all([
    gracefulShutdownWebSocketServer(),
    gracefulShutdownLongPollingRequests(),
  ]);
  // Notice that Kafka is not closed here
  // Because long-polling requests require Kafka consumer to complete
  logger.warn('Graceful shutdown is complete', { ctx: 'GracefulShutdown' });
};

module.exports = gracefulShutdown;
