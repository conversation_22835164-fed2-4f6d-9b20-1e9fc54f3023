require('dotenv').config();
const {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} = require('@aws-sdk/client-s3');
const path = require('path');

const { S3_ACCESS_KEY_ID, S3_SECRET_ACCESS_KEY } = require('../configs');

const getS3AccessKey = (bucketName) => {
  if (AWS_S3_ACCESS[bucketName]) {
    const { s3AccessKeyId, s3SecretAccessKey } = AWS_S3_ACCESS[bucketName];
    return { s3AccessKeyId, s3SecretAccessKey };
  }
  return {
    s3AccessKeyId: S3_ACCESS_KEY_ID,
    s3SecretAccessKey: S3_SECRET_ACCESS_KEY,
  };
};

// Create an Amazon S3 service client object.
const createS3Client = (awsZoneSynthesis, s3AccessKeyId, s3SecretAccessKey) => {
  const s3Client = new S3Client({
    region: awsZoneSynthesis,
    credentials: {
      accessKeyId: s3AccessKeyId,
      secretAccessKey: s3SecretAccessKey,
    },
  });
  return s3Client;
};

const renameS3File = async ({
  bucketName,
  oldKey,
  newKey,
  awsZoneSynthesis,
}) => {
  const copyParams = {
    Bucket: bucketName,
    CopySource: `${bucketName}/${oldKey}`,
    Key: newKey,
    MetadataDirective: 'COPY',
    TaggingDirective: 'COPY',
  };

  const { s3AccessKeyId, s3SecretAccessKey } = getS3AccessKey(bucketName);
  const s3Client = createS3Client(
    awsZoneSynthesis,
    s3AccessKeyId,
    s3SecretAccessKey,
  );
  try {
    await s3Client.send(new CopyObjectCommand(copyParams));
    await s3Client.send(
      new DeleteObjectCommand({ Bucket: bucketName, Key: oldKey }),
    );
    const newUrl = `https://${bucketName}.s3.${awsZoneSynthesis}.amazonaws.com/${newKey}`;
    return newUrl;
  } catch (err) {
    logger.error(err, {
      ctx: 'RenameD3File',
      bucketName,
      oldKey,
      newKey,
      awsZoneSynthesis,
    });

    return null;
  }
};

const getKeyFromS3Url = (s3Url) => {
  const url = new URL(s3Url);
  const key = url.pathname.substring(1);
  return key;
};

// ADVISE: use shared-lib/S3Repository.upload() (which use latest AWS API)
const copyToS3 = async ({ bucket, key, url, awsZoneSynthesis } = {}) => {
  const sourceBucket = url.split('.')[0].replace(/^https?:\/\//, '');
  const sourceKey = path.basename(url);
  const copySource = `/${sourceBucket}/${sourceKey}`;
  key = key || sourceKey;

  const command = new CopyObjectCommand({
    Bucket: bucket,
    CopySource: copySource,
    Key: key,
  });

  const { s3AccessKeyId, s3SecretAccessKey } = getS3AccessKey(bucket);
  const s3Client = createS3Client(
    awsZoneSynthesis,
    s3AccessKeyId,
    s3SecretAccessKey,
  );

  const data = await s3Client.send(command);
  if (data.$metadata && data.$metadata.httpStatusCode === 200) {
    return `https://${bucket}.s3.${awsZoneSynthesis}.amazonaws.com/${key}`;
  }
  throw new Error(
    'Copy s3 has error with http status code:',
    data.$metadata.httpStatusCode,
  );
};

module.exports = { renameS3File, getKeyFromS3Url, copyToS3 };
