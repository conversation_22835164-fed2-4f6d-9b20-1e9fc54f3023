const CommunityVoiceStats = require('../models/communityVoiceStats');

const getUserVoiceStatsPipeline = () => [
  {
    $group: {
      _id: null,
      totalUsers: { $addToSet: '$userIds' },
      totalCredits: { $sum: '$totalCredits' },
      totalCharacters: { $sum: '$totalCharacters' },
      totalRequests: { $sum: '$totalRequests' },
      totalMoneyVnd: {
        $sum: {
          $multiply: ['$totalCredits', '$revenueShareRate.vndPerCredit'],
        },
      },
      totalMoneyUsd: {
        $sum: {
          $multiply: ['$totalCredits', '$revenueShareRate.usdPerCredit'],
        },
      },
    },
  },
  {
    $project: {
      _id: 0,
      totalUsers: {
        $size: {
          $reduce: {
            input: '$totalUsers',
            initialValue: [],
            in: { $setUnion: ['$$value', '$$this'] },
          },
        },
      },
      totalCredits: 1,
      totalCharacters: 1,
      totalRequests: 1,
      totalMoneyVnd: { $round: ['$totalMoneyVnd', 0] },
      totalMoneyUsd: 1,
    },
  },
];

const getCommunityVoiceStatsPipeline = () => [
  {
    $group: {
      _id: '$voiceCode',
      totalUsers: { $addToSet: '$userIds' },
      totalCredits: { $sum: '$totalCredits' },
      totalCharacters: { $sum: '$totalCharacters' },
      totalRequests: { $sum: '$totalRequests' },
      totalMoneyVnd: {
        $sum: {
          $multiply: ['$totalCredits', '$revenueShareRate.vndPerCredit'],
        },
      },
      totalMoneyUsd: {
        $sum: {
          $multiply: ['$totalCredits', '$revenueShareRate.usdPerCredit'],
        },
      },
    },
  },
  {
    $project: {
      _id: 0,
      voiceCode: '$_id',
      totalUsers: {
        $size: {
          $reduce: {
            input: '$totalUsers',
            initialValue: [],
            in: { $setUnion: ['$$value', '$$this'] },
          },
        },
      },
      totalCredits: 1,
      totalCharacters: 1,
      totalRequests: 1,
      totalMoneyVnd: { $round: ['$totalMoneyVnd', 0] },
      totalMoneyUsd: 1,
    },
  },
];

const getAggregateUserVoiceStats = async (matchQuery) => {
  const pipeline = [
    {
      $match: matchQuery,
    },
    {
      $facet: {
        summary: getUserVoiceStatsPipeline(),
        communityVoices: getCommunityVoiceStatsPipeline(),
      },
    },
    {
      $project: {
        summary: { $arrayElemAt: ['$summary', 0] },
        communityVoices: 1,
      },
    },
  ];
  const [result] = await CommunityVoiceStats.aggregate(pipeline);
  return result;
};

module.exports = { getAggregateUserVoiceStats };
