const getGlobalVariable = async (req, res) => {
  return res.send({
    IAM_PUBLIC_KEY,
    REQUESTS: REQUESTS ? Object.keys(REQUESTS).length : null,
    CONNECTIONS: CONNECTIONS ? Object.keys(CONNECTIONS).length : null,
    REQUEST_DIRECT: REQUEST_DIRECT ? Object.keys(REQUEST_DIRECT).length : null,
    VOICES: VOICES ? VOICES.length : null,
    LANGUAGES: LANGUAGES ? LANGUAGES.length : null,
    BLACK_WORDS: BLACK_WORDS ? BLACK_WORDS.length : null,
  });
};

module.exports = {
  getGlobalVariable,
};
