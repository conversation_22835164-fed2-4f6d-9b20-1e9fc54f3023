require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const VBEE_SAMPLE_RATES = [8000, 16000, 22050, 32000, 44100, 48000];
const VBEE_DEFAULT_SAMPLE_RATE = {
  'hn_female_ngochuyen_full_48k-fhg': 44100,
  'sg_male_trungkien_vdts_48k-fhg': 22050,
  'hn_female_maiphuong_vdts_48k-fhg': 22050,
  'sg_female_lantrinh_vdts_48k-fhg': 22050,
  'sg_male_minhhoang_full_48k-fhg': 22050,
  'hue_female_huonggiang_full_48k-fhg': 22050,
  'hn_male_manhdung_news_48k-fhg': 44100,
  'sg_female_thaotrinh_full_48k-fhg': 22050,
  'sg_female_thaotrinh_full_44k-phg': 44100,
  'hn_male_thanhlong_talk_48k-fhg': 22050,
  'hn_male_phuthang_news65dt_44k-fhg': 44100,
  'hue_male_duyphuong_full_48k-fhg': 22050,
};

const updateSampleRate = async (voiceCode, defaultSampleRate) => {
  try {
    const sampleRates = VBEE_SAMPLE_RATES.filter(
      (rate) => rate <= defaultSampleRate,
    );
    await Voice.findOneAndUpdate(
      { code: voiceCode },
      { sampleRates, defaultSampleRate },
    );
  } catch (error) {
    logger.error(`Update sample rate of ${voiceCode} failure`, {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  for (const [voiceCode, defaultSampleRate] of Object.entries(
    VBEE_DEFAULT_SAMPLE_RATE,
  )) {
    await updateSampleRate(voiceCode, defaultSampleRate);
    const voices = await Voice.find({});
    global.VOICES = voices;
    logger.info(`Update sample rate of ${voiceCode} successfully`, {
      ctx: 'RunScript',
    });
  }
  process.exit(1);
})();
