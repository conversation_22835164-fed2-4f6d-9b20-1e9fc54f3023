require('dotenv').config();
const moment = require('moment');
require('../../models');
const logger = require('../../utils/logger');
const MoEngageAdapter = require('../../adapters/moengage');
const { scanAndHandleByBatch } = require('../../services/batcher');
const requestDao = require('../../daos/request');
const { scheduleLoadFeaturesRealtime } = require('../../services/growthbook');

global.logger = logger;

const BATCH_SIZE = 1000;

const getRequestsQuery = ({ dateRange, dateField, excludeDocIds = [] }) => {
  const { start, end } = dateRange;
  return {
    _id: { $nin: excludeDocIds },
    [dateField]: { $gte: start, $lte: end },
  };
};

const syncRequestsByDateRange = async (startDate, endDate) => {
  while (startDate <= endDate) {
    logger.info(
      `Start sync make request events to MoEngage for date: ${startDate}`,
    );

    const uniqueUsers = new Map();

    const syncMakeRequestEvents = async (requests) => {
      const syncRequests = [];
      requests.forEach((request) => {
        if (uniqueUsers.has(request.userId)) return;
        syncRequests.push(request);
        uniqueUsers.set(request.userId);
      });

      await Promise.all(
        syncRequests.map((request) =>
          MoEngageAdapter.sendEventCreateRequest({
            customerId: request.userId,
            request,
            timestamp: request.createdAt,
          }),
        ),
      );
    };

    const startOfDate = moment(startDate).startOf('day').toDate();
    const endOfDate = moment(startDate).endOf('day').toDate();

    await scanAndHandleByBatch({
      dateField: 'createdAt',
      dateRange: {
        start: startOfDate,
        end: endOfDate,
      },
      batchSize: BATCH_SIZE,
      getQueryFn: getRequestsQuery,
      countDocumentsFn: requestDao.countRequests,
      getDocumentsFn: requestDao.getRequests,
      handleDocumentsFn: syncMakeRequestEvents,
    });

    startDate.setDate(startDate.getDate() + 1);
  }
};

const main = async (startDate, endDate) => {
  scheduleLoadFeaturesRealtime()
    .then(async () => {
      await syncRequestsByDateRange(startDate, endDate);
      logger.info(`Completed sync make request events to MoEngage`);
    })
    .catch((err) => logger.error(err))
    .finally(() => process.exit(1));
};

main(
  new Date('2024-09-20T00:00:00.000+07:00'),
  new Date('2024-09-20T23:59:59.999+07:00'),
);
