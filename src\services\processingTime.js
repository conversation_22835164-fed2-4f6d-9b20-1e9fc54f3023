const { TTS_PROCESSING_STEPS, START_STEP_NAME } = require('../constants');

const ProcessingTimeCaching = require('../caching/processingTimeCaching');
const processingTimeDao = require('../daos/processingTime');

const calcAvgProcessingTime = (processingTime, characters) => {
  const avgTime = Math.round((processingTime / (characters || 1)) * 1000);
  return avgTime || 0;
};

const saveProcessingTime = async ({
  requestId,
  startTime = Date.now(),
  status,
}) => {
  const isSaved = await ProcessingTimeCaching.checkSaveProcessingTime(
    requestId,
  );
  if (isSaved) return;

  const endTime = Date.now();
  const totalProcessingTime = endTime - startTime;

  const processingTime = await ProcessingTimeCaching.getProcessingTime(
    requestId,
  );
  const {
    userId,
    preProcessingTime,
    queueTime,
    sentenceTokenizerTime,
    synthesisTime,
    audioJoinerTime,
    characters,
  } = processingTime;

  processingTimeDao.createProcessingTime({
    _id: requestId,
    userId,
    characters,
    preProcessingTime,
    queueTime,
    sentenceTokenizerTime,
    synthesisTime,
    audioJoinerTime,
    avgTotalTime: calcAvgProcessingTime(totalProcessingTime, characters),
    totalTime: totalProcessingTime,
    status,
  });

  ProcessingTimeCaching.deleteProcessingTime(requestId);
};

const getCurrentStep = (processingTime = {}) => {
  const ttsProcessingSteps = Object.values(TTS_PROCESSING_STEPS);
  const stepTimeFieldNames = Object.values(TTS_PROCESSING_STEPS).map(
    (step) => `${step}Time`,
  );
  const originStepNames = [...stepTimeFieldNames];

  const lastStepProcessed =
    stepTimeFieldNames
      .reverse()
      .find((step) =>
        Object.prototype.hasOwnProperty.call(processingTime, step),
      ) || null;

  const stepProcessedIndex = originStepNames.indexOf(lastStepProcessed);
  const currentStep = ttsProcessingSteps[stepProcessedIndex + 1];

  return currentStep;
};

const getStartTimeFromStep = (processingTime, step) => {
  const startTime = processingTime[START_STEP_NAME[step]];
  return startTime;
};

module.exports = {
  saveProcessingTime,
  getCurrentStep,
  getStartTimeFromStep,
};
