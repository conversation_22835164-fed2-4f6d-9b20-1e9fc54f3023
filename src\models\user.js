const mongoose = require('mongoose');
require('mongoose-long')(mongoose);

const { Long } = mongoose.Types;
const { SPEED_CONVERT } = require('../constants');

const userSchema = new mongoose.Schema(
  {
    _id: String,
    remainingCharacters: { type: Long, default: 0 },
    bonusCharacters: { type: Long, default: 0 },
    lockCharacters: { type: Long, default: 0 },
    packageCode: String,
    packageStartDate: Date,
    packageExpiryDate: Date,
    maxLengthInputText: Number,
    maxLengthDemoInput: Number,
    speedConvert: { type: String, enum: Object.values(SPEED_CONVERT) },
    /** max concurrent requests allowed for this user */
    concurrentRequest: Number,
    download: Number,
    latestDownloadedAt: Date,
    retentionPeriod: Number,
    features: [String],
    maxPreview: Number,
    usedFreePreviews: {
      default: 0,
      type: Number,
    },
    maxInstantVoiceSynthesis: Number,
    usedFreeInstantVoiceSynthesis: {
      default: 0,
      type: Number,
    },
    isMigrated: { type: Boolean, default: false },
    isBlock: { type: Boolean, default: false },
    lockedCharactersAt: Date,
    overwriteAt: Date,
    isSyncDictionary: { type: Boolean, default: false },
    apiPackage: {
      packageCode: String,
      packageExpiryDate: Date,
      maxLengthInputText: Number,
      speedConvert: { type: String, enum: Object.values(SPEED_CONVERT) },
      concurrentRequest: Number,
      download: Number,
      retentionPeriod: Number,
      features: [String],
      price: Number,
    },
    apiCharacters: {
      remainingCharacters: { type: Long, default: 0 },
      bonusCharacters: { type: Long, default: 0 },
      lockCharacters: { type: Long, default: 0 },
      lockedCharactersAt: Date,
    },
    dubbing: {
      packageCode: String,
      packageExpiryDate: Date,
      retentionPeriod: Number,
      concurrentRequest: Number,
      features: [String],
      remainingSeconds: { type: Long, default: 0 },
      lockSeconds: { type: Long, default: 0 },
      download: Number,
      price: Number,
      lockedSecondsAt: Date,
      latestDownloadedAt: Date,
    },
    stt: {
      packageCode: String,
      packageExpiryDate: Date,
      retentionPeriod: Number,
      concurrentRequest: Number,
      features: [String],
      remainingSeconds: { type: Long, default: 0 },
      lockSeconds: { type: Long, default: 0 },
      download: Number,
      price: Number,
      lockedSecondsAt: Date,
      latestDownloadedAt: Date,
      maxDurationInput: Number,
    },
    isSendWarning: {
      apiCharactersRunningOut: { type: Boolean, default: false },
      apiCharactersReachedZero: { type: Boolean, default: false },
    },
    recaptcha: {
      totalReq: Number,
      totalInvalidReq: Number,
      totalBrowserErrorReq: Number,
      numOfConsecutiveBrowserErrorReq: Number,
    },
    firstConvertAt: Date,
    lastResetToZeroAt: Date,
    lastResetToDefaultAt: {
      type: Date,
      // This field must be set to current date when user is created
      // to prevent referral bonus characters are reset to zero immediately
      default: () => new Date(),
    },
    studio: {
      oneTime: {
        packageCode: String,
        eolDate: Date,
        packageExpiryDate: Date,
        capturedReferralCredits: { type: Long, default: 0 },
      },
      cycle: {
        nextResetAt: Date,
        remainingCredits: { type: Long, default: 0 },
        creditsPerCycle: { type: Long, default: 0 },
        purchasedTopUpCredits: { type: Long, default: 0 },
        period: Number,
        isLocked: { type: Boolean, default: false },
        capturedRolloverCredits: { type: Long, default: 0 },
        capturedReferralCredits: { type: Long, default: 0 },
      },
      topUp: {
        remainingCredits: { type: Long, default: 0 },
        isLocked: { type: Boolean, default: false },
        capturedRolloverCredits: { type: Long, default: 0 },
      },
      custom: {
        remainingCredits: { type: Long, default: 0 },
        isLocked: { type: Boolean, default: false },
        capturedReferralCredits: { type: Long, default: 0 },
      },
    },
    refPackageCode: String,
    shareVoiceProfile: {
      userSlug: { type: String, unique: true, sparse: true },
      displayName: { type: String },
      bio: { type: String },
    },
    hasEverHadCommunityVoice: { type: Boolean, default: false },
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model('User', userSchema);
