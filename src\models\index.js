const mongoose = require('mongoose');
const logger = require('../utils/logger');
const { MONGO_URI } = require('../configs');

const initMongoDB = async () => {
  const mongooseInstance = mongoose.set('strictQuery', true);
  await mongooseInstance.connect(MONGO_URI || '', { autoIndex: false });

  mongooseInstance.connection
    .on('error', (err) => {
      logger.error(`Connect error to MongoDB: ${MONGO_URI}`, {
        ctx: 'MongoDB',
        stack: err.stack,
      });
      process.exit();
    })
    .once('open', () => {
      logger.info(`Connected to MongoDB: ${MONGO_URI}`, { ctx: 'MongoDB' });
    });

  logger.info('MongoDB: Init successfully,', { ctx: 'MongoDB' });

  return mongooseInstance;
};

module.exports = { initMongoDB };
