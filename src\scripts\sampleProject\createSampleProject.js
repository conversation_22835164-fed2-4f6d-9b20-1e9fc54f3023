require('dotenv').config();
const dataSampleProject = require('./dataSampleProject.json');
const sampleProjectDao = require('../../daos/sampleProject');
const logger = require('../../utils/logger');
require('../../models');

global.logger = logger;

const createSampleProject = async () => {
  logger.info('Starting create sample project...', { ctx: 'RunScript' });
  await sampleProjectDao.createSampleProject(dataSampleProject);
  logger.info('Create sample project successfully', { ctx: 'RunScript' });
};

(async () => {
  await createSampleProject();
  process.exit(1);
})();
