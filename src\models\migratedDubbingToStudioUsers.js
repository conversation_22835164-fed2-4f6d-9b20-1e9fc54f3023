const mongoose = require('mongoose');

const migratedDubbingToStudioUserSchema = new mongoose.Schema(
  {
    userId: String,
    oldRemainingCharacters: Number,
    oldLockCharacters: Number,
    oldRemainingSeconds: Number,
    oldLockSeconds: Number,
    hasAgreedToMigrate: Boolean,
    agreedToMigrateAt: Date,
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model(
  'MigratedDubbingToStudioUser',
  migratedDubbingToStudioUserSchema,
  'migrate_dubbing_to_studio_users',
);
