const { findLanguages } = require('./language');
const Voice = require('../models/voice');
const daoUtils = require('./utils');

const createVoices = async (voices) => {
  const voiceIds = voices.map((voice) => voice.code);
  const existingVoices = await Voice.find({ code: { $in: voiceIds } });
  const existingVoiceCodes = existingVoices.map((voice) => voice.code);
  if (existingVoices.length > 0) {
    throw new Error(`Voice already exists:\n${existingVoiceCodes}`);
  }

  const newVoices = await Voice.insertMany(voices, { lean: true });

  return newVoices;
};

// ADVISE: duplicated code with dao/voiceCloning Duplicate code: lines 60
const findVoices = async (query = {}) => {
  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset,
    limit,
    fields,
    sort = ['rank_asc'],
  } = query;

  let dataQuery = {};
  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };
    if (gender) dataQuery.gender = { $in: gender.split(',') };
    if (languageCode) dataQuery.languageCode = { $in: languageCode.split(',') };
    if (level) dataQuery.level = { $in: level.split(',') };
    if (features)
      dataQuery.features = { $elemMatch: { $in: features.split(',') } };
  }

  const { documents: voices, total } = await daoUtils.find(Voice, {
    search,
    searchFields,
    query: dataQuery,
    offset,
    limit,
    fields,
    sort,
  });

  // ADVISE: IMPORTANT: get rid of global.LANGUAGES here. For isolation, Voices must not init and setup Languages
  if (!LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  const detailVoices = voices.map((voice) => {
    const language = LANGUAGES.find((item) => item.code === voice.languageCode);
    return { ...voice, language };
  });

  return { voices: detailVoices, total };
};

const updateVoice = async (voiceId, updateFields) => {
  const voice = await Voice.findByIdAndUpdate(voiceId, updateFields, {
    new: true,
    omitUndefined: true,
  }).lean();

  return voice;
};

module.exports = { createVoices, findVoices, updateVoice };
