const { DATASENSES_KEY, DATASENSES_URL } = require('../configs');
const {
  DATASENSES_EVENTS,
  DATASENSES_CLIENT_ID_DEFAULT,
} = require('../constants');
const { FEATURE_KEYS } = require('../constants/featureKeys');
const callApi = require('../utils/callApi');
const { getFeatureValue } = require('./growthbook');
const requestDao = require('../daos/request');

const sendDataToDataSenses = async ({
  requiredFields,
  eventName,
  eventProperties,
}) => {
  const data = {
    event_name: eventName,
    event_properties: eventProperties,
    client_id: requiredFields.clientId || DATASENSES_CLIENT_ID_DEFAULT,
    user_agent: requiredFields.userAgent,
    platform: requiredFields.platform || 'backend',
    referrer: requiredFields.referrer,
    sdk_version: requiredFields.sdkVersion,
    unix_timestamp: requiredFields.unixTimestamp || Date.now().toString(),
    app_instance_id: requiredFields.clientId || DATASENSES_CLIENT_ID_DEFAULT,
  };

  const response = await callApi({
    method: 'POST',
    url: `${DATASENSES_URL}/v1/event/single`,
    headers: {
      'Content-Type': 'application/json',
      'sdk-dev-key': DATASENSES_KEY,
    },
    data,
  });

  logger.info(response, { ctx: 'DataSenses', data });

  return response;
};

const getRequestProperties = (request) => {
  return {
    customer_id: request.userId,
    request_type: request.type,
    voice: request.voiceCode,
    characters: request.characters,
    seconds: request.seconds,
    status: request.status,
  };
};

const sendRequestToDataSenses = async (request) => {
  try {
    const dataSenses = getFeatureValue(FEATURE_KEYS.DATASENSES);
    const allowSendEventDatasenses = dataSenses?.s2s;
    if (!allowSendEventDatasenses) return;

    const eventProperties = getRequestProperties(request);

    const response = await sendDataToDataSenses({
      requiredFields: request.datasenses || {},
      eventName: request.demo
        ? DATASENSES_EVENTS.UPDATE_PREVIEW_STATUS
        : DATASENSES_EVENTS.UPDATE_MAKE_REQUEST,
      eventProperties,
    });

    if (response.result !== 'success')
      throw new Error(`Send paid order to DataSenses failed: ${response}`);

    // Remove datasenses field after sending event to DataSenses
    await requestDao.deleteDatasenses(request._id);
  } catch (error) {
    logger.error(error, {
      ctx: 'SendFreeOrderToDataSenses',
      errorMessage: error.message,
    });
  }
};

const sendEventDownloadToDataSenses = async (request, datasenses) => {
  try {
    const dataSenses = getFeatureValue(FEATURE_KEYS.DATASENSES);
    const allowSendEventDatasenses = dataSenses?.s2s;
    if (!allowSendEventDatasenses) return;

    const eventProperties = {
      customer_id: request.userId,
      request_type: request.type,
      voice: request.voice?.code,
    };

    const response = await sendDataToDataSenses({
      requiredFields: datasenses || {},
      eventName: DATASENSES_EVENTS.DOWNLOAD,
      eventProperties,
    });

    if (response.result !== 'success')
      throw new Error(`Send paid order to DataSenses failed: ${response}`);

    // Remove datasenses field after sending event to DataSenses
    await requestDao.deleteDatasenses(request._id);
  } catch (error) {
    logger.error(error, {
      ctx: 'SendFreeOrderToDataSenses',
      errorMessage: error.message,
    });
  }
};

module.exports = {
  sendDataToDataSenses,
  sendRequestToDataSenses,
  sendEventDownloadToDataSenses,
};
