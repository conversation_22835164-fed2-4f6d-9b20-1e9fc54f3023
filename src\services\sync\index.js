const { SYNC_ENTERPRISE_SERVICE_URLS } = require('../../configs');
const callApi = require('../../utils/callApi');
const { getAccessToken } = require('../iam');
const logger = require('../../utils/logger');

/** post app info to other services, to notify app changes? */
const syncApp = async (app) => {
  const accessToken = await getAccessToken();

  for (const url of SYNC_ENTERPRISE_SERVICE_URLS) {
    try {
      await callApi({
        method: 'POST',
        url: `${url}/api/v1/admin/apps/sync`,
        headers: { Authorization: `Bearer ${accessToken}` },
        data: app,
      });
    } catch (error) {
      logger.error(`Sync app ${app._id} to ${url} failed`, {
        ctx: 'SyncApp',
        stack: error.stack,
      });
    }
  }
};

module.exports = { syncApp };
