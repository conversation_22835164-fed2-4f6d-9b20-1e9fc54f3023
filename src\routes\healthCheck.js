const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const healthCheckController = require('../controllers/healthCheck');

/* eslint-disable prettier/prettier */
router.get('/probe/startup', asyncMiddleware(healthCheckController.checkStartup));
router.get('/probe/readiness', asyncMiddleware(healthCheckController.checkReadiness));
router.get('/probe/liveness', asyncMiddleware(healthCheckController.checkLiveness));
/* eslint-disable prettier/prettier */

module.exports = router;
