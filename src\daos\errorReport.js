const daoUtils = require('./utils');
const ErrorReport = require('../models/errorReport');
const {
  getSearchQuery,
  getSortQuery,
  getSelectQuery,
  getDateQuery,
} = require('./utils/util');

const createErrorReport = async (createFields) => {
  const errorReport = await ErrorReport.create(createFields);
  return errorReport;
};

const findErrorReports = async ({
  search,
  searchFields = ['requestId'],
  dateField = 'createdAt',
  query,
  offset,
  limit,
  sort,
  fields,
}) => {
  const s = getSearchQuery(ErrorReport, searchFields, search);

  // eslint-disable-next-line prefer-const
  let { startDate, endDate, ...dataQuery } = query || {};
  if (startDate || endDate) {
    const dateQuery = getDateQuery(dateField, startDate, endDate);
    dataQuery = { ...dataQuery, ...dateQuery };
  }

  const total = await ErrorReport.countDocuments(
    search ? { $or: s, ...dataQuery } : dataQuery,
  );

  if (!total) return { errorReports: [], total: 0 };

  const pipeline = [
    { $match: search ? { $or: s, ...dataQuery } : dataQuery },
    { $sort: getSortQuery(sort) },
    { $skip: offset || 0 },
  ];
  if (limit) pipeline.push({ $limit: limit });
  pipeline.push(
    ...[
      {
        $lookup: {
          from: 'requests',
          localField: 'requestId',
          foreignField: '_id',
          as: 'request',
        },
      },
      { $unwind: '$request' },
      {
        $lookup: {
          from: 'voices',
          localField: 'request.voiceCode',
          foreignField: 'code',
          as: 'request.voice',
        },
      },
      { $unwind: '$request.voice' },
      { $project: { requestId: 0 } },
    ],
  );
  if (fields) pipeline.push({ $project: getSelectQuery(fields) });

  const errorReports = await ErrorReport.aggregate(pipeline);
  return { errorReports, total };
};

const findErrorReport = async (condition) => {
  const errorReport = await daoUtils.findOne(ErrorReport, condition);
  return errorReport;
};

module.exports = {
  createErrorReport,
  findErrorReports,
  findErrorReport,
};
