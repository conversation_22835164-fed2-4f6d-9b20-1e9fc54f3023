const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const systemController = require('../controllers/system');
const { auth, hasRole } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.get('/api/global', auth, hasRole('admin'), asyncMiddleware(systemController.getGlobalVariable));
/* eslint-disable prettier/prettier */

module.exports = router;
