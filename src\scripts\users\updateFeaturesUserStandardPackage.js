require('dotenv').config();
require('../../models');

const User = require('../../models/user');
const logger = require('../../utils/logger');

global.logger = logger;

const STANDARD_PACKAGE_CODES = [
  'STUDIO-STANDARD-MONTH',
  'STUDIO-STANDARD-QUARTERLY',
  'STUDIO-STANDARD-YEARLY',
];

const updateFeaturesUserStandardPackage = async () => {
  logger.info(`Starting update user use standard package`, {
    ctx: 'UpdateFeaturesUserStandardPackage',
  });

  // Remove premium voice feature
  await User.updateMany(
    { packageCode: { $in: STANDARD_PACKAGE_CODES } },
    { $pull: { features: { $in: ['pro-vietnam-voice', 'global-voice'] } } },
  );

  // Add standard voice feature
  await User.updateMany(
    { packageCode: { $in: STANDARD_PACKAGE_CODES } },
    { $push: { features: 'standard-global-voice' } },
  );

  logger.info(`Updated user use standard package`, {
    ctx: 'UpdateFeaturesUserStandardPackage',
  });
};

(async () => {
  await updateFeaturesUserStandardPackage();
  process.exit(1);
})();
