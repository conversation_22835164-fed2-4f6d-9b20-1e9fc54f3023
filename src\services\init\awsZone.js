const { PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY } = require('../../configs');
const { REQUEST_TYPE } = require('../../constants');
const { FEATURE_KEYS } = require('../../constants/featureKeys');
const { getAwsZones } = require('../../daos/awsZone');
const { decryptAccessKey } = require('../../scripts/awsZone/encryptAccessKey');
const { getFeatureValue } = require('../growthbook');
const logger = require('../../utils/logger');

const createArrayFollowWeight = (awsZones) => {
  const arrayFollowWeight = [];
  awsZones.forEach(({ region, weight }) => {
    for (let i = 0; i < weight; i += 1) {
      arrayFollowWeight.push(region);
    }
  });
  return arrayFollowWeight;
};

/**
 * Get aws zone for synthesis request.
 * with this `userid`, we need to call which lambda func (in which zone) of TTSCore
 */
const getAwsZoneSynthesis = () => {
  const lengthAwsZones = AWS_ZONES_TTS_STUDIO.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_STUDIO[randomIndex];
  return awsZone;
};

const initTtsCachingAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.API_CACHING),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_CACHING = arrayFollowWeight;

  logger.info('TTSCaching: Init successfully', {
    ctx: 'AwsZone.TTSCaching',
  });
};

const initTtsStudioAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.STUDIO),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_STUDIO = arrayFollowWeight;

  logger.info('TTSStudio: Init successfully', {
    ctx: 'AwsZone.TTSStudio',
  });
};

const initS3AccessMapping = async () => {
  const shouldDecryptS3Key = getFeatureValue(FEATURE_KEYS.DECRYPT_S3_KEY);
  if (!shouldDecryptS3Key) {
    return;
  }

  const awsZones = (await getAwsZones()) || [];
  for (const awsZone of awsZones) {
    if (awsZone.s3Buckets && Object.values(awsZone.s3Buckets).length > 0) {
      // s3 bucket name is globally unique among all AWS accounts and regions
      const privateKey = PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY;
      const s3AccessKeyId = decryptAccessKey({
        encryptedAccessKey: awsZone.s3AccessKeyId,
        privateKey,
      });
      const s3SecretAccessKey = decryptAccessKey({
        encryptedAccessKey: awsZone.s3SecretAccessKey,
        privateKey,
      });
      Object.values(awsZone.s3Buckets).forEach((bucketName) => {
        AWS_S3_ACCESS[bucketName] = {
          s3AccessKeyId,
          s3SecretAccessKey,
        };
      });
    }
  }

  logger.info('S3Access: Init successfully', {
    ctx: 'AwsZone.s3Buckets',
  });
};

module.exports = {
  getAwsZoneSynthesis,

  initTtsCachingAwsZone,
  initTtsStudioAwsZone,
  initS3AccessMapping,
};
