const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const requestController = require('../controllers/request');
const errorReportController = require('../controllers/errorReport');
const { auth, hasRole } = require('../middlewares/auth');
const { createErrorReportValidate } = require('../validations/errorReport');
const {
  updateParagraphsValidate,
  updateManyRequestByIdsValidate,
  updateRequestByIdValidate,
  removePendingRequests,
} = require('../validations/request');

/* eslint-disable prettier/prettier */
router.get('/requests', auth, asyncMiddleware(requestController.getRequests));
router.get('/requests/count-pending-inprogress-req', auth, hasRole('manage-request-queue'), asyncMiddleware(requestController.countPendingAndInProgressReq));
router.put('/requests/count-pending-inprogress-req', auth, hasRole('manage-request-queue'), asyncMiddleware(requestController.setPendingAndInProgressReqCount));
router.get('/requests/count-pending-req', auth, hasRole('manage-request-queue'), asyncMiddleware(requestController.countPendingRequests));
router.delete('/requests/remove-pending-req', auth, hasRole('manage-request-queue'), removePendingRequests, asyncMiddleware(requestController.removePendingRequests));

router.get('/requests/:requestId', auth, asyncMiddleware(requestController.getRequest));
router.get('/requests/:requestId/progress', auth, asyncMiddleware(requestController.getProgressRequest));
router.get('/requests/:requestId/audio', auth, asyncMiddleware(requestController.getAudio));
router.get('/requests/:requestId/audio-download-url', auth, asyncMiddleware(requestController.getAudioDownloadUrl));
router.post('/requests/:requestId/error-report', auth, createErrorReportValidate, asyncMiddleware(errorReportController.createErrorReport));
router.put('/requests/:requestId/paragraphs', auth, updateParagraphsValidate, asyncMiddleware(requestController.updateParagraphsOfRequest));
router.put('/requests/delete', auth, updateManyRequestByIdsValidate, asyncMiddleware(requestController.deleteRequests));
router.put('/requests/:requestId', auth, updateRequestByIdValidate, asyncMiddleware(requestController.updateRequest));
/* eslint-disable prettier/prettier */

module.exports = router;
