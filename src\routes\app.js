const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const appController = require('../controllers/app');
const { auth, hasRole } = require('../middlewares/auth');
const { createAppValidate, updateAppValidate } = require('../validations/app');

/* eslint-disable prettier/prettier */
router.post(
  '/apps',
  auth,
  createAppValidate,
  asyncMiddleware(appController.createApp),
);
router.get('/apps', auth, asyncMiddleware(appController.getApps));
router.put(
  '/apps/:appId',
  auth,
  updateAppValidate,
  asyncMiddleware(appController.updateApp),
);
router.get('/apps/:appId', auth, asyncMiddleware(appController.getApp));
router.get(
  '/apps/:appId/requests',
  auth,
  asyncMiddleware(appController.getRequests),
);
router.get(
  '/admin/apps',
  auth,
  hasRole('view-apps'),
  asyncMiddleware(appController.getAppsByAdmin),
);
router.post(
  '/admin/apps/sync',
  auth,
  hasRole('manage-apps'),
  asyncMiddleware(appController.syncApp),
);
/* eslint-disable prettier/prettier */

module.exports = router;
