const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const synthesisController = require('../controllers/synthesis');
const { auth } = require('../middlewares/auth');
const { blockBlackWords } = require('../middlewares/blockBlackWords');
const { checkBlockedUser } = require('../middlewares/checkBlockedUser');
const { checkRecaptcha } = require('../middlewares/recaptcha');
const { synthesisValidate } = require('../validations/synthesis');

router.post(
  '/synthesis',
  auth,
  synthesisValidate,
  checkBlockedUser,
  blockBlackWords,
  checkRecaptcha,
  asyncMiddleware(synthesisController.synthesize),
);

module.exports = router;
