const getEmailDomain = (email) => email?.split('@')[1];

const checkDomainMatched = (domain, rules) => {
  if (!domain) return false;
  const normalizedDomain = domain.toLowerCase();

  for (const rule of rules) {
    const ruleDomain = rule.toLowerCase();

    // Exact match first
    if (normalizedDomain === ruleDomain) return true;

    // Wildcard domain (e.g. *.example.com)
    if (ruleDomain.startsWith('*.')) {
      const baseDomain = ruleDomain.slice(2);
      if (
        normalizedDomain === baseDomain ||
        normalizedDomain.endsWith(`.${baseDomain}`)
      ) {
        return true;
      }
    }
  }

  return false;
};

module.exports = {
  getEmailDomain,
  checkDomainMatched,
};
