const { SYNTHESIS_BY_GATEWAY } = require('../configs');
const ttsService = require('../services/tts');

const getTts = async (req, res) => {
  const { search, searchFields, offset, limit, sort } = req.query;

  const query = {};
  query.query = {};
  if (search) query.search = search;
  // ADVISE: duplicated code with controllers/errorReport Duplicate code: lines 18-30
  if (searchFields) query.searchFields = searchFields.split(',');
  if (offset) query.offset = parseInt(offset, 10);
  if (limit) query.limit = parseInt(limit, 10);
  if (sort) query.sort = sort.split(',');
  Object.keys(req.query)
    .filter(
      (q) => ['search', 'fields', 'offset', 'limit', 'sort'].indexOf(q) === -1,
    )
    .forEach((q) => {
      query.query[q] = ['true', 'false'].includes(req.query[q])
        ? JSON.parse(req.query[q])
        : req.query[q];
    });

  const { tts, total } = await ttsService.getTts(
    SYNTHESIS_BY_GATEWAY ? req.query : query,
  );
  return res.send({ tts, total });
};

const getTtsPresets = async (req, res) => {
  const { userId } = req.user;
  const ttsPresets = await ttsService.getTtsPresets(userId);
  return res.send(ttsPresets);
};

const getTtsPresetById = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  const ttsPreset = await ttsService.getTtsPresetById(userId, ttsPresetId);
  return res.send({ ttsPreset });
};

const createTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { name, audioType, backgroundMusic, speed, voiceCode, clientPause } =
    req.body;
  const ttsPreset = await ttsService.createTtsPreset(userId, {
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  });
  return res.send({ ttsPreset });
};

const updateTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  const { name, audioType, backgroundMusic, speed, voiceCode, clientPause } =
    req.body;
  const ttsPreset = await ttsService.updateTtsPreset(userId, ttsPresetId, {
    name,
    audioType,
    backgroundMusic,
    speed,
    voiceCode,
    clientPause,
  });
  return res.send({ ttsPreset });
};

const deleteTtsPreset = async (req, res) => {
  const { userId } = req.user;
  const { ttsPresetId } = req.params;
  await ttsService.deleteTtsPreset(userId, ttsPresetId);
  return res.send({});
};

module.exports = {
  getTts,
  getTtsPresets,
  getTtsPresetById,
  createTtsPreset,
  updateTtsPreset,
  deleteTtsPreset,
};
