# Nếu code vbee-tts-api trùng với tts-api, thì xoá bên vbee-tts-api

# API version

- hiện tại coi là V4
- **IMPORTANT**: V3, ch<PERSON> <PERSON> check Datadog, xem còn usage ko thì xoá
- tạm thời có thể ẩn route cho V3

# this folder is basically can be refactored to a shared `models` repository

to migrate safely, we import the `models` repository here, reassign the const with value from `models`

after a period of monitoring, we remove constants here (when everything is refer to `models` repository, not to this constants)

Should export an object, to be namespaced the constants, improve readability

```json
const DUBBING_AUDIO_DURATION_LIMIT = 4; // hours
const DUBBING_MAX_SENTENCE_LENGTH = 500; // characters
const DUBBING_SOURCE = {
  YOUTUBE: 'youtube',
  LOCAL: 'local',
  SRT: 'srt',
};
const SUBTITLE_FEATURE = 'subtitle';
module.exports = {
  DUBBING_AUDIO_DURATION_LIMIT,
  DUBBING_MAX_SENTENCE_LENGTH,
  DUBBING_SOURCE,
  SUBTITLE_FEATURE,
};
```

export `dubbing` object.

# ngay khi tạo được TTSRequest thì API lưu đồng thời vào Mongo và Redis

Trong các quá trình trung gian, truy cập từ Redis cho tốc độ nhanh hơn

// ADVISE: BUSINESS: explain caching mechanism of request and sentences
const cacheSentences = sentences.map((sentence) => {
// ADVISE: textSentence is unused?
const { text: textSentence, ...cacheSentence } = sentence;
return cacheSentence;
});

# update deps

## axios

## package-lock

# Should we cache Voice Dto?

- ít thay đổi

  - Với Giọng của bên thứ 3, Microsoft, poly
  - Với giọng Built-in VBee

- Hay thay đổi
  - Riêng với VC, sự thay đổi diễn ra thường xuyên hơn do user (chủ giọng) có nhu cầu thay đổi tên tuổi, ảnh demo ...
  - vẫn có thể cache, nhưng lưu ý refresh cache khi có sự thay đổi

# cyclic references

services/ttsProcessing call services/dubbing
services/synthesis call services/ttsProcessing
[v] REMOVE CALL from services/ttsProcessing => services/synthesis

- services/ttsProcessing

  - services/ws // ADVISE: Circular deps (inline require./synthesis)
  - ./dubbing // ADVISE: Circular deps (services/dubbing.js > services/request.js > services/ttsProcessing.js)
  - ./request // ADVISE: Circular deps (services/request.js > services/ttsProcessing.js)
  - ./apiResponse // ADVISE: Circular deps (services/apiResponse.js > services/request.js > services/ttsProcessing.js)
  - ./queue // ADVISE: Circular deps (services/queue.js > services/synthesis.js > ttsProcessing.js)

  - ./characterProcessing

  - ./secondProcessing
  - ./package
  - ./synthesisComputePlatform
  - ./voiceCloning
  - ./audio

- services/ws -

  - services/ttsProcessing
  - ./SynthesisService
  - ./recaptcha
  - ./package

- services/synthesis.js > services/ttsProcessing.js

✖ Found 11 circular dependencies!

1. services/queue.js > services/synthesis.js > services/dubbing.js
2. services/request.js > services/ttsProcessing.js > services/apiResponse.js
3. services/dubbing.js > services/request.js > services/ttsProcessing.js
4. services/queue.js > services/synthesis.js > services/dubbing.js > services/request.js > services/ttsProcessing.js
5. services/request.js > services/ttsProcessing.js
6. services/synthesis.js > services/dubbing.js > services/request.js > services/ttsProcessing.js > services/ws.js
7. services/queue.js > services/synthesis.js

8. express.js
9. express.js > routes/apiV3.js
10. routes/index.js
11. routes/v2/index.js
