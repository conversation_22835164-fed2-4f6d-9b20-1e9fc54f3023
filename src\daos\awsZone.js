const AwsZone = require('../models/awsZone');

const createAwsZone = async (awsZoneInfo) => {
  const awsZone = await AwsZone.create(awsZoneInfo);
  return awsZone.toJSON();
};

const getAwsZone = async (condition) => {
  const awsZone = await AwsZone.findOne(condition).lean();
  return awsZone;
};

const updateAwsZone = async (condition, updateFields) => {
  const newAwsZone = await AwsZone.updateOne(condition, updateFields, {
    new: true,
    upsert: true,
  }).lean();

  return newAwsZone;
};

const getAwsZones = async (condition) => {
  const awsZones = await AwsZone.find(condition).lean();
  return awsZones;
};

module.exports = {
  createAwsZone,
  getAwsZone,
  getAwsZones,
  updateAwsZone,
};
