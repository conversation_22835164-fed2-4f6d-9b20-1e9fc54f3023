const prometheusClient = require('prom-client');
const { KAFKA_TOPIC, REDIS_KEY_PREFIX } = require('../../constants');
const Caching = require('../../caching');

const incr = (...args) => Caching.GlobalCounter.increase(...args);
const decr = (...args) => Caching.GlobalCounter.decrease(...args);

const getMetrics = async () => prometheusClient.register.metrics();

// #region Counter

const incrTokenizerRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_REQUESTS);

const incrTokenizerInProgressRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_IN_PROGRESS_REQUESTS);

const decrTokenizerInProgressRequests = () =>
  decr(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_IN_PROGRESS_REQUESTS);

const incrTokenizerFailedRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SENTENCE_TOKENIZER_FAILED_REQUESTS);

const incrSynthesisRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_REQUESTS);

const incrSynthesisInProgressRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_IN_PROGRESS_REQUESTS);

const decrSynthesisInProgressRequests = () =>
  decr(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_IN_PROGRESS_REQUESTS);

const incrSynthesisFailedRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_SYNTHESIS_FAILED_REQUESTS);

const incrJoinerRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_REQUESTS);

const incrJoinerInProgressRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_IN_PROGRESS_REQUESTS);

const decrJoinerInProgressRequests = () =>
  decr(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_IN_PROGRESS_REQUESTS);

const incrJoinerFailedRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_AUDIO_JOINER_FAILED_REQUESTS);

// this only call to increase, no decrease
const incrTotalRequests = () => incr(REDIS_KEY_PREFIX.TOTAL_REQUESTS);

const incrTotalInProgressRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_PENDING_AND_INPROGRESS_REQUESTS);

const decrTotalInProgressRequests = () =>
  decr(REDIS_KEY_PREFIX.TOTAL_PENDING_AND_INPROGRESS_REQUESTS);

const incrTotalProcessingRequests = () =>
  incr(REDIS_KEY_PREFIX.TOTAL_INPROGRESS_REQUESTS);

const decrTotalProcessingRequests = () =>
  decr(REDIS_KEY_PREFIX.TOTAL_INPROGRESS_REQUESTS);

// #endregion

const calculateMetricBasedOnKafkaTopic = (kafkaTopic) => {
  switch (kafkaTopic) {
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_REQUEST: {
      incrTokenizerRequests();
      incrTokenizerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS: {
      decrTokenizerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE: {
      decrTokenizerInProgressRequests();
      incrTokenizerFailedRequests();
      break;
    }

    case KAFKA_TOPIC.SYNTHESIS_REQUEST: {
      incrSynthesisRequests();
      incrSynthesisInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SYNTHESIS_SUCCESS: {
      decrSynthesisInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.SYNTHESIS_FAILURE: {
      decrSynthesisInProgressRequests();
      incrSynthesisFailedRequests();
      break;
    }

    case KAFKA_TOPIC.JOIN_AUDIOS_REQUEST: {
      incrJoinerRequests();
      incrJoinerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS: {
      decrJoinerInProgressRequests();
      break;
    }
    case KAFKA_TOPIC.JOIN_AUDIOS_FAILURE: {
      decrJoinerInProgressRequests();
      incrJoinerFailedRequests();
      break;
    }

    default:
      break;
  }
};

module.exports = {
  getMetrics,
  incrTotalRequests,
  incrTotalInProgressRequests,
  decrTotalInProgressRequests,
  incrTotalProcessingRequests,
  decrTotalProcessingRequests,
  calculateMetricBasedOnKafkaTopic,
};
