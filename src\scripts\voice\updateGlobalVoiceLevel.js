require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER, VOICE_LEVEL } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update voice level...`, { ctx: 'RunScript' });

  await Voice.updateMany(
    { provider: { $ne: VOICE_PROVIDER.VBEE } },
    { $addToSet: { features: 'global-voice' } },
  );

  await Voice.updateMany(
    { provider: { $ne: VOICE_PROVIDER.VBEE }, type: 'Neural TTS' },
    { level: VOICE_LEVEL.PRO },
  );

  await Voice.updateMany(
    { provider: { $ne: VOICE_PROVIDER.VBEE }, type: 'Standard TTS' },
    { level: VOICE_LEVEL.BASIC },
  );

  const voices = await Voice.find({});
  global.VOICES = voices;

  logger.info(`Update voice level successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
