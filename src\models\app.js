const mongoose = require('mongoose');
const { SERVER_ENV } = require('../configs');
const { APP_ROLE } = require('../constants');

const appSchema = new mongoose.Schema(
  {
    _id: String,
    name: String,
    token: String,
    expiresAt: Date,
    secretKey: String,
    members: [
      {
        userId: { type: String, ref: 'User' },
        role: { type: String, enum: Object.values(APP_ROLE) },
      },
    ],
    active: { type: Boolean, default: true },
    server: { type: String, default: SERVER_ENV },
  },
  {
    _id: false,
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('App', appSchema);
