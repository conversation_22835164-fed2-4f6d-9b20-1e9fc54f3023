const { Jo<PERSON>, validate } = require('express-validation');
const { VALID_CLIENT_PAUSE } = require('../constants/clientPause');

const createClientPause = {
  body: Joi.object({
    paragraphBreak: Joi.number().min(0).max(VALID_CLIENT_PAUSE.MAX).optional(),
    sentenceBreak: Joi.number()
      .min(VALID_CLIENT_PAUSE.MIN)
      .max(VALID_CLIENT_PAUSE.MAX)
      .optional(),
    majorBreak: Joi.number()
      .min(VALID_CLIENT_PAUSE.MIN)
      .max(VALID_CLIENT_PAUSE.MAX)
      .optional(),
    mediumBreak: Joi.number()
      .min(VALID_CLIENT_PAUSE.MIN)
      .max(VALID_CLIENT_PAUSE.MAX)
      .optional(),
  }),
};

module.exports = {
  createClientPauseValidate: validate(createClientPause, { keyByField: true }),
};
