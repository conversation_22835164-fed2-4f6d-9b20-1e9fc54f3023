import { it, expect, describe } from 'vitest';
import { getSameElementsIn2Array, hasSameElementIn2Array } from './array';

describe('array', () => {
  describe('getSameElementsIn2Array', () => {
    it('return nothing', () => {
      expect(getSameElementsIn2Array([], [])).toEqual([]);
      expect(getSameElementsIn2Array([1], [2])).toEqual([]);
    });

    it('return intersection', () => {
      expect(getSameElementsIn2Array([1, 2, 3], [2, 3, 4])).toEqual([2, 3]);
    });
  });

  describe('hasSameElementIn2Array', () => {
    it('not intersect', () => {
      expect(hasSameElementIn2Array([], [])).toBeFalsy();
      expect(hasSameElementIn2Array([1], [2, 3])).toBeFalsy();
    });

    it('intersect', () => {
      expect(hasSameElementIn2Array([1, 2, 3], [1])).toBeTruthy();
      expect(hasSameElementIn2Array([1, 2, 3], [2, 3, 4])).toBeTruthy();
    });
  });
});
