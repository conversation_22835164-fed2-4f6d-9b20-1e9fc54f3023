require('dotenv').config();
require('../../models');
const dataSampleScript = require('./sampleScriptData.json');
const sampleScriptDao = require('../../daos/sampleScript');
const logger = require('../../utils/logger');

global.logger = logger;

const deleteSampleScript = async () => {
  logger.info('Starting delete sample script...', { ctx: 'RunScript' });
  await sampleScriptDao.deleteSampleScripts();
  logger.info('Delete sample script successfully', { ctx: 'RunScript' });
};

const createSampleScript = async () => {
  logger.info('Starting create sample script...', { ctx: 'RunScript' });
  await sampleScriptDao.createSampleScripts(dataSampleScript);
  logger.info('Create sample script successfully', { ctx: 'RunScript' });
};

(async () => {
  await deleteSampleScript();
  await createSampleScript();
  process.exit(1);
})();
