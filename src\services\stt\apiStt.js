const {
  RESPONSE_TYPE,
  REQUEST_TYPE,
  REQUEST_STATUS,
  VALID_SAMPLE_RATE,
} = require('../../constants');
const { createRequest } = require('../request');
const { callProcessStt } = require('./sttProcessing');
const { updateRequestById } = require('../../daos/request');
const { getUserFromApp } = require('../user');
const { sendResponseApiStt, getPayloadResponse } = require('./sendResponse');
const requestDao = require('../../daos/request');
const errorCodes = require('../../errors/code');
const CustomError = require('../../errors/CustomError');
const { checkAppPermission } = require('../app');
const { spendSttSeconds, refundSttSeconds } = require('./secondsProcessing');
const {
  getNumOfPendAndInprRequests,
  increaseNumOfPendAndInprRequests,
  decreaseNumOfPendAndInprRequests,
} = require('./queue');
const { RandomFactory } = require('../../utils/random');

const updateRequestAfterCallGate = async ({
  userId,
  requestId,
  responseType,
  gateStatus,
  gateResult,
  errorMessage,
  errorCode,
}) => {
  const { id: gateRequestId, text, textRaw } = gateResult || {};
  const isSuccess = gateStatus === 1;
  if (!isSuccess || responseType === RESPONSE_TYPE.DIRECT)
    decreaseNumOfPendAndInprRequests(userId);

  const basePayload = {
    gateRequestId,
    errorCode: errorCode || gateResult?.errorCode,
    errorMessage: errorMessage || gateResult?.errorMessage,
  };

  const payload =
    responseType === RESPONSE_TYPE.DIRECT
      ? {
          ...basePayload,
          status: isSuccess ? REQUEST_STATUS.SUCCESS : REQUEST_STATUS.FAILURE,
          'stt.text': text,
          'stt.textRaw': textRaw,
        }
      : {
          ...basePayload,
          status: isSuccess
            ? REQUEST_STATUS.IN_PROGRESS
            : REQUEST_STATUS.FAILURE,
        };
  const updatedRequest = await updateRequestById(requestId, payload);

  return updatedRequest;
};

const verifyAndIncreaseCCR = async (userId, maxCCR = 0) => {
  const currentCCR = (await getNumOfPendAndInprRequests(userId)) || 0;

  logger.info(`Current CCR: ${currentCCR}`, {
    ctx: 'ProcessRequest',
    currentCCR,
    maxCCR,
  });

  if (currentCCR >= maxCCR) {
    logger.warn('Max CCR reached', {
      ctx: 'HandleSttRequest',
      userId,
      currentCCR,
      maxCCR,
    });
    throw new CustomError(errorCodes.STT_MAX_CCR_REACHED, 'Max CCR reached');
  }

  increaseNumOfPendAndInprRequests(userId);
};

const processStt = async ({ request }) => {
  const response = await callProcessStt(request);

  return response;
};

const handleSttRequest = async ({
  ip,
  responseType = RESPONSE_TYPE.INDIRECT,
  callbackUrl,
  fileContent,
  fileUrl,
  app,
  audioDuration,
  configAudio = {},
}) => {
  const requestId = RandomFactory.getGuid();
  const requestCreatedAt = new Date();
  audioDuration = Math.ceil(audioDuration);
  const { sampleRateHertz, sampleSizeByte, channel } = configAudio;

  if (!VALID_SAMPLE_RATE.includes(sampleRateHertz))
    throw new CustomError(
      errorCodes.STT_INVALID_SAMPLE_RATE,
      `Invalid sample rate: ${sampleRateHertz}hz, must be one of ${VALID_SAMPLE_RATE.join()}`,
    );

  const user = await getUserFromApp(app);

  const appId = app._id;
  const userId = user._id;
  const { packageCode } = user.stt || {};

  const remainingSeconds = user?.stt?.remainingSeconds || 0;

  if (remainingSeconds < audioDuration) {
    logger.error('Insufficient seconds', {
      ctx: 'HandleSttRequest',
      userId,
      appId,
      audioDuration,
      remainingSeconds,
    });
    throw new CustomError(errorCodes.STT_INSUFFICIENT_SECONDS);
  }

  await verifyAndIncreaseCCR(userId, user?.stt?.concurrentRequest);

  const request = {
    ip,
    requestId,
    createdAt: requestCreatedAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    app: appId,
    userId,
    callbackUrl,
    responseType,
    type: REQUEST_TYPE.API_STT,
    packageCode,
    stt: {
      sampleRateHertz,
      sampleSizeByte,
      channel,
      fileContent,
      fileUrl,
    },
    seconds: audioDuration,
  };
  await createRequest(request);
  spendSttSeconds({ userId, requestId, seconds: audioDuration });

  const callGateResponse = await processStt({
    requestId,
    request,
    userId,
    maxCCR: user?.stt?.concurrentRequest,
  });
  const { status, result, errorMessage } = callGateResponse;
  const updatedRequest = await updateRequestAfterCallGate({
    userId,
    requestId,
    responseType,
    gateStatus: status,
    gateResult: result,
    errorMessage,
  });

  // Refund seconds if call gate fail
  if (status !== 1)
    refundSttSeconds({ userId, requestId, seconds: audioDuration });

  const response =
    responseType === RESPONSE_TYPE.DIRECT
      ? getPayloadResponse(updatedRequest)
      : {
          id: requestId,
          audioDuration,
          status: updatedRequest.status,
        };

  return response;
};

const handleSttCallback = async (
  requestId,
  { status, text, textRaw, errorCode, errorMessage, gateRequestId },
) => {
  const updateResult = {
    status: status === 1 ? REQUEST_STATUS.SUCCESS : REQUEST_STATUS.FAILURE,
    gateRequestId,
    errorCode,
    errorMessage,
    'stt.text': text,
    'stt.textRaw': textRaw,
  };
  const request = await requestDao.findRequest({ _id: requestId });
  await updateRequestById(requestId, updateResult);
  decreaseNumOfPendAndInprRequests(request.userId);

  await sendResponseApiStt(requestId);
  if (status !== 1)
    refundSttSeconds({
      userId: request.userId,
      requestId,
      seconds: request?.stt?.audioDuration,
    });
};

const getSttRequest = async ({ requestId, appToken }) => {
  const request = await requestDao.findRequest({ _id: requestId });

  if (!request) throw new CustomError(errorCodes.REQUEST_NOT_FOUND);
  await checkAppPermission(request.app, appToken);

  const payload = getPayloadResponse(request);
  return payload;
};

module.exports = { handleSttRequest, handleSttCallback, getSttRequest };
