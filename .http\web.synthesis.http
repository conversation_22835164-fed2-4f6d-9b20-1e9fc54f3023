# ============================================================================
# BASE VARIABLES
@baseUrl = http://localhost:8000
# <EMAIL>

# ============================================================================
# VARIABLES FOR TESTING

@appId = f9258cf3-b0a8-4f7d-ad0e-0b343fb419f1
@token = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
@requestId = sample-request-id-123
@voiceId = sample-voice-id-123
@userId = sample-user-id-456
@blackWordId = sample-blackword-id-789
@projectId = sample-project-id-101
@errorReportId = sample-error-report-id-202

@voiceCode = n_hn_male_duyonyx_oaistable_vc

# ============================================================================
# WEB SYNTHESIS ENDPOINTS (Authenticated Users)
# ============================================================================

### Web Synthesis
POST {{baseUrl}}/api/v1/synthesis
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "text":"Thử nghiệm tái cấu trúc API",
    "voice_code": "hn_female_ngochuyen_full_48k-fhg",
    "speed": 1.0,
    "bitrate":128,
    "backgroundMusic":{"volume":80},
    "audio_type": "mp3"
}

#1 Check spam account
#2  Push pending requests to queue {"ctx":"ProcessRequestByCcr","requestId":"6562e454-43f7-4825-9758-a1cf42df1f06","userId":"f3c41af8-226e-478e-bb9a-0400c1e6e428"}
#   {"topic":"characters-processing","message":{"key":"f3c41af8-226e-478e-bb9a-0400c1e6e428","value":"{\"event\":\"SPEND\",\"user_id\":\"f3c41af8-226e-478e-bb9a-0400c1e6e428\",\"request_id\":\"6562e454-43f7-4825-9758-a1cf42df1f06\",\"characters\":27,\"blocked_credits\":{},\"sentences_credits_info\":[{\"wallets\":[\"studio_one_time\"],\"credits\":27,\"voice_code\":\"hn_female_ngochuyen_full_48k-fhg\"}]}"},"result":[{"topicName":"characters-processing","partition":0,"errorCode":0,"baseOffset":"2","logAppendTime":"-1","logStartOffset":"0"}]} {"ctx":"KafkaProduce"}
#   {"topic":"tts-created","message":{"value":"{\"request_id\":\"6562e454-43f7-4825-9758-a1cf42df1f06\",\"user_id\":\"f3c41af8-226e-478e-bb9a-0400c1e6e428\",\"characters\":27,\"blocked_credits\":{},\"sentences_credits_info\":[{\"wallets\":[\"studio_one_time\"],\"credits\":27,\"voice_code\":\"hn_female_ngochuyen_full_48k-fhg\"}]}"},"result":[{"topicName":"tts-created","partition":0,"errorCode":0,"baseOffset":"1","logAppendTime":"-1","logStartOffset":"0"}]} {"ctx":"KafkaProduce"}
#   {"topic":"characters-processing","partition":0,"offset":"2","timestamp":"1754643434467","key":"f3c41af8-226e-478e-bb9a-0400c1e6e428","value":"{\"event\":\"SPEND\",\"user_id\":\"f3c41af8-226e-478e-bb9a-0400c1e6e428\",\"request_id\":\"6562e454-43f7-4825-9758-a1cf42df1f06\",\"characters\":27,\"blocked_credits\":{},\"sentences_credits_info\":[{\"wallets\":[\"studio_one_time\"],\"credits\":27,\"voice_code\":\"hn_female_ngochuyen_full_48k-fhg\"}]}"} {"ctx":"KafkaConsume"}
#3   Sync characters {"ctx":"UpdateByCharacters","userId":"f3c41af8-226e-478e-bb9a-0400c1e6e428","orderType":"STUDIO","remainingCharacters":0,"bonusCharacters":2946,"lockCharacters":0}
#   {"topic":"sync-characters","message":{"key":"f3c41af8-226e-478e-bb9a-0400c1e6e428","value":"{\"event\":\"SPEND\",\"user_id\":\"f3c41af8-226e-478e-bb9a-0400c1e6e428\",\"order_type\":\"STUDIO\",\"remaining_characters\":0,\"bonus_characters\":2946,\"lock_characters\":0,\"sync_characters_at\":\"2025-08-08T08:57:15.135Z\",\"studio\":{\"custom\":{\"captured_referral_credits\":0,\"is_locked\":false,\"remaining_credits\":0},\"cycle\":{\"captured_referral_credits\":0,\"captured_rollover_credits\":0,\"credits_per_cycle\":0,\"is_locked\":false,\"purchased_top_up_credits\":0,\"remaining_credits\":0},\"one_time\":{\"captured_referral_credits\":0,\"package_expiry_date\":null},\"top_up\":{\"captured_rollover_credits\":0,\"is_locked\":false,\"remaining_credits\":0}}}"},"result":[{"topicName":"sync-characters","partition":0,"errorCode":0,"baseOffset":"1","logAppendTime":"-1","logStartOffset":"0"}]} {"ctx":"KafkaProduce"}
#4  Pop pending request from queue {"ctx":"ProcessRequestByCcr","requestId":"6562e454-43f7-4825-9758-a1cf42df1f06","userId":"f3c41af8-226e-478e-bb9a-0400c1e6e428"}

