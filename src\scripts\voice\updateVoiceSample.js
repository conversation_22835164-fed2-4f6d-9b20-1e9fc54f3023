require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update voice sample...`, {
    ctx: 'RunScriptUpdateVoiceSample',
  });

  const voices = require('./seedVoiceSample.json');
  for (const voice of voices) {
    await Voice.findOneAndUpdate({ code: voice.code }, voice);
  }

  logger.info(`Update voice sample successfully`, {
    ctx: 'RunScriptUpdateVoiceSample',
  });
  process.exit(1);
})();
