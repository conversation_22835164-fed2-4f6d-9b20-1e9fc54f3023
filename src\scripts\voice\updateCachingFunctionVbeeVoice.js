require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const VBEE_CACHING_FUNCTION = {
  'hn_female_ngochuyen_full_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-ngochuyen-news-44k',
  'hn_female_maiphuong_vdts_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-maiphuong-vdts',
  'sg_male_minhhoang_full_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-minhhoang-full',
  'hue_female_huonggiang_full_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-huonggiang-full',
  'hn_male_manhdung_news_48k-fhg': 'vbee_cloud_tts_cache-viterbi-manhdung-news',
  'sg_female_thaotrinh_full_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-thaotrinh-full',
  'hue_male_duyphuong_full_48k-fhg':
    'vbee_cloud_tts_cache-viterbi-duyphuong-full',
  'sg_female_tuongvy_call_44k-fhg':
    'vbee_cloud_tts_cache-viterbi-tuongvy-call-44k',
};

const updateCachingFunction = async (voiceCode, cachingFunction) => {
  try {
    await Voice.findOneAndUpdate({ code: voiceCode }, { cachingFunction });
  } catch (error) {
    logger.error(`Update caching voice of ${voiceCode} failure`, {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  for (const [voiceCode, cachingFunction] of Object.entries(
    VBEE_CACHING_FUNCTION,
  )) {
    await updateCachingFunction(voiceCode, cachingFunction);
    const voices = await Voice.find({});
    global.VOICES = voices;
    logger.info(`Update caching function of ${voiceCode} successfully`, {
      ctx: 'RunScript',
    });
  }
  process.exit(1);
})();
