const callApi = require('../../utils/callApi');
const { createCallbackResult } = require('../../daos/callbackResult');
const { findRequestById } = require('../../daos/request');
const { REQUEST_STATUS } = require('../../constants');
const getErrorMessage = require('../../errors/message');

const sendCallBack = async ({ url, requestId, payload }) => {
  let statusCode;
  let result;
  try {
    const response = await callApi({
      method: 'POST',
      url,
      data: payload,
    });

    logger.info(JSON.stringify(response.data), {
      ctx: 'SendApiResponse',
      requestId,
    });

    statusCode = response.status;
    result = response.data;
  } catch (err) {
    logger.error(err, { ctx: 'SendApiResponse', requestId });
    statusCode = err.response?.status || 500;
    result = err.response?.data || err.response?.statusText || err.message;
  }

  return { statusCode, result };
};

const getPayloadResponse = (request = {}) => {
  const { errorCode, errorMessage, status, seconds } = request;
  const { text, textRaw } = request.stt || {};
  if (status === REQUEST_STATUS.FAILURE)
    return {
      id: request._id,
      errorCode,
      errorMessage: errorMessage || getErrorMessage(errorCode) || undefined,
      status: 0,
    };

  if (status === REQUEST_STATUS.SUCCESS)
    return { id: request._id, text, textRaw, audioDuration: seconds };

  return { id: request._id, audioDuration: seconds, status: request.status };
};

const sendResponseApiStt = async (requestId) => {
  const request = await findRequestById(requestId);

  const { callbackUrl } = request;
  const payload = getPayloadResponse(request);

  // Send request response to callback url
  const { statusCode, result } = await sendCallBack({
    url: callbackUrl,
    requestId,
    payload,
  });

  createCallbackResult({
    requestId,
    callbackUrl,
    payload,
    statusCode,
    result,
  });
};

module.exports = { sendResponseApiStt, getPayloadResponse };
