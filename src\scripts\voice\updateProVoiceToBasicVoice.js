require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const updateStatusActive = async () => {
  try {
    await Voice.updateMany(
      {
        provider: VOICE_PROVIDER.VBEE,
      },
      {
        $set: {
          level: 'STANDARD',
        },
        $pull: {
          features: 'pro-vietnam-voice',
        },
      },
    );
  } catch (error) {
    logger.error('Update status active to voice failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update pro voice to basic voice...`, {
    ctx: 'RunScript',
  });
  await updateStatusActive();
  logger.info(`Update status pro voice to basic voice`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
