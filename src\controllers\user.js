const { AuthorizationService } = require('../services/authorization');
const userService = require('../services/user');

const getUsersNotResetCharacter = async (req, res) => {
  const { packageCode, totalBonusCharacters, limit } = req.query;
  const userIds = await userService.getUsersNotResetCharacter({
    packageCode,
    totalBonusCharacters: parseInt(totalBonusCharacters, 10),
    limit: parseInt(limit, 10) || undefined,
  });
  return res.send({ userIds });
};

const getUsersNotResetSeconds = async (req, res) => {
  const { packageCode, totalBonusSeconds, limit } = req.query;
  const userIds = await userService.getUsersNotResetSecond({
    packageCode,
    totalBonusSeconds: parseInt(totalBonusSeconds, 10),
    limit: parseInt(limit, 10) || undefined,
  });
  return res.send({ userIds });
};

const getUser = async (req, res) => {
  const { publicIP: ip } = req.__clientInfo;
  const { userId } = req.user;
  const user = await userService.getUser(userId, ip);
  return res.send(user);
};

const getUserById = async (req, res) => {
  const { userId } = req.params;
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: BUSINESS: can we throw if user not found?

  return res.send(user);
};

const updateLockStudioCredits = async (req, res) => {
  const { userId } = req.params;
  const { action, walletTypes } = req.body;

  await userService.lockCredits({ userId, action, walletTypes });

  return res.send({});
};

const createShareVoiceProfile = async (req, res) => {
  const { userId } = req;
  const { userSlug, displayName, bio } = req.body;

  const userUpdated = await userService.createShareVoiceProfile({
    userId,
    userSlug,
    displayName,
    bio,
  });

  return res.send({ user: userUpdated });
};

const checkCommunityVoiceRevenueAccess = async (req, res) => {
  const { userId } = req.user;
  const hasAccess = await userService.checkCommunityVoiceRevenueAccess(userId);

  return res.send({ hasAccess });
};

module.exports = {
  getUsersNotResetCharacter,
  getUsersNotResetSeconds,
  getUser,
  getUserById,
  updateLockStudioCredits,
  createShareVoiceProfile,
  checkCommunityVoiceRevenueAccess,
};
