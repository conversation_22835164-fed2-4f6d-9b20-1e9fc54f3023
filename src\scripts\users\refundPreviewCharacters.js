/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');
require('../../services/kafka');

const Request = require('../../models/request');

const userService = require('../../services/user');
const { SYNC_CHARACTERS_EVENT } = require('../../constants');

const logger = require('../../utils/logger');

const refundPreviewCharactersByUser = async (userId, characters) => {
  await userService.updateByCharacters({
    event: SYNC_CHARACTERS_EVENT.REFUND_PREVIEW_CHARACTERS,
    userId,
    characters,
  });
};

const refundPreviewCharacters = async () => {
  const users = await Request.aggregate([
    {
      $match: {
        userId: { $exists: true },
        demo: true,
      },
    },
    {
      $group: {
        _id: '$userId',
        characters: { $sum: '$characters' },
      },
    },
  ]);

  const totalUsers = users.length;
  const split = 1;
  let flag = 1;

  for (const [index, user] of users.entries()) {
    try {
      if (index === 0 || flag * split === index) {
        flag += 1;
        logger.info(
          `Increase ${((index * 100) / totalUsers).toFixed(2)}% users`,
          { ctx: 'RunScript' },
        );
      }

      const { _id: userId, characters } = user;
      await refundPreviewCharactersByUser(userId, characters);
    } catch (error) {
      logger.error(`Refund preview characters for user failed - ${user._id}`, {
        ctx: 'RunScript',
        stack: error.stack,
      });
      throw error;
    }
  }
};

(async () => {
  logger.info('Start refund preview characters for user...', {
    ctx: 'RunScript',
  });
  await refundPreviewCharacters();
  logger.info('Refund preview characters for user successfully', {
    ctx: 'RunScript',
  });
})();
