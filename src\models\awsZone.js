const mongoose = require('mongoose');
const { REQUEST_TYPE } = require('../constants');

const awsZoneSchema = new mongoose.Schema(
  {
    accountId: { type: String },
    name: { type: String, required: true },
    region: { type: String, required: true },
    weight: { type: Number, required: true },
    allowRequestTypes: [{ type: String, enum: Object.values(REQUEST_TYPE) }],
    normalizerFunction: { type: String },
    sentenceTokenizerFunction: { type: String },
    newSentenceTokenizerFunction: { type: String },
    textToAllophoneFunction: { type: String },
    synthesisFunction: { type: String },
    joinSentencesFunction: { type: String },
    srtFunction: { type: String },
    defaultS3Bucket: { type: String },
    s3Buckets: { type: Object },
    s3CloudFront: { type: Object },
    s3AccessKeyId: { type: String },
    s3SecretAccessKey: { type: String },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('AwsZone', awsZoneSchema);
