
# ============================================================================
# DUBBING ENDPOINTS
# 🎬 Dubbing (9 endpoints)
# Video and audio dubbing
# Dubbing project management
# Language support

# 📝 SRT (Subtitle) (1 endpoint)
# Subtitle content extraction
# ============================================================================

### Dubbing with Video
POST {{baseUrl}}/api/v1/dubbing/video
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "video_url": "https://example.com/video.mp4",
    "target_language": "vi",
    "voice_settings": {
        "voice_code": "hn_female_ngochuyen_full_48k-fhg",
        "speed_rate": 1.0
    }
}

### Dubbing (Audio only)
POST {{baseUrl}}/api/v1/dubbing
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "audio_url": "https://example.com/audio.wav",
    "target_language": "vi",
    "voice_code": "hn_female_ngochuyen_full_48k-fhg"
}

### Count Subtitle Characters
GET {{baseUrl}}/api/v1/dubbing/subtitle-characters?subtitle_url=https://example.com/subtitle.srt
Authorization: Bearer {{token}}

### Get Dubbing Languages
GET {{baseUrl}}/api/v1/dubbing/languages

### Create Dubbing Project
POST {{baseUrl}}/api/v1/dubbing/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "My Dubbing Project",
    "source_language": "en",
    "target_language": "vi"
}

### Get Dubbing Projects
GET {{baseUrl}}/api/v1/dubbing/projects
Authorization: Bearer {{token}}

### Get Dubbing Project
GET {{baseUrl}}/api/v1/dubbing/projects/{{projectId}}
Authorization: Bearer {{token}}

### Update Dubbing Project
PUT {{baseUrl}}/api/v1/dubbing/projects/{{projectId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Updated Dubbing Project"
}

### Delete Dubbing Projects
DELETE {{baseUrl}}/api/v1/dubbing/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "project_ids": ["project1", "project2"]
}




# ============================================================================
# SRT (SUBTITLE) ENDPOINTS
# ============================================================================

### Extract SRT Content from Link
POST {{baseUrl}}/api/v1/srt/extract-from-link
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "url": "https://example.com/subtitle.srt"
}
