const clientPauseDao = require('../daos/clientPause');
const { omitIsNil } = require('../utils/omit');

const createClientPause = async (
  userId,
  { paragraphBreak, sentenceBreak, majorBreak, mediumBreak },
) => {
  const clientPauseExist = await clientPauseDao.findClientPause(userId);

  const clientPause = omitIsNil(
    { paragraphBreak, sentenceBreak, majorBreak, mediumBreak },
    { deep: true },
  );

  if (clientPauseExist) {
    const { _id } = clientPauseExist;
    await clientPauseDao.updateClientPause(_id, clientPause);
  } else {
    await clientPauseDao.createClientPause({ userId, ...clientPause });
  }
};

module.exports = { createClientPause };
