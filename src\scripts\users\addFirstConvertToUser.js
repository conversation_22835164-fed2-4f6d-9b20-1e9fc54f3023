require('dotenv').config();
require('../../models');

const User = require('../../models/user');
const Request = require('../../models/request');
const logger = require('../../utils/logger');

global.logger = logger;

const addFirstConvertAtForUsers = async (userIds = []) => {
  const requests = await Request.aggregate([
    { $match: { userId: { $in: userIds }, demo: false } },
    { $group: { _id: '$userId', createdAt: { $first: '$createdAt' } } },
  ]);
  const bulkOps = requests.map((request) => {
    const { _id: userId, createdAt: firstConvertAt } = request;
    return {
      updateOne: {
        filter: { _id: userId },
        update: { $set: { firstConvertAt } },
      },
    };
  });
  await User.bulkWrite(bulkOps);
};

const addFirstConvertAts = async (userCondition) => {
  logger.info(`Starting add first convert at`, { ctx: 'RunScript' });

  const BATCH = 1000;
  const totalUsers = await User.countDocuments(userCondition);

  logger.info(`Total users: ${totalUsers}`, { ctx: 'RunScript' });
  for (let i = 0; i < totalUsers; i += BATCH) {
    logger.info(
      `Processing ${i} of ${totalUsers}: ${Math.round(
        (i / totalUsers) * 100,
      )}%`,
      { ctx: 'RunScript' },
    );
    const users = await User.find(userCondition).skip(i).limit(BATCH);
    const userIds = users.map((user) => user._id);
    await addFirstConvertAtForUsers(userIds);
  }

  logger.info(`Add first convert at successfully`, { ctx: 'RunScript' });
};

(async () => {
  // Change condition to query users here
  const userCondition = {
    firstConvertAt: { $exists: false },
  };
  await addFirstConvertAts(userCondition);
  process.exit(1);
})();
