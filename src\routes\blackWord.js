const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const blackWordController = require('../controllers/blackWord');
const { auth, hasRole } = require('../middlewares/auth');
const {
  createBlackWordValidate,
  editBlackWordValidate,
  deleteBlackWordValidate,
} = require('../validations/blackWord');

/* eslint-disable prettier/prettier */
router.get('/black-words', auth, asyncMiddleware(blackWordController.getBlackWords));
router.post('/black-words', auth, hasRole('manage-black-words'), createBlackWordValidate, asyncMiddleware(blackWordController.createBlackWord));
router.put('/black-words/:blackWordId', auth, hasRole('manage-black-words'), editBlackWordValidate, asyncMiddleware(blackWordController.editBlackWord));
router.delete('/black-words/:blackWordId', auth, hasRole('manage-black-words'), deleteBlackWordValidate, asyncMiddleware(blackWordController.deleteBlackWord));

/* eslint-disable prettier/prettier */

module.exports = router;
