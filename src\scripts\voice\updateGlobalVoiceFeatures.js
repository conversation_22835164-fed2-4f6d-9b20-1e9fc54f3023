require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const updateGlobalVoiceFeatures = async () => {
  logger.info(`Starting update feature global voices...`, {
    ctx: 'UpdateGlobalVoiceFeatures',
  });

  try {
    // Update standard voice
    await Voice.updateMany(
      {
        provider: { $ne: VOICE_PROVIDER.VBEE },
        level: 'BASIC',
      },
      {
        $set: {
          features: ['standard-global-voice'],
          level: 'STANDARD',
        },
      },
    );

    // Update premium voice
    await Voice.updateMany(
      {
        provider: { $ne: VOICE_PROVIDER.VBEE },
        level: 'PRO',
      },
      {
        $set: {
          features: ['standard-global-voice', 'premium-global-voice'],
          level: 'PREMIUM',
        },
      },
    );

    logger.info(`Update global voice features successfully`, {
      ctx: 'RunScript',
    });
  } catch (error) {
    logger.error('Update global voice features failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  await updateGlobalVoiceFeatures();
  process.exit(1);
})();
