
# ============================================================================
# SPEECH-TO-TEXT (STT) API ENDPOINTS
# 🎧 Speech-to-Text (STT) API (3 endpoints)
# STT synthesis with file upload
# STT callback handling
# STT request status
# ============================================================================

### STT API (Speech Recognition)
POST {{baseUrl}}/api/v1/stt
Authorization: Bearer {{token}}
app-id: {{appId}}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="file_content"; filename="audio.wav"
Content-Type: audio/wav

< ./path/to/audio.wav
--boundary
Content-Disposition: form-data; name="response_type"

indirect
--boundary
Content-Disposition: form-data; name="callback_url"

https://example.com/stt-callback
--boundary--

### STT Callback Response
POST {{baseUrl}}/api/v1/stt/callback
Content-Type: application/json

{
    "request_id": "{{requestId}}",
    "status": "completed",
    "result": {
        "transcript": "Recognized speech text"
    }
}

### Get STT Request Status
GET {{baseUrl}}/api/v1/stt/{{requestId}}
