const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const asyncMiddleware = require('./async');
const { containBlackWords } = require('../services/blackWord');

const blockBlackWords = async (req, res, next) => {
  const { inputText, text, sentences } = req.body;

  const SPACE = ' ';
  const requestText =
    inputText ||
    text ||
    sentences?.reduce(
      (prev, curr) => prev + SPACE + (curr.text || curr.inputText)?.trim(),
      '',
    );

  const isHateSpeech = containBlackWords(requestText);
  if (isHateSpeech) throw new CustomError(errorCodes.IS_HATE_SPEECH);

  next();
};

module.exports = {
  blockBlackWords: asyncMiddleware(blockBlackWords),
};
