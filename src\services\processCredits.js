const { WALLET_TYPES } = require('../constants');

const calCredits = (currentCredits, spentCredits) => {
  const deducted = Math.min(spentCredits, currentCredits);
  spentCredits -= deducted;
  return { credits: currentCredits - deducted, spentCredits };
};

const processWallets = ({ usageWallets, currWallets, spentCredits }) => {
  let { onetimeCredits, cycleCredits, topUpCredits, customCredits } =
    currWallets;

  for (const walletType of usageWallets) {
    switch (walletType) {
      case WALLET_TYPES.STUDIO_ONE_TIME: {
        const updated = calCredits(onetimeCredits, spentCredits);
        onetimeCredits = updated.credits;
        spentCredits = updated.spentCredits;
        break;
      }
      case WALLET_TYPES.STUDIO_CYCLE: {
        const updatedCycleCredits = calCredits(cycleCredits, spentCredits);
        cycleCredits = updatedCycleCredits.credits;
        spentCredits = updatedCycleCredits.spentCredits;
        break;
      }
      case WALLET_TYPES.STUDIO_TOP_UP: {
        const updatedTopUpCredits = calCredits(topUpCredits, spentCredits);
        topUpCredits = updatedTopUpCredits.credits;
        spentCredits = updatedTopUpCredits.spentCredits;
        break;
      }
      case WALLET_TYPES.STUDIO_CUSTOM: {
        const updatedCustomCredits = calCredits(customCredits, spentCredits);
        customCredits = updatedCustomCredits.credits;
        spentCredits = updatedCustomCredits.spentCredits;
        break;
      }
      default:
        break;
    }
  }

  return {
    onetimeCredits,
    cycleCredits,
    topUpCredits,
    customCredits,
    spentCredits,
  };
};

const processWalletCreditsDeduction = ({
  sentencesCreditsInfo,
  currWallets,
}) => {
  let {
    onetimeCredits = 0,
    cycleCredits = 0,
    topUpCredits = 0,
    customCredits = 0,
  } = currWallets;

  for (const sentence of sentencesCreditsInfo) {
    const { wallets, credits } = sentence;

    const result = processWallets({
      usageWallets: wallets,
      currWallets: {
        onetimeCredits,
        cycleCredits,
        topUpCredits,
        customCredits,
      },
      spentCredits: credits,
    });

    if (result.spentCredits > 0) return false;

    // Update credits for next iteration
    onetimeCredits = result.onetimeCredits;
    cycleCredits = result.cycleCredits;
    topUpCredits = result.topUpCredits;
    customCredits = result.customCredits;
  }

  return {
    onetimeCredits,
    cycleCredits,
    topUpCredits,
    customCredits,
  };
};

const getUserUpdateCredits = ({ user, credits, blockedCredits }) => {
  // Spend characters: topUp -> cycle -> oneTime (bonus -> remaining)
  const {
    studio: { cycle = {}, topUp = {}, custom = {} } = {},
    remainingCharacters: oneTimeRemainingCredits = 0,
    bonusCharacters: oneTimeBonusCredits = 0,
  } = user;

  const canUseCycleCredits = !blockedCredits?.cycleCredits;
  const canUseTopTupCredits = !blockedCredits?.topUpCredits;
  const canUseOneTimeCredits = !blockedCredits?.oneTimeCredits;
  const canUseCustomCredits = !blockedCredits?.customCredits;

  let remainingCredits = credits;

  const updateCredits = (currentCredits) => {
    const deducted = Math.min(remainingCredits, currentCredits);
    remainingCredits -= deducted;
    return currentCredits - deducted;
  };

  const remainingCycleCredits = cycle.remainingCredits || 0;
  let updatedCycleCredits = remainingCycleCredits;
  let updatedTopUpCredits = topUp.remainingCredits || 0;
  let updatedOneTimeBonusCredits = oneTimeBonusCredits;
  let updatedCustomCredits = custom?.remainingCredits || 0;
  let updatedRemainingOneTimeCredits = oneTimeRemainingCredits;

  // Spend characters: topUp -> cycle -> oneTime (bonus -> remaining)
  if (canUseTopTupCredits)
    updatedTopUpCredits = updateCredits(topUp.remainingCredits || 0);

  if (canUseCycleCredits)
    updatedCycleCredits = updateCredits(remainingCycleCredits);

  if (canUseOneTimeCredits) {
    updatedOneTimeBonusCredits = updateCredits(oneTimeBonusCredits);
    updatedRemainingOneTimeCredits = updateCredits(oneTimeRemainingCredits);
  }

  const userUpdateFields = {
    bonusCharacters: updatedOneTimeBonusCredits,
    remainingCharacters: updatedRemainingOneTimeCredits,
  };

  if (canUseCustomCredits)
    updatedCustomCredits = updateCredits(custom?.remainingCredits || 0);

  if (user?.studio?.custom)
    userUpdateFields['studio.custom.remainingCredits'] = updatedCustomCredits;

  if (user?.studio?.cycle)
    userUpdateFields['studio.cycle.remainingCredits'] = updatedCycleCredits;
  if (user?.studio?.topUp)
    userUpdateFields['studio.topUp.remainingCredits'] = updatedTopUpCredits;

  return userUpdateFields;
};

const getUserUpdateCreditsProcessWallet = ({
  sentencesCreditsInfo,
  currWallets,
  isFreeUser,
}) => {
  const updatedWallets = processWalletCreditsDeduction({
    sentencesCreditsInfo,
    currWallets,
  });
  if (!updatedWallets) return false; // Not enough credits

  const userUpdateFields = {};
  if (isFreeUser)
    userUpdateFields.bonusCharacters = updatedWallets.onetimeCredits;
  else userUpdateFields.remainingCharacters = updatedWallets.onetimeCredits;

  if (currWallets.cycleCredits)
    userUpdateFields['studio.cycle.remainingCredits'] =
      updatedWallets.cycleCredits;
  if (currWallets.topUpCredits)
    userUpdateFields['studio.topUp.remainingCredits'] =
      updatedWallets.topUpCredits;

  if (currWallets.customCredits)
    userUpdateFields['studio.custom.remainingCredits'] =
      updatedWallets.customCredits;

  return userUpdateFields;
};

module.exports = {
  getUserUpdateCredits,
  processWalletCreditsDeduction,
  getUserUpdateCreditsProcessWallet,
};
