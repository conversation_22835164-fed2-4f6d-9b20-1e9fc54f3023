require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const voice = {
  code: 'hn_male_phuthang_stor80dt_48k-fhg',
  name: 'H<PERSON> - <PERSON><PERSON>hăng',
  gender: 'male',
  languageCode: 'vi-VN',
  type: 'Neural TTS',
  provider: 'vbee',
  squareImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/phu-thang.png',
  roundImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/phu-thang.png',
  demo: 'https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_phuthang_stor80dt_48k-fhg.mp3',
  rank: 4,
  synthesisFunction: 'a2s-fhg-prod-phuthang-stor80dt-fhg',
  active: true,
  defaultSampleRate: 22050,
  sampleRates: [8000, 16000, 22050],
  level: 'BASIC',
  styles: ['story'],
  beta: true,
};

(async () => {
  logger.info(`Starting create new voice...`, { ctx: 'RunScript' });
  await Voice.create(voice);
  const voices = await Voice.find({});
  global.VOICES = voices;
  logger.info(`Create new voice successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
