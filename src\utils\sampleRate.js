const generateMSVoiceSampleRates = (maxRate) => {
  // const commonRates = [192000, 96000, 48000, 44100, 32000, 22050, 16000, 8000];
  const commonRates = [48000, 24000, 16000]; // MS Azure mp3 only supports 48000, 24000, 16000

  const sampleRates = commonRates.filter((rate) => rate <= maxRate);

  if (!commonRates.includes(maxRate)) {
    sampleRates.push(maxRate);
    sampleRates.sort((a, b) => b - a);
  }

  return sampleRates;
};

module.exports = { generateMSVoiceSampleRates };
