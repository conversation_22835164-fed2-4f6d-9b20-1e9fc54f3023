const moment = require('moment');
const logger = require('../utils/logger');
const projectDao = require('../daos/project');
const sampleProjectDao = require('../daos/sampleProject');
const { handleSynthesisRequest } = require('./synthesis');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { REQUEST_STATUS, BLOCK_SYNTHESIS_STATUS } = require('../constants');
const { joinAudio } = require('./cloudRun/joiner');
const { AuthorizationService } = require('./authorization');
const {
  validateWalletCredits,
  countCreditsByVoiceFactor,
} = require('./preprocessing');
const { getAudioName, copyLinkToDestinationStorage } = require('./audio');
const {
  getVoiceWithCreditFactor,
  getVoiceInfoByCodes,
  addLanguageDetailToVoice,
} = require('./voice');
const { getPackageUsageOptions } = require('./package');
const { isVoiceCloningCode } = require('./voiceCloning');

const createProject = async ({ userId, title, product, blocks }) => {
  const newBlocks = blocks.map((block) => {
    block._id = block.id;
    delete block.id;
    return block;
  });

  const projectData = {
    userId,
    title,
    product,
    blocks: newBlocks,
  };
  const project = await projectDao.createProject(projectData);
  return project;
};

const getProjects = async ({ userId, limit, offset, sort, search }) => {
  const projects = await projectDao.findProjects({
    userId,
    limit,
    offset,
    sort,
    search,
  });
  return projects;
};

const getProject = async ({ userId, projectId }) => {
  // Get project
  const project = await projectDao.findProject({ userId, projectId });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);

  // Add voice details to blocks
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: can we throw if user not found? nothing to update if user not found

  const { packageCode } = user || {};
  const { blocks } = project;
  const studioUsageOptions = await getPackageUsageOptions({
    userId,
    packageCode: user.packageCode,
    userUsageOptions: user,
  });

  const voiceCodes = blocks.map((block) => block.voiceCode);
  const removedDuplicatedVoiceCodes = [...new Set(voiceCodes)];
  const voices = await getVoiceInfoByCodes(
    removedDuplicatedVoiceCodes,
    userId,
    studioUsageOptions.features,
  );
  const voicesWithCreditFactor = voices.map((voice) =>
    getVoiceWithCreditFactor(voice, packageCode),
  );

  const voicesWithLanguageDetail = await Promise.all(
    voicesWithCreditFactor.map(async (item) => {
      const voice = await addLanguageDetailToVoice(item);
      return voice;
    }),
  );

  const voiceDetailsMap = Object.fromEntries(
    voicesWithLanguageDetail.map((voice) => {
      const isClonedVoice = isVoiceCloningCode(voice?.code);
      const commonVoiceData = {
        code: voice.code,
        name: voice?.name,
        roundImage: voice?.roundImage,
        language: {
          roundImage: voice?.language?.roundImage,
        },
        level: voice?.level,
        eolDate: voice?.eolDate,
        creditFactor: voice?.creditFactor,
        canonicalVoice: voice?.canonicalVoice,
        provider: voice?.provider,
      };

      const addOnVoiceCloningData = {
        discardAt: voice?.discardAt,
        retentionDays: voice?.retentionDays,
        status: voice?.status,
        userId: voice?.userId,
      };

      const result = isClonedVoice
        ? { ...commonVoiceData, ...addOnVoiceCloningData }
        : commonVoiceData;
      return [voice.code, result];
    }),
  );
  project.blocks = blocks.map((block) => ({
    ...block,
    voice: voiceDetailsMap[block.voiceCode],
  }));

  // Return project with voice details
  return project;
};

const getProjectWithSelectedFields = async ({ userId, projectId, fields }) => {
  const project = await projectDao.findProject({
    userId,
    projectId,
    fields,
  });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);

  return project;
};

const updateProject = async ({
  userId,
  projectId,
  title,
  blocks,
  audioType,
}) => {
  const project = await projectDao.findProject({ projectId, userId });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);

  // Get sample project
  const hasSampleBlock = blocks.some((block) => block.isSample);
  const sampleProject = hasSampleBlock
    ? await sampleProjectDao.getSampleProject()
    : null;

  // Convert an array of saved block into an object in which
  // each key is blockId and value is block.
  const savedBlocks = project.blocks.reduce((acc, block) => {
    acc[block._id] = block;
    return acc;
  }, {});

  // Map through each block in new blocks
  // Each iteration will check if this block is editted
  // or if this block is existed.
  const newBlocks = blocks.map((block) => {
    // If this block is a sample block, return the sample block
    const sampleBlock = sampleProject?.blocks.find(
      (b) => b._id === block.id && block.isSample,
    );
    if (sampleBlock) {
      return { ...sampleBlock, _id: block.id };
    }

    // Check if this block is editted
    const prevBlock = savedBlocks[block.id];

    // This means new block is added
    if (!prevBlock) {
      return { ...block, _id: block.id };
    }

    // Check if this block is editted
    const isEdited =
      prevBlock.text !== block.text ||
      prevBlock.voiceCode !== block.voiceCode ||
      prevBlock.characters !== block.characters ||
      prevBlock.breakTime !== block.breakTime ||
      prevBlock.speed !== block.speed ||
      prevBlock.isSample !== block.isSample;

    if (isEdited) {
      return { ...block, _id: block.id };
    }

    return prevBlock;
  });

  const updatedProject = await projectDao.updateProject({
    projectId,
    title,
    blocks: newBlocks,
    audioType,
  });
  return updatedProject;
};

const synthesisProject = async ({ userId, email, projectId, blocks }) => {
  const project = await projectDao.findProject({ userId, projectId });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);

  const user = await AuthorizationService.getUser(userId);

  // Validate user credit
  const sentencesCreditsInfo = await countCreditsByVoiceFactor({
    sentences: blocks,
    // ADVISE: the ssmlRegex is always undefined, can we remove the input argument? because it always fallback to something else. Trace the caller tree to avoid side-effect.
    user,
  });

  const { remainingCharacters, bonusCharacters } = user;
  validateWalletCredits({
    demo: false,
    sentencesCreditsInfo,
    currWallets: {
      onetimeCredits: remainingCharacters + bonusCharacters,
      cycleCredits: user?.studio?.cycle?.remainingCredits || 0,
      topUpCredits: user?.studio?.topUp?.remainingCredits || 0,
      customCredits: user?.studio?.custom?.remainingCredits || 0,
    },
  });

  // Make a for loop and call handleSynthesisRequest for each blocks
  const synthesisBlockResults = [];
  for (let i = 0; i < blocks.length; i += 1) {
    const { id: blockId, text, voiceCode, speed, audioType } = blocks[i];
    let result = { ...blocks[i] };
    try {
      const request = await handleSynthesisRequest({
        email,
        userId,
        projectId,
        blockId,
        text,
        voiceCode,
        speed,
        audioType,
      });

      result = {
        ...result,
        requestId: request.requestId,
        status: request.status,
      };
    } catch (error) {
      // Error thrown from handleSynthesisRequest will be caught here
      // and that block synthesis status will be FAILURE
      logger.error(error, {
        ctx: 'SynthesisProject',
        projectId,
        blockId,
      });
      result = {
        ...result,
        status: REQUEST_STATUS.FAILURE,
      };
    } finally {
      synthesisBlockResults.push(result);
    }
  }

  return {
    projectId,
    blocks: synthesisBlockResults,
  };
};

const deleteProject = async ({ userId, projectId }) => {
  const project = await projectDao.findProject({ projectId, userId });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);
  const deletedProject = await projectDao.deleteProject(projectId);
  return deletedProject;
};

const getJoinedAudio = async ({ userId, projectId }) => {
  // Get project from database
  const project = await projectDao.findProject({ userId, projectId });
  if (!project) throw new CustomError(code.PROJECT_NOT_FOUND);

  // Check if all blocks are synthesized successfully and audio link is valid
  const { blocks, sampleRate, bitrate, title } = project;

  const blocksWithText = blocks?.filter((block) => block?.text?.trim());

  if (!blocksWithText?.length) {
    throw new CustomError(code.INVALID_BLOCK, 'Blocks is empty');
  }

  const checkValidAudioExpiredTime = (time) => {
    if (!time) return false;
    return moment().isBefore(time);
  };
  const isValidBlocks = blocksWithText.every(
    (block) =>
      block.status === BLOCK_SYNTHESIS_STATUS.SUCCESS &&
      block.audioLink &&
      checkValidAudioExpiredTime(block.audioExpiredAt),
  );
  if (!isValidBlocks)
    throw new CustomError(
      code.INVALID_BLOCK,
      'All blocks must be synthesized successfully and audio link must be valid before joining',
    );

  // Join audios
  const audios = blocksWithText.map((block) => block.audioLink);
  const outputType = project.audioType;
  const joinedAudioLink = await joinAudio({
    audios,
    outputType,
    sampleRate,
    bitRate: bitrate,
  });

  // Copy joined audio to destination storage
  const audioName = `Vbee_${getAudioName(
    title,
    projectId,
    userId,
  )}_${moment().unix()}`;
  const today = moment().format('YYYY/MM/DD');
  const destinationKey = `synthesis/${today}/${audioName}.${outputType}`;
  const audioLink = await copyLinkToDestinationStorage({
    sourceLink: joinedAudioLink,
    destinationKey,
    userId,
  });

  return audioLink;
};

const getSampleProject = async () => {
  const sampleProject = await sampleProjectDao.getSampleProject();
  return sampleProject;
};

module.exports = {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  synthesisProject,
  getJoinedAudio,
  getSampleProject,
  getProjectWithSelectedFields,
};
