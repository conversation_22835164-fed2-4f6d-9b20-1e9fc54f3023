require('dotenv').config();
require('../../models');

const { PACKAGE_CODE } = require('../../constants');
const User = require('../../models/user');
const logger = require('../../utils/logger');

global.logger = logger;

const addMaxPreviewWithTrialUser = async (maxPreview) => {
  logger.info(`Starting add max preview`, { ctx: 'RunScript' });
  await User.updateMany(
    { packageCode: PACKAGE_CODE.STUDIO_TRIAL },
    { maxPreview },
  );
  logger.info(`Add add max preview successfully`, { ctx: 'RunScript' });
};

(async () => {
  await addMaxPreviewWithTrialUser(3);
  process.exit(1);
})();
