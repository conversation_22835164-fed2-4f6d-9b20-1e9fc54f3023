# ============================================================================
# BASE VARIABLES
# ============================================================================
@baseUrl = http://localhost:8000
# <EMAIL>
@appId = d8d64cb5-23f0-4c18-b80d-26397715a13d
@tokenAPI = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTI2MzMzODZ9._QDrXz5L7H9BfOi8sr81Y3XkDOUMyqUGuhBiWZbbXNE
@token = **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
@requestId = sample-request-id-123

# ============================================================================

### Get TTS Request Status
GET {{baseUrl}}/api/v1/apps/{{appId}}
Authorization: Bearer {{token}}

# ============================================================================
# TEXT-TO-SPEECH (TTS) API ENDPOINTS
# Pay As You Go API https://documenter.getpostman.com/view/12951168/Uz5FHbSd
# Main TTS synthesis API with various options
# Direct/indirect response types
# Multi-sentence synthesis
# Caching synthesis and text length counting
# ============================================================================

### TTS API Synthesis (Main API)
POST {{baseUrl}}/api/v1/tts
Authorization: Bearer {{token}}
app-id: {{appId}}
Content-Type: application/json

{
    "input_text": "Hello, this is a test message for TTS synthesis",
    "voice_code": "hn_female_ngochuyen_full_48k-fhg",
    "response_type": "indirect",
    "audio_type": "mp3",
    "sample_rate": 22050,
    "bitrate": 128,
    "speed_rate": 1.0,
    "callback_url": "https://example.com/callback"
}


### TTS API with Multiple Sentences
POST {{baseUrl}}/api/v1/tts
Authorization: Bearer {{token}}
app-id: {{appId}}
Content-Type: application/json

{
    "sentences": [
        {
            "input_text": "First sentence",
            "voice_code": "hn_female_ngochuyen_full_48k-fhg",
            "speed_rate": 1.0
        },
        {
            "input_text": "Second sentence",
            "voice_code": "hn_male_xuantin_full_48k-fhg",
            "speed_rate": 1.2
        }
    ],
    "response_type": "indirect",
    "audio_type": "mp3"
}

### Get TTS Request Status
GET {{baseUrl}}/api/v1/tts/{{requestId}}

### Get TTS Callback Result
GET {{baseUrl}}/api/v1/tts/{{requestId}}/callback-result




# ============================================================================
# APPLICATION MANAGEMENT ENDPOINTS
# App CRUD operations
# App request tracking
# Admin app management
# ============================================================================

### Create App
POST {{baseUrl}}/api/v1/apps
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "My TTS Application",
    "description": "Application for TTS services",
    "domain": "example.com"
}

### Get Apps
GET {{baseUrl}}/api/v1/apps
Authorization: Bearer {{token}}

### Get Specific App
GET {{baseUrl}}/api/v1/apps/{{appId}}
Authorization: Bearer {{token}}

### Update App
PUT {{baseUrl}}/api/v1/apps/{{appId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Updated App Name",
    "description": "Updated description"
}

### Get App Requests
GET {{baseUrl}}/api/v1/apps/{{appId}}/requests
Authorization: Bearer {{token}}

### Get Apps by Admin
GET {{baseUrl}}/api/v1/admin/apps
Authorization: Bearer {{token}}

### Sync App (Admin)
POST {{baseUrl}}/api/v1/admin/apps/sync
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "app_id": "{{appId}}"
}
