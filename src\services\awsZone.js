const awsZoneDao = require('../daos/awsZone');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const createAwsZone = async ({ name, weight, region, allowRequestTypes }) => {
  const checkExistAwsZone = await awsZoneDao.getAwsZone({ region });

  if (checkExistAwsZone) throw new CustomError(errorCodes.AWS_ZONE_EXIST);

  const newAwsZone = await awsZoneDao.createAwsZone({
    name,
    weight,
    region,
    allowRequestTypes,
  });

  return newAwsZone;
};

module.exports = { createAwsZone };
