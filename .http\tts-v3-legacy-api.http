
# ============================================================================
# TTS V3 API (Legacy/Old API)
# 📜 TTS V3 API (Legacy) (2 endpoints)
# Legacy V3 synthesis API
# V3 request status checking
# ============================================================================

### TTS V3 API Synthesis (Old API)
POST {{baseUrl}}/api/old/tts
Content-Type: application/json

{
    "app_id": "{{appId}}",
    "input_text": "Test message for V3 API",
    "voice": "hn_female_ngochuyen_full_48k-fhg",
    "type_response": "indirect",
    "audio_type": "mp3",
    "rate": 1.0
}

### Get V3 API Request Status
GET {{baseUrl}}/api/old/requests/{{requestId}}
