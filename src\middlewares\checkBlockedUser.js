const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const asyncMiddleware = require('./async');
const { AuthorizationService } = require('../services/authorization');

const checkBlockedUser = async (req, res, next) => {
  const { userId } = req.user;
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: can we throw if user not found?

  if (user?.isBlock) throw new CustomError(errorCodes.USER_BLOCK);

  return next();
};

const checkBlockedUserApi = async (req, res, next) => {
  const { members } = req.apiApp;
  if (!members) throw new CustomError(errorCodes.NOT_FOUND);

  // ADVISE: recheck this naming, see what is actual value of members[0].userId in runtime. It might not an userId string?
  const { userId: user } = members[0];
  if (!user) throw new CustomError(errorCodes.NOT_FOUND);

  const userDB = await AuthorizationService.getUser(user._id);
  if (userDB?.isBlock) throw new CustomError(errorCodes.USER_BLOCK);

  return next();
};
module.exports = {
  checkBlockedUser: asyncMiddleware(checkBlockedUser),
  checkBlockedUserApi: asyncMiddleware(checkBlockedUserApi),
};
