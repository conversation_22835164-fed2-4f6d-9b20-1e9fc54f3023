/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');

const Request = require('../../models/request');
const BackgroundMusic = require('../../models/backgroundMusic');
const logger = require('../../utils/logger');

const updateBackgroundMusic = async () => {
  logger.info('Starting update background music...', { ctx: 'RunScript' });

  const requests = await Request.find({ backgroundMusic: { $exists: true } })
    .select('backgroundMusic')
    .lean();

  await Promise.all(
    requests.map(async (req) => {
      const { _id, backgroundMusic: link, userId } = req;

      if (typeof link === 'string') {
        const bgExist = await BackgroundMusic.findOne({
          link,
          $or: [{ userId }, { userId: { $exists: false } }],
        });

        const backgroundMusic = { link, name: bgExist?.name };
        await Request.findByIdAndUpdate(_id, { backgroundMusic });
      }
    }),
  );

  logger.info('Create update background music successfully', {
    ctx: 'RunScript',
  });
};

(async () => {
  await updateBackgroundMusic();
  process.exit(1);
})();
