const TtsPreset = require('../models/ttsPreset');

const getTtsPresets = async (userId) => {
  const total = await TtsPreset.countDocuments({ userId });
  const ttsPresets = await TtsPreset.find({ userId }).lean();
  return { total, ttsPresets };
};

const getTtsPresetById = async (userId, ttsPresetId) => {
  const ttsPreset = await TtsPreset.findOne({
    _id: ttsPresetId,
    userId,
  }).lean();
  return ttsPreset;
};

const getTtsPresetByName = async ({ userId, name, excludeId }) => {
  const query = { userId, name };
  if (excludeId) query._id = { $ne: excludeId };
  const ttsPreset = await TtsPreset.findOne(query).lean();
  return ttsPreset;
};

const countTtsPresetByUserId = async (userId) => {
  const count = await TtsPreset.countDocuments({ userId });
  return count;
};

const createTtsPreset = async (createFields) => {
  const ttsPreset = await TtsPreset.create(createFields);
  return ttsPreset;
};

const updateTtsPreset = async ({ userId, ttsPresetId, updateFields }) => {
  const ttsPreset = await TtsPreset.findOneAndUpdate(
    { userId, _id: ttsPresetId },
    updateFields,
    {
      new: true,
    },
  );
  return ttsPreset;
};

const deleteTtsPreset = async (userId, ttsPresetId) => {
  await TtsPreset.findOneAndDelete({ userId, _id: ttsPresetId });
};

module.exports = {
  getTtsPresets,
  getTtsPresetById,
  getTtsPresetByName,
  countTtsPresetByUserId,
  createTtsPreset,
  updateTtsPreset,
  deleteTtsPreset,
};
