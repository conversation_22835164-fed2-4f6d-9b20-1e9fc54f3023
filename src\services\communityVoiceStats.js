const moment = require('moment');
const { getAggregateUserVoiceStats } = require('../daos/communityVoiceStats');
const {
  findVoiceCloningByCodes,
  findVoiceCloningsByNameForUser,
} = require('../daos/voiceCloning');

const getUserVoiceStats = async ({ userId, startDate, endDate, search }) => {
  const matchQuery = { voiceOwnerId: userId };
  let voiceCodes = [];
  let voiceList = [];

  if (search) {
    voiceList = await findVoiceCloningsByNameForUser(search, userId);
    voiceCodes = voiceList.map((v) => v.code);
  }

  if (startDate) {
    matchQuery.date = {
      ...matchQuery.date,
      $gte: moment(startDate).startOf('day').toDate(),
    };
  }
  if (endDate) {
    matchQuery.date = {
      ...matchQuery.date,
      $lte: moment(endDate).endOf('day').toDate(),
    };
  }
  if (search) {
    matchQuery.voiceCode = { $in: voiceCodes };
  }

  const userVoiceStats = await getAggregateUserVoiceStats(matchQuery);

  if (!search) {
    voiceCodes = userVoiceStats.communityVoices.map((v) => v.voiceCode);
    voiceList = await findVoiceCloningByCodes(voiceCodes);
  }

  const enrichedCommunityVoices = userVoiceStats.communityVoices.map((v) => {
    const voiceInfo = voiceList.find((voice) => voice.code === v.voiceCode);
    return { ...v, voice: voiceInfo };
  });

  return {
    summary: {
      ...userVoiceStats.summary,
      totalCommunityVoice: enrichedCommunityVoices.length,
    },
    communityVoices: enrichedCommunityVoices,
  };
};

module.exports = { getUserVoiceStats };
