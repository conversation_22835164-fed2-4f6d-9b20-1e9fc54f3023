const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const projectController = require('../controllers/project');
const {
  createProjectValidate,
  getProjectValidate,
  updateProjectValidate,
  deleteProjectValidate,
  getProjectsValidate,
  synthesisProjectValidate,
} = require('../validations/project');
const { auth } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.post('/projects', auth, createProjectValidate, asyncMiddleware(projectController.createProject));
router.get('/projects', auth, getProjectsValidate, asyncMiddleware(projectController.getProjects));
router.get('/projects/sample', auth, asyncMiddleware(projectController.getSampleProject));
router.get('/projects/:projectId', auth, getProjectValidate, asyncMiddleware(projectController.getProject));
router.post('/projects/synthesis', auth, synthesisProjectValidate, asyncMiddleware(projectController.synthesisProject));
router.put('/projects/:projectId', auth, updateProjectValidate, asyncMiddleware(projectController.updateProject));
router.delete('/projects/:projectId', auth, deleteProjectValidate, asyncMiddleware(projectController.deleteProject));
router.get('/projects/:projectId/audio', auth, asyncMiddleware(projectController.getAudioLink));
/* eslint-disable prettier/prettier */

module.exports = router;
