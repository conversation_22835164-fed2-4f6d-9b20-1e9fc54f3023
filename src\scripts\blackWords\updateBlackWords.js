require('dotenv').config();
require('../../models');

const BlackWord = require('../../models/blackWord');
const { initBlackWords } = require('../../services/init/blackWords');
const logger = require('../../utils/logger');

global.logger = logger;

const updateBlackWords = async (blackWords = []) => {
  try {
    await BlackWord.deleteMany({});
    await BlackWord.insertMany(blackWords);
    initBlackWords();
  } catch (error) {
    logger.error('Update black words failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update black words...`, {
    ctx: 'RunScript',
  });
  const blackWords = require('./seedBlackWords.json');
  await updateBlackWords(blackWords);
  logger.info(`Update black words successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
