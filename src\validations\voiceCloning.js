const { Joi, validate } = require('express-validation');
const {
  VOICE_STATUS,
  VOICE_LOCALE,
  VOICE_GENDER,
} = require('../constants/voiceCloning');
const { VOICE_CLONING_TYPE } = require('../constants');

const createVoiceCloning = {
  body: Joi.object({
    userId: Joi.string().trim().required(),
    code: Joi.string().trim().required(),
    name: Joi.string().trim().required(),
    gender: Joi.string()
      .valid(...Object.values(VOICE_GENDER))
      .required(),
    avatar: Joi.string().trim().required(),
    locale: Joi.string()
      .valid(...Object.values(VOICE_LOCALE))
      .required(),
    province: Joi.string().trim().required(),
    status: Joi.string()
      .valid(...Object.values(VOICE_STATUS))
      .required(),
    type: Joi.string()
      .optional()
      .valid(...Object.values(VOICE_CLONING_TYPE)),
    ttsGateCode: Joi.string().trim().optional().allow('', null),
    sampleLink: Joi.string().trim().optional().allow('', null),
  }),
};

module.exports = {
  createVoiceCloningValidate: validate(createVoiceCloning, {
    keyByField: true,
  }),
};
