const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const ttsController = require('../controllers/tts');
const { auth, hasRole } = require('../middlewares/auth');
const { createTtsPresetValidate } = require('../validations/ttsPreset');

/* eslint-disable prettier/prettier */
router.get('/admin/tts', auth, hasRole("view-requests"), asyncMiddleware(ttsController.getTts));
router.get('/tts-presets', auth, asyncMiddleware(ttsController.getTtsPresets));
router.post('/tts-presets', auth, createTtsPresetValidate, asyncMiddleware(ttsController.createTtsPreset));
router.get('/tts-presets/:ttsPresetId', auth, asyncMiddleware(ttsController.getTtsPresetById));
router.put('/tts-presets/:ttsPresetId', auth, createTtsPresetValidate, asyncMiddleware(ttsController.updateTtsPreset));
router.delete('/tts-presets/:ttsPresetId', auth, asyncMiddleware(ttsController.deleteTtsPreset));
/* eslint-disable prettier/prettier */

module.exports = router;
