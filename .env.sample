PORT=

PRODUCT=
NODE_ENV=
SERVER_ENV=
ENV=
LOG_LEVEL=

MONGO_URI=
REDIS_URI=


S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=

GCS_AICORE_TTS_CLIENT_EMAIL=
GCS_AICORE_TTS_PRIVATE_KEY=

PUBLIC_RSA_KEY_ENCRYPT_ACCESS_KEY=
PRIVATE_RSA_KEY_ENCRYPT_ACCESS_KEY=

KAFKA_BROKERS=
KAFKA_CLIENT_ID=
KAFKA_CONSUMER_GROUP_TTS=

IAM_URL=
IAM_REALM=
IAM_CLIENT_ID=
IAM_CLIENT_SECRET=
IAM_VALID_CLIENT_IDS=

MAX_DEMO_TTS=  // Max demo tts
MAX_DEMO_TTS_TTL =  // Time to reset number of demo tts (unit: hours)
MAX_DEMO_TTS_LENGTH=500
MAX_PREVIEW_TTS_LENGTH=2000

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
NORMALIZE_FUNCTION_NAME=

NOTIFICATION_URL=
ERROR_REPORT_SLACK_CHANNEL=

SILENCE_BREAK_LINE_AUDIO=

ACCOUNT_URL=https://dev.vbee.vn
CONSOLE_URL=
BACK_OFFICE_URL=
VBEE_URL= // Vbee URL, use for response api v3
VN_URL= // VN service URL, using for client block  foreign server
UPLOAD_URL=
PAYMENT_URL= // Vbee Payment URL
VBEE_DUBBING_URL= // Vbee Dubbing URL

API_TTS_VERSION_DEFAULT=
STUDIO_TTS_VERSION_DEFAULT=

PRIVATE_RSA_KEY=

SYNC_ENTERPRISE_SERVICE_URLS=

// Value is 0 or 1, 0 means running 1 zone, 1 means running all zones can work
MULTI_ZONE=

// Add zone and default function to avoid undefined error
DEFAULT_AWS_REGION=
DEFAULT_NORMALIZER_FUNCTION=
DEFAULT_SENTENCE_TOKENIZER_FUNCTION=
DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION=
DEFAULT_T2A_FUNCTION=
DEFAULT_SYNTHESIS_FUNCTION=
DEFAULT_JOIN_AUDIO_FUNCTION=
DEFAULT_BUCKET_S3=
DEFAULT_RETENTION_PERIOD=

API_CHARACTERS_RUN_OUT_NUMBER=
API_CHARACTERS_REACHED_ZERO_NUMBER=

# Recaptcha v3
RECAPTCHA_SECRET_KEY=
# Recaptcha Enterprise
RECAPTCHA_ENTERPRISE_PROJECT_ID=
RECAPTCHA_ENTERPRISE_CREDENTIAL_CLIENT_EMAIL=
RECAPTCHA_ENTERPRISE_CREDENTIAL_PRIVATE_KEY=
RECAPTCHA_ENTERPRISE_WEBSITE_SITE_KEY=
RECAPTCHA_ENTERPRISE_ANDROID_SITE_KEY=
RECAPTCHA_ENTERPRISE_IOS_SITE_KEY=
RECAPTCHA_ERROR_SLACK_CHANNEL=

GROWTH_BOOK_API_HOST=
GROWTH_BOOK_CLIENT_KEY=
LOADING_FEATURES_REALTIME_INTERVAL=1 // Minutes

DOWNSTREAM_HEALTHCHECK_INTERVAL=30000 // milliseconds
SYNC_ACCOUNT_FROM_IAM_INTERVAL=300000 // milliseconds

NEW_RELIC_ENABLED=
NEW_RELIC_LICENSE_KEY=
NEW_RELIC_APP_NAME=vbee-tts-api

NEW_RELIC_NO_CONFIG_FILE=true
NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
NEW_RELIC_LOG=stdout

MOE_APP_ID=
MOE_API_KEY=
MOE_DASHBOARD=

DATASENSES_URL=
DATASENSES_KEY=

STT_GATE_URL=
AIV_STT_CALLBACK_URL=
STT_APP_ID=
STT_TOKEN=
STT_S3_BUCKET=
STT_S3_REGION=
STT_S3_SECRET_ACCESS_KEY=
STT_S3_ACCESS_KEY_ID=

MICROSOFT_REGION=
MICROSOFT_SPEECH_KEY=

SENTRY_ENABLED=
SENTRY_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_PROFILES_SAMPLE_RATE=1.0
CLOUD_RUN_CLIENT_EMAIL=

CLOUD_RUN_PRIVATE_KEY=
CLOUD_RUN_JOIN_URL=
