/* eslint-disable import/no-dynamic-require */
module.exports = (app) => {
  require('fs')
    .readdirSync('src/routes')
    .forEach((fileName) => {
      if (fileName === 'index.js' || fileName === 'apiV3.js') return; // skip exceptions
      if (['js'].indexOf(fileName.split('.').pop()) === -1) return; // skip folder

      // exceptional route
      if (fileName === 'shortUrl.js') {
        app.use('/', require(`./${fileName}`));
        return;
      }

      // all normal routefiles
      app.use('/api/v1', require(`./${fileName}`));
    });
};

// ADVISE: Route Registration use file-based convention, but have few of exceptions. It's not intuitive.
/*
in General, Route Filename is not matter, not included in the final route string
Example: file "src/routes/system.js" register route "api/global" ==> final route will be /api/v1/api/global

The exception Make unnecessary complexity
Should use choose one or other approach: declarative or imperative
All route should be registered in one place (here)
*/
