const axios = require('axios');
require('dotenv').config();

const logger = require('../../utils/logger');
require('../../models');
const Tts = require('../../models/tts');
const {
  REQUEST_TYPE,
  REDIS_KEY_PREFIX,
  SYNC_CHARACTERS_EVENT,
  PACKAGE_CODE,
  SME_PACKAGE_CODES,
} = require('../../constants');

require('../../services/kafka/producer');
const Caching = require('../../caching');
const userService = require('../../services/user');

global.logger = logger;

const { MICROSOFT_REGION, MICROSOFT_SPEECH_KEY } = require('../../configs');

const updateUserRedis = async (userRequests) => {
  for (const userRequest of userRequests) {
    const { userId, numOfRequests, type, requests } = userRequest;

    // Update request in Cache
    const numPendAndInprReqKey =
      type === REQUEST_TYPE.STUDIO
        ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`
        : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_REQUESTS}_${userId}`;

    const numOfPendAndInprReqRedis = await Caching.RedisRepo.get(
      numPendAndInprReqKey,
    );

    const numOfPendAndInprReq = numOfPendAndInprReqRedis
      ? parseInt(numOfPendAndInprReqRedis, 10)
      : 0;

    const newNumOfPendAndInprReq =
      numOfPendAndInprReq > numOfRequests
        ? numOfPendAndInprReq - numOfRequests
        : 0;

    if (parseInt(numOfPendAndInprReq, 10) !== newNumOfPendAndInprReq) {
      await Caching.RedisRepo.set(numPendAndInprReqKey, newNumOfPendAndInprReq);
      logger.info(
        `Updated ${numPendAndInprReqKey} from ${numOfPendAndInprReq} to ${newNumOfPendAndInprReq}`,
        { ctx: 'RunScript' },
      );
    }

    // Update tts in Cache
    const numPendAndInprTtsKey =
      type === REQUEST_TYPE.STUDIO
        ? `${REDIS_KEY_PREFIX.STUDIO_PENDING_AND_INPROGRESS_TTS}_${userId}`
        : `${REDIS_KEY_PREFIX.API_PENDING_AND_INPROGRESS_TTS}_${userId}`;

    const numOfPendAndInprTtsRedis = await Caching.RedisRepo.get(
      numPendAndInprTtsKey,
    );

    const numOfPendAndInprTts = numOfPendAndInprTtsRedis
      ? parseInt(numOfPendAndInprTtsRedis, 10)
      : 0;

    if (numOfPendAndInprTts) {
      for (const request of requests) {
        const numOfTts = await Tts.countDocuments({ requestId: request._id });

        const newNumOfPendAndInprTts =
          numOfPendAndInprTts > numOfTts ? numOfPendAndInprTts - numOfTts : 0;

        await Caching.RedisRepo.set(
          numPendAndInprTtsKey,
          newNumOfPendAndInprTts,
        );
        logger.info(
          `Updated ${numPendAndInprTtsKey} from ${numOfPendAndInprTts} to ${newNumOfPendAndInprTts}`,
          { ctx: 'RunScript' },
        );
      }
    }
  }
};

const refundCharacters = async (userRequests) => {
  for (const userRequest of userRequests) {
    const { userId, requests } = userRequest;

    for (const request of requests) {
      const packageCodes = [...SME_PACKAGE_CODES, PACKAGE_CODE.STUDIO_TRIAL];
      if (!packageCodes.includes(request.packageCode)) {
        await userService.updateByCharacters({
          event: SYNC_CHARACTERS_EVENT.REFUND,
          userId,
          requestId: request._id,
          characters: request.characters,
        });
      }
    }
    logger.info(`Refunded characters for user ${userId}`, { ctx: 'RunScript' });
  }
};

const getMicrosoftVoices = async () => {
  const response = await axios.get(
    `https://${MICROSOFT_REGION}.tts.speech.microsoft.com/cognitiveservices/voices/list`,
    {
      headers: {
        'Ocp-Apim-Subscription-Key': MICROSOFT_SPEECH_KEY,
      },
    },
  );

  const voices = response.data;
  const readyVoice = voices.filter((voice) => voice.Status === 'GA');

  return readyVoice;
};

const getSquareImage = (gender) =>
  gender === 'Male'
    ? 'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/male.png'
    : 'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/female.png';

const getRoundImage = (gender) =>
  gender === 'Male'
    ? 'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/male.png'
    : 'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/female.png';

const getVoiceFeatures = (voiceType) => {
  if (voiceType === 'Standard') return [GLOBAL_VOICE_FEATURES.STANDARD];

  return [GLOBAL_VOICE_FEATURES.STANDARD, GLOBAL_VOICE_FEATURES.PREMIUM];
};

module.exports = {
  updateUserRedis,
  refundCharacters,

  getMicrosoftVoices,

  getSquareImage,
  getRoundImage,
  getVoiceFeatures,
};
