const { Joi, validate } = require('express-validation');
const { ObjectId } = require('mongoose').Types;
const { Constants } = require('@vbee-holding/vbee-tts-models');
const { PRODUCT_TYPE } = require('../constants');

const createProject = {
  body: Joi.object({
    title: Joi.string().trim().required(),
    product: Joi.string()
      .trim()
      .valid(...Object.values(PRODUCT_TYPE))
      .required(),
    blocks: Joi.array()
      .items(
        Joi.object({
          id: Joi.string(),
          text: Joi.string(),
          voiceCode: Joi.string(),
          characters: Joi.number(),
          breakTime: Joi.number(),
          speed: Joi.number(),
          audioLink: Joi.string(),
          elements: Joi.array().items(
            Joi.object({
              key: Joi.string(),
              text: Joi.string(),
              startOffset: Joi.number(),
              endOffset: Joi.number(),
              name: Joi.string(),
              value: Joi.string(),
            }),
          ),
        }),
      )
      .required(),
    isDeleted: Joi.boolean(),
  }),
};

const getProject = {
  params: Joi.object({
    projectId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('projectId is invalid');
      }),
  }),
};

const updateProject = {
  body: Joi.object({
    title: Joi.string().trim().required(),
    blocks: Joi.array()
      .items(
        Joi.object({
          id: Joi.string(),
          text: Joi.string(),
          voiceCode: Joi.string(),
          characters: Joi.number(),
          breakTime: Joi.number(),
          speed: Joi.number(),
          elements: Joi.array().items(
            Joi.object({
              key: Joi.string(),
              text: Joi.string(),
              startOffset: Joi.number(),
              endOffset: Joi.number(),
              name: Joi.string(),
              value: Joi.string(),
            }),
          ),
          isSample: Joi.boolean(),
          sampleBlockId: Joi.string(),
        }),
      )
      .required(),
    audioType: Joi.string()
      .trim()
      .valid(...Object.values(Constants.AUDIO_TYPE))
      .optional(),
  }),
  params: Joi.object({
    projectId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('projectId is invalid');
      }),
  }),
};

const deleteProject = {
  params: Joi.object({
    projectId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('projectId is invalid');
      }),
  }),
};

const getProjects = {
  query: Joi.object({
    limit: Joi.number().optional(),
    offset: Joi.number().optional(),
    sort: Joi.string().optional(),
    search: Joi.string().optional(),
  }),
};

const synthesisProject = {
  body: Joi.object({
    projectId: Joi.string()
      .required()
      .custom((value, helper) => {
        if (ObjectId.isValid(value)) return true;
        return helper.message('projectId is invalid');
      }),
    blocks: Joi.array()
      .items(
        Joi.object({
          id: Joi.string(),
          text: Joi.string(),
          voiceCode: Joi.string(),
          audioType: Joi.string(),
          speed: Joi.number(),
        }),
      )
      .required(),
  }),
};

module.exports = {
  createProjectValidate: validate(createProject, { keyByField: true }),
  updateProjectValidate: validate(updateProject, { keyByField: true }),
  getProjectValidate: validate(getProject, { keyByField: true }),
  deleteProjectValidate: validate(deleteProject, { keyByField: true }),
  getProjectsValidate: validate(getProjects, { keyByField: true }),
  synthesisProjectValidate: validate(synthesisProject, { keyByField: true }),
};
