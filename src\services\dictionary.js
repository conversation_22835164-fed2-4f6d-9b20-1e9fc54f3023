const dictionaryDao = require('../daos/dictionary');
const userDao = require('../daos/user');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');
const { VIETNAMESE_LETTERS } = require('../constants');
const { AuthorizationService } = require('./authorization');

const checkValidateWord = (word) => {
  const wordRegexString = `^[a-zA-Z${VIETNAMESE_LETTERS}]+((-|&)?[a-zA-Z${VIETNAMESE_LETTERS}]+)*(\\.)?$`;
  const wordRegex = new RegExp(wordRegexString, 'i');
  return !!word.match(wordRegex);
};

const checkValidatePronunciation = (pronunciation) => {
  const pronunciationRegexString = `^[a-zA-Z${VIETNAMESE_LETTERS}]+((-|\\s)?[a-zA-Z${VIETNAMESE_LETTERS}]+)+$`;
  const pronunciationRegex = new RegExp(pronunciationRegexString, 'i');
  return !!pronunciation.match(pronunciationRegex);
};

const checkValidateAWord = (words, word, index) => {
  const isDuplicate = !!words.find(
    (item, itemIndex) =>
      item.word.trim().toLowerCase() === word.word.trim().toLowerCase() &&
      itemIndex !== index,
  );
  if (isDuplicate) return false;

  if (
    !checkValidateWord(word.word) ||
    !checkValidatePronunciation(word.pronunciation)
  )
    return false;

  return true;
};

const checkValidateDictionary = (words) => {
  if (words.length === 0) return true;
  return words.every((word, index) => checkValidateAWord(words, word, index));
};

const createDictionary = async (userId, words) => {
  if (!checkValidateDictionary)
    throw new CustomError(errorCodes.BAD_REQUEST, 'Dictionary invalid');

  const dictionaryExist = await dictionaryDao.findDictionary(userId);

  if (dictionaryExist) {
    const { _id } = dictionaryExist;
    await dictionaryDao.updateDictionary(_id, { words });
  } else {
    await dictionaryDao.createDictionary({ userId, words });
  }
};

const getUniqueWords = (currWords, newWords) => {
  const uniqueWords = newWords.filter(
    (newWord) =>
      !currWords.some(
        (item) =>
          item.word.trim().toLowerCase() === newWord.word.trim().toLowerCase(),
      ),
  );

  return [...currWords, ...uniqueWords];
};

const syncDictionary = async (userId, dictionary) => {
  const user = await AuthorizationService.getUser(userId, false);
  // ADVISE: can we throw if user not found? nothing to update

  if (user?.isSyncDictionary) return;

  const dictionaryExist = await dictionaryDao.findDictionary(userId);

  if (!dictionaryExist) {
    await dictionaryDao.createDictionary({ userId, words: dictionary });
  } else {
    const { _id: dictionaryId } = dictionaryExist;
    const studioWords = dictionaryExist.words || [];
    const words = getUniqueWords(studioWords, dictionary);
    await dictionaryDao.updateDictionary(dictionaryId, { words });
  }
  await userDao.updateUserById(userId, { isSyncDictionary: true });
};

module.exports = { createDictionary, syncDictionary };
