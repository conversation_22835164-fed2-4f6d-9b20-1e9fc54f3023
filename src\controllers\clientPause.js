const clientPauseDao = require('../daos/clientPause');
const clientPauseService = require('../services/clientPause');

const getClientPause = async (req, res) => {
  const { userId } = req.user;
  const clientPause = await clientPauseDao.findClientPause(userId);

  return res.send({ clientPause });
};

const createClientPause = async (req, res) => {
  const { userId } = req.user;
  const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } = req.body;

  await clientPauseService.createClientPause(userId, {
    paragraphBreak,
    sentenceBreak,
    majorBreak,
    mediumBreak,
  });

  return res.send({});
};

module.exports = { getClientPause, createClientPause };
