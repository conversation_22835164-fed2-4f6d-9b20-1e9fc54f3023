const daoUtils = require('./utils');
const Dictionary = require('../models/dictionary');

/** find in DB the Dictionary only belong to specific user */
const findDictionary = async (userId) => {
  const dictionary = await daoUtils.findOne(Dictionary, { userId });
  return dictionary;
};

const createDictionary = async (createFields) => {
  const dictionary = await Dictionary.create(createFields);
  return dictionary;
};

const updateDictionary = async (id, updateFields) => {
  const dictionary = await Dictionary.findByIdAndUpdate(id, updateFields, {
    new: true,
  });
  return dictionary;
};

module.exports = { findDictionary, createDictionary, updateDictionary };
