require('dotenv').config();
require('../../models');

const Language = require('../../models/language');
const logger = require('../../utils/logger');
const msChineseLanguage = require('./chineseLanguages.json');

global.logger = logger;

const addMSChineseLanguage = async () => {
  try {
    await Promise.all(
      msChineseLanguage.map(async (lang) => {
        await Language.updateOne({ code: lang.code }, lang, { upsert: true });
      }),
    );
  } catch (error) {
    logger.error('Add MS Chinese Languages', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting add MS Chinese Languages...`, {
    ctx: 'RunScript',
  });
  await addMSChineseLanguage();
  logger.info(`Add MS Chinese Languages successfully`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
