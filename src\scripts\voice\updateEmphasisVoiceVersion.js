require('dotenv').config();

const logger = require('../../utils/logger');

global.logger = logger;

require('../../models');
const Voice = require('../../models/voice');
const { TTS_CORE_VERSION } = require('../../constants');

(async () => {
  logger.info(`Starting update version emphasis voice ...`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  });

  await Voice.updateMany(
    {
      styles: ['emphasis'],
      version: TTS_CORE_VERSION.NEW,
    },
    {
      $set: {
        version: TTS_CORE_VERSION.OLD,
      },
    },
  );

  logger.info(`Update version emphasis voice successfully`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  });
  process.exit(1);
})();
