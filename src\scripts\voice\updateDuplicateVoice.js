require('dotenv').config();
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');

global.logger = logger;

require('../../models');
const Voice = require('../../models/voice');
const { VOICE_LEVEL } = require('../../constants');

const IMPORT_FILE_PATH = 'duplicate-voice-most-usage.csv';
const EXPORT_FILE_PATH = 'voice_update_codes.csv';

const exportVoiceCodesToCSV = (updates, outputPath = EXPORT_FILE_PATH) => {
  const headers = ['code'];
  const rows = updates.map((item) => item.code);

  const csvContent = [headers.join(','), ...rows].join('\n');
  fs.writeFileSync(path.resolve(outputPath), csvContent, 'utf8');
};

const exportUpdatesToJSON = (updates, outputPath = 'voice_updates.json') => {
  fs.writeFileSync(
    path.resolve(outputPath),
    JSON.stringify(updates, null, 2),
    'utf8',
  );
};

const processVoices = async () => {
  const csvLines = fs
    .readFileSync(IMPORT_FILE_PATH, 'utf8')
    .split('\n')
    .slice(1)
    .filter((line) => line.trim());

  const fileVoices = csvLines.map((line) => {
    const [group, voiceCode, totalChars] = line.split(',');
    return { group, voiceCode, totalChars: Number(totalChars) };
  });

  const voicesToImport = fileVoices.filter((v) => !v.voiceCode.includes('New'));

  const voiceCodes = voicesToImport.map((v) => v.voiceCode);
  const voicesInDB = await Voice.find({
    code: { $in: voiceCodes },
    active: true,
    $or: [{ eolDate: { $exists: false } }, { eolDate: null }],
  })
    .select({ code: 1, level: 1, active: 1 })
    .lean();

  const mergedVoices = voicesToImport.map((v) => {
    const dbVoice = voicesInDB.find((dbv) => dbv.code === v.voiceCode);
    return dbVoice ? { ...v, ...dbVoice } : v;
  });

  const validVoices = mergedVoices.filter((v) => v._id);

  const voicesByGroup = {};
  for (const voice of validVoices) {
    if (!voicesByGroup[voice.group]) {
      voicesByGroup[voice.group] = [];
    }
    voicesByGroup[voice.group].push(voice);
  }

  const updates = [];

  for (const groupVoices of Object.values(voicesByGroup)) {
    const advancedVoices = groupVoices.filter((v) => v.level === 'ADVANCED');
    const basicVoices = groupVoices.filter((v) => v.level === 'BASIC');

    const bestAdvanced = advancedVoices.reduce(
      (best, v) => (v.totalChars > (best?.totalChars || 0) ? v : best),
      null,
    );
    const bestBasic = basicVoices.reduce(
      (best, v) => (v.totalChars > (best?.totalChars || 0) ? v : best),
      null,
    );

    for (const voice of groupVoices) {
      if (
        voice.level === VOICE_LEVEL.ADVANCED &&
        bestAdvanced &&
        voice.voiceCode !== bestAdvanced.voiceCode
      ) {
        updates.push({
          code: voice.code,
          update: {
            active: false,
            canonicalVoiceCode: bestAdvanced.voiceCode,
          },
        });
      }
      if (
        voice.level === VOICE_LEVEL.BASIC &&
        bestBasic &&
        voice.voiceCode !== bestBasic.voiceCode
      ) {
        updates.push({
          code: voice.code,
          update: {
            active: false,
            canonicalVoiceCode: bestBasic.voiceCode,
          },
        });
      }
    }
  }

  exportVoiceCodesToCSV(updates);
  exportUpdatesToJSON(updates);

  if (updates.length > 0) {
    const bulkOps = updates.map(({ code, update }) => ({
      updateOne: {
        filter: { code },
        update: { $set: update },
      },
    }));

    await Voice.bulkWrite(bulkOps);
  }
};

const rollback = async () => {
  const csvLines = fs
    .readFileSync(EXPORT_FILE_PATH, 'utf8')
    .split('\n')
    .slice(1)
    .filter((line) => line.trim());

  const fileVoices = csvLines.map((line) => {
    const [code] = line.split(',');
    return { code };
  });

  const rollbackOps = fileVoices.map(({ code }) => ({
    updateOne: {
      filter: { code },
      update: { $set: { active: true, canonicalVoiceCode: null } },
    },
  }));

  await Voice.bulkWrite(rollbackOps);
};

const main = async () => {
  await processVoices();
  // await rollback();
};

main()
  .then(() => {
    logger.info('Update Google voices completed successfully', {
      ctx: 'UpdateGoogleVoices',
    });
  })
  .catch((error) => {
    logger.error(error, { ctx: 'UpdateGoogleVoices' });
  })
  .finally(() => {
    process.exit();
  });
