const multer = require('multer');
const path = require('path');

const errorCodes = require('../errors/code');
const CustomError = require('../errors/CustomError');
const uploadSttService = require('../services/stt/upload');

const filetypes = /wav|audio\/vnd.wave/;
const fileExts = /wav/;

const storage = multer.diskStorage({
  destination(req, file, cb) {
    const dir = uploadSttService.getUploadDir();
    cb(null, dir);
  },
  filename(req, file, cb) {
    // cb(null, file.fieldname + '-' + Date.now())
    const fileName = uploadSttService.getUploadFileName(file);
    cb(null, fileName);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 1024 * 1024 * 4, // 4MB
  },
  fileFilter: (req, file, cb) => {
    // console.log('file.mimetype', file.mimetype);
    // console.log(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    const extname = fileExts.test(
      path.extname(file.originalname).toLowerCase(),
    );
    if (mimetype && extname) {
      return cb(null, true);
    }
    return cb(
      new CustomError(
        errorCodes.INVALID_FILE_TYPE,
        `File upload only support the following filetypes: ${filetypes}`,
      ),
    );
  },
});

// eslint-disable-next-line consistent-return
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    /* eslint-disable prettier/prettier */
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        return next(new CustomError(errorCodes.LIMIT_FILE_SIZE, err.message));
      case 'LIMIT_PART_COUNT':
        return next(new CustomError(errorCodes.LIMIT_PART_COUNT, err.message));
      case 'LIMIT_FILE_COUNT':
        return next(new CustomError(errorCodes.LIMIT_FILE_COUNT, err.message));
      case 'LIMIT_FIELD_KEY':
        return next(new CustomError(errorCodes.LIMIT_FIELD_KEY, err.message));
      case 'LIMIT_FIELD_VALUE':
        return next(new CustomError(errorCodes.LIMIT_FIELD_VALUE, err.message));
      case 'LIMIT_FIELD_COUNT':
        return next(new CustomError(errorCodes.LIMIT_FIELD_COUNT, err.message));
      case 'LIMIT_UNEXPECTED_FILE':
        return next(
          new CustomError(errorCodes.LIMIT_UNEXPECTED_FILE, err.message),
        );
      default:
        return next(new CustomError(errorCodes.ERROR_UPLOAD, err.message));
      /* eslint-enable prettier/prettier */
    }
  } else if (err) {
    return next(err);
  }
};

const uploadSingle = (fieldName) => {
  return (req, res, next) => {
    upload.single(fieldName)(req, res, (err) => {
      if (err) handleUploadError(err, req, res, next);
      else {
        const { file } = req;
        logger.debug(`Upload file at path ${file ? file.path : null}`, {
          ctx: 'multerMiddleware.uploadSingle',
        });
        next();
      }
    });
  };
};

module.exports = {
  uploadSingle,
};
