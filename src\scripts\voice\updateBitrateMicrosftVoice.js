require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');
const { VOICE_PROVIDER } = require('../../constants');
const { generateMSVoiceSampleRates } = require('../../utils/sampleRate');

global.logger = logger;

const updateBitrateMicrosoftVoice = async () => {
  const voices = await Voice.find({ provider: VOICE_PROVIDER.MICROSOFT });
  logger.info(`Get ${voices.length} Microsoft voices successfully`, {
    ctx: 'UpdateBitrateMicrosoftVoice',
  });

  for (const voice of voices) {
    const sampleRates = generateMSVoiceSampleRates(voice.defaultSampleRate);
    await Voice.updateOne({ _id: voice._id }, { sampleRates });
  }

  logger.info(`Update bitrate Microsoft voices successfully`, {
    ctx: 'UpdateBitrateMicrosoftVoice',
  });
};

(async () => {
  await updateBitrateMicrosoftVoice();
  process.exit(1);
})();
