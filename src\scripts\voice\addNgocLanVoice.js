require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const voice = {
  code: 'hn_female_hermer_stor_48k-fhg',
  name: '<PERSON><PERSON> - <PERSON>',
  gender: 'female',
  languageCode: 'vi-VN',
  type: 'Neural TTS',
  provider: 'vbee',
  squareImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ngoc-lan.png',
  roundImage:
    'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ngoc-lan.png',
  demo: 'https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/audios/samples/ngoc-lan.mp3',
  rank: 4,
  synthesisFunction: 'a2s-phg-dev-hermer-stor-phg',
  active: true,
  defaultSampleRate: 22050,
  sampleRates: [8000, 16000, 22050],
  level: 'BASIC',
  styles: ['story'],
  beta: true,
};

(async () => {
  logger.info(`Starting create new voice...`, { ctx: 'RunScript' });
  await Voice.create(voice);
  const voices = await Voice.find({});
  global.VOICES = voices;
  logger.info(`Create new voice successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
