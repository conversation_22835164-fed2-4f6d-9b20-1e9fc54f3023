require('dotenv').config();
const moment = require('moment');

const logger = require('../../utils/logger');

global.logger = logger;

require('../../models');
const Voice = require('../../models/voice');
const { VOICE_PROVIDER } = require('../../constants');
const { GLOBAL_VOICE_FEATURES } = require('../../constants/voice');

const EOL_VOICES = [
  'en-US-Wavenet-D',
  'en-US-Wavenet-F',
  'en-GB-Wavenet-A',
  'en-US-Neural2-J',
  'en-US-Wavenet-J',
  'ko-KR-Wavenet-C',
  'en-US-Neural2-D',
  'ko-KR-Wavenet-B',
  'en-US-Wavenet-C',
  'en-US-Neural2-C',
  'en-US-Neural2-F',
  'en-GB-Wavenet-B',
  'ja-JP-Wavenet-B',
  'en-US-Wavenet-B',
  'en-US-Neural2-G',
  'en-GB-Wavenet-D',
  'en-US-Neural2-I',
  'ko-KR-Neural2-C',
  'en-US-Neural2-A',
  'ja-JP-Neural2-B',
  'en-US-Wavenet-I',
  'en-US-Neural2-E',
  'en-US-Neural2-H',
  'ja-JP-Neural2-D',
  'ja-JP-Wavenet-D',
  'ko-KR-Wavenet-A',
  'ko-KR-Wavenet-D',
  'es-ES-Neural2-F',
  'es-ES-Neural2-G',
  'ko-KR-Neural2-A',
  'en-GB-Wavenet-C',
  'en-GB-Wavenet-F',
  'th-TH-Neural2-C',
  'en-US-Wavenet-E',
  'ko-KR-Neural2-B',
  'cmn-CN-Wavenet-C',
  'en-US-Wavenet-H',
  'ja-JP-Wavenet-C',
  'ja-JP-Neural2-C',
  'cmn-CN-Wavenet-B',
  'cmn-CN-Wavenet-A',
  'gu-IN-Wavenet-A',
  'en-GB-Neural2-O',
  'es-ES-Neural2-H',
  'en-AU-Wavenet-B',
  'de-DE-Neural2-G',
  'cmn-CN-Wavenet-D',
  'ja-JP-Wavenet-A',
  'fr-FR-Neural2-G',
  'fil-ph-Neural2-A',
  'en-GB-Wavenet-O',
  'fil-PH-Wavenet-A',
  'en-US-Wavenet-A',
  'de-DE-Neural2-H',
  'ms-MY-Wavenet-A',
  'en-IN-Wavenet-D',
  'fr-FR-Wavenet-F',
  'ms-MY-Wavenet-C',
  'es-ES-Wavenet-B',
  'it-IT-Neural2-F',
  'ru-RU-Wavenet-D',
  'en-AU-Neural2-B',
  'hi-IN-Neural2-D',
  'ar-XA-Wavenet-A',
  'fil-ph-Neural2-D',
  'es-ES-Neural2-E',
  'de-DE-Wavenet-F',
  'pt-PT-Wavenet-A',
  'en-IN-Wavenet-C',
  'fil-PH-Wavenet-B',
  'es-ES-Wavenet-E',
  'es-ES-Neural2-A',
  'it-IT-Neural2-A',
  'en-GB-Wavenet-N',
  'fr-FR-Wavenet-B',
  'nb-NO-Wavenet-G',
  'sv-SE-Wavenet-B',
  'fr-FR-Wavenet-E',
  'en-AU-Neural2-C',
  'ms-MY-Wavenet-B',
  'en-GB-Neural2-N',
  'pt-BR-Wavenet-B',
  'es-US-Neural2-A',
  'es-US-Wavenet-C',
  'pt-BR-Neural2-B',
  'en-US-Wavenet-G',
  'fi-FI-Wavenet-A',
  'es-ES-Wavenet-D',
  'ms-MY-Wavenet-D',
  'fil-PH-Wavenet-C',
  'fil-PH-Wavenet-D',
  'es-US-Wavenet-A',
  'pt-BR-Wavenet-D',
  'en-AU-Neural2-D',
  'de-DE-Wavenet-A',
  'fr-FR-Neural2-F',
  'ru-RU-Wavenet-C',
  'es-US-Neural2-C',
  'en-AU-Wavenet-C',
  'es-ES-Wavenet-C',
  'hi-IN-Neural2-C',
  'de-DE-Wavenet-B',
  'id-ID-Wavenet-A',
  'en-AU-Wavenet-D',
  'de-DE-Wavenet-C',
  'fr-FR-Wavenet-A',
  'ar-XA-Wavenet-B',
  'hi-IN-Wavenet-F',
  'es-ES-Wavenet-H',
  'ar-XA-Wavenet-C',
  'fr-FR-Wavenet-D',
  'pt-BR-Wavenet-E',
  'id-ID-Wavenet-C',
  'en-IN-Wavenet-B',
  'de-DE-Wavenet-E',
  'pt-PT-Wavenet-F',
  'de-DE-Wavenet-D',
  'hi-IN-Wavenet-B',
  'fr-FR-Wavenet-C',
  'id-ID-Wavenet-D',
  'pt-PT-Wavenet-C',
  'es-US-Neural2-B',
];

const TOP_VOICES = [
  'en-US-Wavenet-D',
  'en-US-Wavenet-F',
  'en-US-Neural2-J',
  'en-US-Neural2-D',
  'ko-KR-Wavenet-C',
  'en-US-Wavenet-J',
  'es-ES-Neural2-G',
  'ko-KR-Wavenet-B',
  'en-GB-Wavenet-D',
  'en-US-Neural2-C',
  'es-ES-Neural2-H',
  'en-GB-Wavenet-B',
  'en-US-Wavenet-C',
  'es-ES-Neural2-F',
  'en-US-Neural2-E',
  'en-US-Wavenet-B',
  'ja-JP-Wavenet-B',
  'en-GB-Wavenet-A',
  'en-AU-Wavenet-B',
  'en-US-Neural2-F',
];

const updateFeatures = async () => {
  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.GOOGLE,
      code: { $in: EOL_VOICES },
    },
    [
      {
        $set: {
          features: [GLOBAL_VOICE_FEATURES.PREMIUM],
        },
      },
    ],
  );
};

const updateEOLDate = async () => {
  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.GOOGLE,
      code: { $in: TOP_VOICES },
    },
    { eolDate: moment().add(2, 'months').endOf('day').toDate() },
  );
  await Voice.updateMany(
    {
      provider: VOICE_PROVIDER.GOOGLE,
      code: {
        $in: EOL_VOICES,
        $not: { $in: TOP_VOICES },
      },
    },
    { eolDate: moment().add(1, 'month').endOf('day').toDate() },
  );
};

const createVoices = async () => {
  const voices = await Voice.find({ code: { $in: EOL_VOICES } }).lean();
  const newVoices = voices.map((voice) => {
    const newVoice = { ...voice };
    newVoice._id = undefined;
    newVoice.code = `${voice.code}-New`;
    newVoice.ttsGateCode = voice.code;
    newVoice.name = `${voice.name}-VIP`;
    newVoice.features = [GLOBAL_VOICE_FEATURES.ADVANCED];
    newVoice.eolDate = undefined;
    return newVoice;
  });

  await Voice.bulkWrite(
    newVoices.map((voice) => ({
      updateOne: {
        filter: { code: voice.code },
        update: { $set: voice },
        upsert: true,
      },
    })),
  );
};

const main = async () => {
  await updateFeatures(EOL_VOICES);
  await updateEOLDate(EOL_VOICES, TOP_VOICES);
  await createVoices(EOL_VOICES);
};

main()
  .then(() => {
    logger.info('Done', { ctx: 'UpdateGoogleVoices' });
  })
  .catch((err) => {
    logger.error(err, { ctx: 'UpdateGoogleVoices' });
  })
  .finally(() => {
    process.exit();
  });
