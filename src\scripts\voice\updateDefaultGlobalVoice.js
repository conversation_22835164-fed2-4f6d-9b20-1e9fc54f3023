require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update default global voice...`, { ctx: 'RunScript' });

  const voices = require('./seedVoiceGlobal.json');
  for (const voice of voices) {
    await Voice.findOneAndUpdate({ code: voice.code }, voice);
  }

  const updatedVoices = await Voice.find({});
  global.VOICES = updatedVoices;

  logger.info(`Update default global voice successfully`, { ctx: 'RunScript' });
  process.exit(1);
})();
