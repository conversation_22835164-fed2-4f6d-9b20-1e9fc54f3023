/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');

const { PACKAGE_TYPE } = require('../../constants');
const User = require('../../models/user');
const logger = require('../../utils/logger');

global.logger = logger;

const addPackageFeaturesToUsers = async (packages = []) => {
  for (const pkg of packages) {
    try {
      const { code, type, features } = pkg;

      logger.info(`Updating features for ${code}`, { ctx: 'RunScript' });

      switch (type) {
        case PACKAGE_TYPE.STUDIO:
          await User.updateMany({ packageCode: code }, { features });
          break;

        case PACKAGE_TYPE.API:
          await User.updateMany(
            { 'apiPackage.packageCode': code },
            { 'apiPackage.features': features },
          );
          break;

        default:
          break;
      }
    } catch (error) {
      logger.error(`Update features for ${pkg.code} failed`, {
        ctx: 'RunScript',
        stack: error.stack,
      });
      throw new Error(error);
    }
  }
};

(async () => {
  logger.info('Starting add package features to user...', { ctx: 'RunScript' });
  const packages = require('./seedPackageFeatures.json');
  await addPackageFeaturesToUsers(packages);
  logger.info('Add package features to user successfully', {
    ctx: 'RunScript',
  });

  process.exit(1);
})();
