const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const clientPauseController = require('../controllers/clientPause');
const { auth } = require('../middlewares/auth');
const { createClientPauseValidate } = require('../validations/clientPause');

/* eslint-disable prettier/prettier */
router.get('/client-pause', auth, asyncMiddleware(clientPauseController.getClientPause));
router.post('/client-pause', auth, createClientPauseValidate, asyncMiddleware(clientPauseController.createClientPause));
/* eslint-disable prettier/prettier */

module.exports = router;
