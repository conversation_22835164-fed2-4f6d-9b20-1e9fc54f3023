require('dotenv').config();
require('../../models');

const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

const UPDATE_BETA_VOICES_CODES = [
  'hn_male_manhdung_news_48k-phg',
  'hn_male_phuthang_stor80dt_48k-fhg',
];

const updateBetaVoicesToOfficialVoices = async () => {
  logger.info(`Starting update beta voices...`, { ctx: 'RunScript' });

  await Voice.updateMany(
    { code: { $in: UPDATE_BETA_VOICES_CODES } },
    { beta: false },
  );

  logger.info(`Starting update beta voices...`, { ctx: 'RunScript' });
};

(async () => {
  await updateBetaVoicesToOfficialVoices();
  process.exit(1);
})();
