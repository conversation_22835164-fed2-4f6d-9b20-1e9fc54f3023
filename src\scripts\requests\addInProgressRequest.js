/* eslint-disable no-underscore-dangle */
require('dotenv').config();
require('../../models');
const Request = require('../../models/request');
const InProgressRequest = require('../../models/inProgressRequest');
const { REQUEST_STATUS } = require('../../constants');
const logger = require('../../utils/logger');

const addInProgressRequests = async () => {
  logger.info('Starting add in progress requests...', { ctx: 'RunScript' });

  const requests = await Request.find({ status: REQUEST_STATUS.IN_PROGRESS })
    .select('createdAt')
    .lean();

  const inProgressRequests = await InProgressRequest.find({}).lean();

  const validRequests = requests.filter((request) =>
    inProgressRequests.every((item) => item._id !== request._id),
  );

  await InProgressRequest.insertMany(validRequests);

  logger.info('Add in progress requests successfully', { ctx: 'RunScript' });
};

(async () => {
  await addInProgressRequests();
  process.exit(1);
})();
