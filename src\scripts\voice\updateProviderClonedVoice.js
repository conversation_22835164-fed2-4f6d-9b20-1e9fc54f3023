require('dotenv').config();
require('../../models');

const { VOICE_PROVIDER } = require('../../constants');
const VoiceCloning = require('../../models/voiceCloning');
const logger = require('../../utils/logger');

global.logger = logger;

const updateProviderClonedVoice = async () => {
  try {
    await VoiceCloning.updateMany(
      { provider: { $exists: false } },
      { $set: { provider: VOICE_PROVIDER.VBEE_VOICE_CLONING } },
    );
  } catch (error) {
    logger.error('Update provider cloned voice failed', {
      ctx: 'RunScript',
      stack: error.stack,
    });
  }
};

(async () => {
  logger.info(`Starting update provider cloned voice...`, {
    ctx: 'RunScript',
  });
  await updateProviderClonedVoice();
  logger.info(`Update provider cloned voice`, {
    ctx: 'RunScript',
  });
  process.exit(1);
})();
