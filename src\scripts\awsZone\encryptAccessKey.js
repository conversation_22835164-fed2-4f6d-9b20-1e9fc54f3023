const NodeRSA = require('node-rsa');
// ADVISE: consider to remove this, and use built-in encryption
// Node.js Built-in Crypto Module: The crypto module is a core Node.js module providing cryptographic functionalities, including RSA key generation, encryption, and decryption. This is often the preferred choice for its native integration and performance.

const generateKeyPairs = () => {
  const key = new NodeRSA({ b: 2048 });
  const publicKey = key.exportKey('pkcs1-public-pem');
  const privateKey = key.exportKey('pkcs1-private-pem');
  return { publicKey, privateKey };
};

const encryptAccessKey = ({ accessKey, publicKey }) => {
  const key = new NodeRSA(publicKey);
  key.setOptions({ encryptionScheme: 'pkcs1' });
  const encrypted = key.encrypt(accessKey, 'base64');
  return encrypted;
};

const decryptAccessKey = ({ encryptedAccessKey, privateKey }) => {
  const key = new NodeRSA(privateKey);
  key.setOptions({ encryptionScheme: 'pkcs1', environment: 'browser' });
  const decrypted = key.decrypt(encryptedAccessKey, 'utf8');
  return decrypted;
};

const encryptAccessKeyIdAndSecretAccessKey = ({
  accessKeyId,
  secretAccessKey,
  publicKey,
}) => {
  const encryptedAccessKeyId = encryptAccessKey({
    accessKey: accessKeyId,
    publicKey,
  });
  const encryptedSecretAccessKey = encryptAccessKey({
    accessKey: secretAccessKey,
    publicKey,
  });
  return { encryptedAccessKeyId, encryptedSecretAccessKey };
};

module.exports = {
  generateKeyPairs,
  encryptAccessKey,
  decryptAccessKey,
  encryptAccessKeyIdAndSecretAccessKey,
};
