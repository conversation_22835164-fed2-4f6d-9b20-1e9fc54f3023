const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const dubbingController = require('../controllers/dubbing');
const { auth } = require('../middlewares/auth');
const { checkBlockedUser } = require('../middlewares/checkBlockedUser');
const {
  dubbingApiValidation,
  dubbingWithVideoApiValidation,
  createProjectValidation,
  updateProjectByIdValidation,
  deleteProjectsValidation,
} = require('../validations/dubbing');

router.post(
  '/dubbing/video',
  auth,
  checkBlockedUser,
  dubbingWithVideoApiValidation,
  asyncMiddleware(dubbingController.handleDubbingWithVideo),
);
router.post(
  '/dubbing',
  auth,
  checkBlockedUser,
  dubbingApiValidation,
  asyncMiddleware(dubbingController.handleDubbing),
);
router.get(
  '/dubbing/subtitle-characters',
  auth,
  asyncMiddleware(dubbingController.countSubtitleCharacters),
);
router.get(
  '/dubbing/languages',
  asyncMiddleware(dubbingController.getLanguages),
);
router.post(
  '/dubbing/projects',
  auth,
  createProjectValidation,
  asyncMiddleware(dubbingController.createProject),
);
router.get(
  '/dubbing/projects',
  auth,
  asyncMiddleware(dubbingController.getProjects),
);
router.delete(
  '/dubbing/projects',
  auth,
  deleteProjectsValidation,
  asyncMiddleware(dubbingController.deleteProjects),
);

router.put(
  '/dubbing/projects/:projectId',
  auth,
  updateProjectByIdValidation,
  asyncMiddleware(dubbingController.updateProject),
);
router.get(
  '/dubbing/projects/:projectId',
  auth,
  asyncMiddleware(dubbingController.getProject),
);

module.exports = router;
